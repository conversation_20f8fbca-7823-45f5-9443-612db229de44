package com.augmentcode.intellij.sentry

import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.utils.CustomPropertyReader
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.intellij.util.application
import io.sentry.Breadcrumb
import io.sentry.Sentry
import io.sentry.SentryLevel
import io.sentry.SentryOptions
import io.sentry.protocol.SentryException
import io.sentry.protocol.User

/**
 * Application-level service for initializing and managing Sentry error tracking.
 *
 * This service supports context-aware metadata collection with the following architecture:
 * - Each project gets its own SentryProjectMetadataService instance (managed as IntelliJ singletons)
 * - Metadata is collected continuously by project services but only reported when errors occur
 * - Metadata is included as contextual breadcrumbs when:
 *   - Exceptions are captured via captureException() (optionally with project context)
 *   - Error-level breadcrumbs are added via addBreadcrumb() (optionally with project context)
 * - When project context is provided, only that project's metadata is included
 * - When no project context is provided, metadata from all open projects are included
 * - This approach reduces unnecessary network traffic and Sentry quota usage
 * - Provides relevant context only when debugging errors
 * - Leverages IntelliJ's service lifecycle management (no manual registration required)
 */
@Service(Service.Level.APP)
class SentryService() : Disposable {
  private val logger = thisLogger()
  private val ideVersion = ApplicationInfo.getInstance().fullVersion
  private val ideName = ApplicationInfo.getInstance().fullApplicationName
  private val osName = System.getProperty("os.name")
  private val osVersion = System.getProperty("os.version")
  private val osArch = System.getProperty("os.arch")
  private val pluginVersion = currentPluginVersion()
  private val environment = if (pluginVersion?.contains("snapshot") == true) "development" else "production"
  private var pendingTenantId: String? = null
  private var pendingTenantName: String? = null

  /**
   * Tracks whether Sentry has been successfully initialized.
   */
  var isInitialized = false
    private set

  companion object {
    private const val SENTRY_DSN = "https://<EMAIL>/4509262642872320"
    private const val WEBVIEW_SENTRY_DSN =
      "https://<EMAIL>/4509294232207360"

    val instance: SentryService
      get() = service()
  }

  init {
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      registerListeners()
    }
  }

  internal fun registerListeners() {
    logger.info("Registering listeners for sentry service")
    AugmentAppStateService.instance.subscribe(
      application.messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          if (context.isSignedIn) {
            initialize(context)
          } else {
            reset()
          }
        }
      },
      triggerOnStateChange = true,
    )
  }

  /**
   * Execute a block of code with proper exception handling and logging.
   *
   * @param errorMessage The message to log if an exception occurs
   * @param block The code block to execute
   * @return The result of the block execution or null if an exception occurred
   */
  private fun <T> withExceptionHandling(
    errorMessage: String,
    block: () -> T,
  ): T? {
    return try {
      block()
    } catch (e: Exception) {
      logger.error(errorMessage, e)
      null
    }
  }

  /**
   * Creates a breadcrumb with the given parameters.
   *
   * @param message The breadcrumb message
   * @param category The breadcrumb category
   * @param level The breadcrumb level
   * @param type The breadcrumb type
   * @param data Additional data to add to the breadcrumb
   * @return The created breadcrumb
   */
  private fun createBreadcrumb(
    message: String,
    category: String = "default",
    level: SentryLevel = SentryLevel.INFO,
    type: String = "default",
    data: Map<String, String> = emptyMap(),
  ): Breadcrumb {
    return Breadcrumb().apply {
      this.message = message
      this.category = category
      this.level = level
      this.type = type

      // Add additional data
      data.forEach { (key, value) ->
        this.setData(key, value)
      }
    }
  }

  /**
   * Executes the given block only if Sentry is initialized.
   *
   * @param block The code block to execute if Sentry is initialized
   * @return The result of the block execution or null if Sentry is not initialized
   */
  private fun <T> ifInitialized(block: () -> T): T? {
    return if (isInitialized) {
      block()
    } else {
      logger.debug("Sentry is not initialized, cannot execute block")
      null
    }
  }

  /**
   * Determines whether Sentry should be enabled based on feature flags and environment.
   *
   * Sentry is enabled only when BOTH conditions are met:
   * 1. The `intellij_enable_sentry` feature flag is enabled (checked with network wait)
   * 2. The current environment is production (determined by plugin version not containing "snapshot")
   *
   * For development environments, developers can enable Sentry by:
   * - Using a production build of the plugin (version without "snapshot")
   * - OR configuring the `augmentcode.sentry.force.enabled` custom property to true
   * - OR configuring the `intellij_enable_sentry` feature flag to return true for development environments
   *   through the feature flag configuration system
   *
   * @return true if Sentry should be enabled, false otherwise
   */
  fun shouldEnableSentry(ctx: PluginContext): Boolean {
    if (CustomPropertyReader.readBoolean(CustomPropertyReader.FORCE_SENTRY_PROPERTY)) {
      return true
    }

    val isProductionEnvironment = environment == "production"

    if (!ctx.flags.sentryEnabled) {
      logger.debug("Sentry is disabled by feature flag (intellij_enable_sentry)")
      return false
    }

    if (!isProductionEnvironment) {
      logger.info("Sentry is disabled for development environment (plugin version contains 'snapshot')")
      return false
    }

    return true
  }

  /**
   * Initialize Sentry with the appropriate configuration.
   * This method is safe to call multiple times.
   */
  fun initialize(ctx: PluginContext) {
    if (!shouldEnableSentry(ctx)) {
      return
    }

    if (isInitialized) {
      logger.warn("Sentry is already initialized")
      return
    }

    withExceptionHandling("Failed to initialize Sentry") {
      // Configure and initialize Sentry
      configureSentry(ctx)

      // Install the global exception handler after successful Sentry initialization
      service<SentryGlobalExceptionHandler>().install()

      logger.info("Sentry initialized successfully")

      isInitialized = true
    }
  }

  /**
   * Configure Sentry with the appropriate options.
   * @ctx The plugin context containing feature flags
   */
  private fun configureSentry(ctx: PluginContext) {
    Sentry.init { options ->
      // Set the DSN
      options.dsn = SENTRY_DSN

      // Set environment (production for now, could be made configurable)
      options.environment = environment

      // Set release version
      options.release = pluginVersion ?: "unknown"

      // Enable automatic session tracking for crash-free sessions
      options.isEnableAutoSessionTracking = true

      // Set sample rate for performance monitoring using feature flag
      options.tracesSampleRate = ctx.flags.pluginTraceSamplingRate

      // Set sample rate for error reporting using feature flag
      options.sampleRate = ctx.flags.pluginErrorSamplingRate

      // Debug shows logs from sentry SDK itself
      options.isDebug = CustomPropertyReader.readBoolean(CustomPropertyReader.ENABLE_SENTRY_DEBUG)

      // Filter out unrelated exceptions
      options.beforeSend =
        SentryOptions.BeforeSendCallback { event, _ ->
          if (event.exceptions == null) {
            // Allow captureMessage calls through
            return@BeforeSendCallback event
          }
          if (event.exceptions?.any { isFromAugmentCode(it) } == true) {
            return@BeforeSendCallback event
          }
          null
        }

      // Add IntelliJ version as a tag
      options.setTag("intellij.version", ideVersion)
      options.setTag("intellij.name", ideName)
      options.setTag("os.name", osName)
      options.setTag("os.version", osVersion)
      options.setTag("os.arch", osArch)
      ctx.model?.tenantId?.takeIf { it.isNotEmpty() }?.let { tenantId ->
        options.setTag("tenant.id", tenantId)
      }
      ctx.model?.tenantName?.takeIf { it.isNotEmpty() }?.let { tenantName ->
        options.setTag("tenant.name", tenantName)
      }

      // Keeping this line below, in case we want to implement beforeSend in the future
      // options.beforeSend = SentryOptions.BeforeSendCallback { event, _ -> filterSensitiveData(event) }
      // Disable sending default PII
      options.isSendDefaultPii = false
    }

    val userId = ctx.model?.userId
    if (userId != null) {
      val user = User()
      user.id = userId
      Sentry.setUser(user)
    }
  }

  internal fun isFromAugmentCode(exception: SentryException): Boolean {
    if (exception.stacktrace == null) {
      return false
    }
    return exception.stacktrace?.frames?.any { it.module?.contains("com.augmentcode") == true } == true
  }

  /**
   * Creates a JSON string with Sentry configuration for the webview.
   * This configuration is injected into the webview and used by the Sentry initialization code.
   */
  fun createSentryConfigForWebview(): String {
    val ctx = AugmentAppStateService.instance.context
    val sentryEnabled = shouldEnableSentry(ctx)
    if (!sentryEnabled) {
      logger.info("Sentry is disabled for webview")
      return "{enabled: false}"
    }

    val webviewErrorSampleRate = ctx.flags.webviewErrorSamplingRate
    val webviewTraceSampleRate = ctx.flags.webviewTraceSamplingRate
    // Create the Sentry configuration JSON
    return (
      "{" +
        "enabled: true," +
        "dsn: '${WEBVIEW_SENTRY_DSN}'," +
        "release: 'augment-intellij-webview@$pluginVersion'," +
        "environment: '$environment'," +
        "errorSampleRate: $webviewErrorSampleRate," +
        "tracesSampleRate: $webviewTraceSampleRate," +
        "sendDefaultPii: false," +
        // if not production, enable debug mode, sentry is disabled in dev mode by default
        "debug: ${CustomPropertyReader.readBoolean(CustomPropertyReader.ENABLE_SENTRY_DEBUG)}," +
        (ctx.model?.userId?.let { "userId: '$it'," } ?: "") +
        "tags: {" +
        "  'intellij.version': '$ideVersion'," +
        "  'plugin.version': '$pluginVersion'" +
        (ctx.model?.tenantId?.takeIf { it.isNotEmpty() }?.let { ",'tenant.id': '$it'" } ?: "") +
        (ctx.model?.tenantName?.takeIf { it.isNotEmpty() }?.let { ",'tenant.name': '$it'" } ?: "") +
        "}" +
        "}"
    )
  }

  /**
   * Capture an exception and send it to Sentry.
   * Includes current metrics as contextual data.
   *
   * @param throwable The exception to capture
   * @param project Optional project context. If provided, only metrics from this project are included.
   *                If null, metrics from all open projects are included.
   */
  fun captureException(
    throwable: Throwable,
    project: Project? = null,
  ) {
    ifInitialized {
      includeCurrentMetricsAsContext(project)
      Sentry.captureException(throwable)
    }
  }

  fun captureMessage(
    message: String,
    level: SentryLevel = SentryLevel.INFO,
    project: Project? = null,
  ) {
    ifInitialized {
      includeCurrentMetricsAsContext(project)
      Sentry.captureMessage(message, level)
    }
  }

  /**
   * Add a breadcrumb to track user actions or application events.
   * Includes current metrics as context for ERROR level breadcrumbs.
   *
   * @param message The breadcrumb message
   * @param category The breadcrumb category
   * @param level The breadcrumb level
   * @param project Optional project context. If provided, only metrics from this project are included.
   *                If null, metrics from all open projects are included.
   */
  fun addBreadcrumb(
    message: String,
    category: String = "default",
    level: SentryLevel = SentryLevel.INFO,
    project: Project? = null,
  ) {
    ifInitialized {
      // Include current metrics as context for error-level breadcrumbs
      if (level == SentryLevel.ERROR) {
        includeCurrentMetricsAsContext(project)
      }

      val breadcrumb = createBreadcrumb(message, category, level)
      Sentry.addBreadcrumb(breadcrumb)
    }
  }

  /**
   * Add a metrics breadcrumb to Sentry.
   */
  fun addMetricsBreadcrumb(
    metricName: String,
    value: Number,
    tags: Map<String, String> = emptyMap(),
  ) {
    ifInitialized {
      // Create data map with metric information
      val data =
        mutableMapOf(
          "metric_name" to metricName,
          "metric_value" to value.toString(),
        )

      // Add tags with prefix
      tags.forEach { (key, tagValue) ->
        data["tag_$key"] = tagValue
      }

      val breadcrumb =
        createBreadcrumb(
          message = "Metric: $metricName = $value",
          category = "metrics",
          level = SentryLevel.INFO,
          data = data,
        )

      Sentry.addBreadcrumb(breadcrumb)
    }
  }

  /**
   * Include current metadata as contextual breadcrumbs.
   * This method is called when capturing exceptions or error-level breadcrumbs.
   *
   * Uses IntelliJ's service singleton pattern to get SentryProjectMetadataService instances
   * directly from projects without maintaining manual registrations.
   *
   * @param project Optional project context. If provided, only metadata from this project are included.
   *                If null, metadata from all open projects are included as fallback.
   */
  private fun includeCurrentMetricsAsContext(project: Project? = null) {
    ifInitialized {
      if (project != null) {
        // Include metrics only for the specified project
        includeMetricsForProject(project)
      } else {
        // Fallback: include metrics from all open projects
        ProjectManager.getInstance().openProjects.forEach { openProject ->
          includeMetricsForProject(openProject)
        }
      }
    }
  }

  /**
   * Include metrics for a specific project.
   *
   * @param project The project to include metrics for
   */
  private fun includeMetricsForProject(project: Project) {
    try {
      SentryProjectMetadataService.getInstance(project).latestMetrics?.let { metrics ->
        addMetricsAsContextualBreadcrumbs(project.locationHash, metrics)
      }
    } catch (e: Exception) {
      logger.warn("Failed to include metrics context for project: ${project.name}", e)
    }
  }

  /**
   * Add metrics for a specific project as contextual breadcrumbs.
   */
  private fun addMetricsAsContextualBreadcrumbs(
    projectKey: String,
    metrics: MetricsData,
  ) {
    val projectTags = metrics.systemTags + ("project_key" to projectKey)

    // Add memory metrics as context
    metrics.memoryMetrics?.let { memory ->
      addMetricsGroup(
        metrics =
          listOf(
            "heap_available_mb" to memory.heapAvailableMB,
            "heap_utilized_mb" to memory.heapUtilizedMB,
          ),
        tags = projectTags,
      )
    }

    // Add repository metrics as context
    metrics.repositoryMetrics?.let { repo ->
      addMetricsGroup(
        metrics =
          listOf(
            "git_tracked_files_count" to repo.gitTrackedFiles,
            "indexed_files_count" to repo.indexedFiles,
          ),
        tags = projectTags,
      )
    }

    // Add webview metrics as context
    metrics.webviewMetrics?.let { webview ->
      addMetricsGroup(
        metrics =
          listOf(
            "webview_state_num_chars" to webview.webviewChatStateSize,
            "settings_state_num_chars" to webview.webviewSettingsStateSize,
          ),
        tags = projectTags,
      )
    }
  }

  /**
   * Add a group of metrics as breadcrumbs.
   *
   * @param metrics List of metric name to value pairs
   * @param tags Tags to add to each metric
   */
  private fun addMetricsGroup(
    metrics: List<Pair<String, Number>>,
    tags: Map<String, String>,
  ) {
    metrics.forEach { (name, value) ->
      addMetricsBreadcrumb(name, value, tags)
    }
  }

  /**
   * Reset Sentry and uninstall the global exception handler.
   *
   * This is used when disposing of the service or the user signs in/out
   */
  private fun reset() {
    if (isInitialized) {
      // Only log if the service was initialized - this makes test logs quiet
      logger.info("Resetting SentryService")
    }
    withExceptionHandling("Failed to reset SentryService") {
      // Uninstall the global exception handler first
      service<SentryGlobalExceptionHandler>().uninstall()

      // Close Sentry
      Sentry.close()

      // Reset initialization flag
      isInitialized = false
    }
  }

  /**
   * Get the current version of the Augment plugin.
   */
  private fun currentPluginVersion(): String? {
    val plugin = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
    return plugin?.version
  }

  /**
   * Dispose Sentry when the service is being disposed.
   * This is called automatically by the IntelliJ Platform when the application shuts down
   * or the plugin is unloaded. This also uninstalls the global exception handler.
   * CoroutineScope is automatically cancelled by IntelliJ Platform when application shuts down.
   * Project-specific metrics services are automatically disposed by IntelliJ's service lifecycle.
   */
  override fun dispose() {
    reset()
  }
}
