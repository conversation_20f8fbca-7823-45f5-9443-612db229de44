package com.augmentcode.intellij.featureflags

import com.augmentcode.api.EloModelConfiguration
import com.augmentcode.api.additionalChatModelsMap
import com.augmentcode.api.modelInfoRegistryMap
import com.augmentcode.api.modelRegistryMap
import com.augmentcode.intellij.utils.SemVer
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.extensions.PluginId
import public_api.PublicApi.GetModelsResponse

data class ModelInfoRegistryEntry(
  val displayName: String,
  val shortName: String? = null,
  val description: String? = null,
  val disabled: Boolean? = null,
  val disabled_reason: String? = null,
)

data class FeatureFlags(
  val chatMultimodalEnabled: <PERSON>olean,
  val enableCompletionsHistory: <PERSON><PERSON>an,
  val agentModeEnabled: <PERSON><PERSON>an,
  val enableRules: <PERSON><PERSON>an,
  val promptEnhancerEnabled: <PERSON><PERSON><PERSON>,
  val enableChatMermaidDiagrams: <PERSON>olean,
  val enableDesignSystemRichTextEditor: Boolean,
  val smartPastePrecomputeMode: String,
  val enableSmartPaste: Boolean,
  val useNewThreadsMenu: Boolean,
  val enableNewThreadsList: Boolean,
  val enableExternalSourcesInChat: Boolean,
  val shareServiceEnabled: Boolean,
  val enableHomespunGitignore: Boolean,
  val memoriesParams: String,
  val userGuidelinesLengthLimit: Int,
  val workspaceGuidelinesLengthLimit: Int,
  val chatWithToolsEnabled: Boolean,
  val sentryEnabled: Boolean,
  val webviewErrorSamplingRate: Double,
  val pluginErrorSamplingRate: Double,
  val webviewTraceSamplingRate: Double,
  val pluginTraceSamplingRate: Double,
  val maxUploadSizeBytes: Int,
  val bypassLanguageFilter: Boolean,
  val additionalChatModels: Map<String, String>,
  val enableAgentAutoMode: Boolean,
  val preferenceCollectionAllowed: Boolean,
  val eloModelConfiguration: EloModelConfiguration,
  val enableEdtFreezeDetection: Boolean,
  val indexingV3Enabled: Boolean,
  val enableWebviewPerformanceMonitoring: Boolean,
  val taskListEnabled: Boolean,
  val enableExchangeStorage: Boolean,
  val enableToolUseStateStorage: Boolean,
  val useHistorySummary: Boolean,
  val historySummaryParams: String,
  val agentEditTool: String,
  val agentEditToolMinViewSize: Int,
  val agentEditToolSchemaType: String,
  val agentEditToolEnableFuzzyMatching: Boolean,
  val agentEditToolFuzzyMatchSuccessMessage: String,
  val agentEditToolFuzzyMatchMaxDiff: Int,
  val agentEditToolFuzzyMatchMaxDiffRatio: Double,
  val agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: Int,
  val agentEditToolInstructionsReminder: Boolean,
  val agentEditToolShowResultSnippet: Boolean,
  val agentEditToolMaxLines: Int,
  val agentSaveFileToolInstructionsReminder: Boolean,
  val grepSearchToolEnable: Boolean,
  val grepSearchToolTimelimitSec: Int,
  val grepSearchToolOutputCharsLimit: Int,
  val grepSearchToolNumContextLines: Int,
  val retryChatStreamTimeouts: Boolean,
  val enableAgentTabs: Boolean,
  val enableAgentGitTracker: Boolean,
  val maxTrackableFileCount: Int?,
  val maxTrackableFileCountWithoutPermission: Int?,
  val enableMemoryRetrieval: Boolean,
  val enableModelRegistry: Boolean,
  val modelRegistry: Map<String, String>,
  val modelInfoRegistry: Map<String, ModelInfoRegistryEntry>,
  val agentChatModel: String?,
  val agentViewToolParams: String,
  val enableLucideIcons: Boolean,
  val enableEnhancedDehydrationMode: Boolean,
  val enableNotificationsService: Boolean,
  val notificationPollingIntervalMs: Long,
  val showThinkingSummary: Boolean,
  val enableFileIntakeService: Boolean,
  val enableSegmentAnalyticsReporting: Boolean,
  val enableParallelTools: Boolean,
  val enableIndexFilterForRootDiscovery: Boolean,
) {
  companion object {
    // Create a FeatureFlags consisting of default values with overrides from the GetModelsResponse flags
    fun fromGetModelsResponse(modelConfig: GetModelsResponse): FeatureFlags {
      val flagsFromAPI = modelConfig.featureFlags
      return DefaultFeatureFlags.copyWithOverrides(flagsFromAPI)
    }

    // Return true if the current version is at least the min version. False if either are undefined.
    internal fun isMinVersionAtLeast(
      currentVersionStr: String?,
      minVersionStr: String?,
    ): Boolean {
      if (minVersionStr.isNullOrBlank() || currentVersionStr.isNullOrBlank()) {
        return false
      }

      try {
        val minVersion = SemVer(minVersionStr)
        val currentVersion = SemVer(currentVersionStr)
        return currentVersion >= minVersion
      } catch (e: IllegalArgumentException) {
        thisLogger().warn("Failed to parse version: $currentVersionStr or $minVersionStr", e)
        return false
      }
    }
  }

  // Create a copy of the FeatureFlags object with values overridden if defined in the APIFeatureFlags
  // object if defined
  private fun copyWithOverrides(flagsFromAPI: GetModelsResponse.FeatureFlags?): FeatureFlags {
    val pluginVersionStr = getCurrentPluginVersion()

    // List deprecated fields explicitly, so it's easier to see
    @Suppress("DEPRECATION")
    val enableExternalSourcesInChat =
      if (flagsFromAPI?.hasEnableExternalSourcesInChat() == true) {
        flagsFromAPI.enableExternalSourcesInChat
      } else {
        enableExternalSourcesInChat
      }

    return FeatureFlags(
      chatMultimodalEnabled =
        if (flagsFromAPI?.hasIntellijChatMultimodalMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijChatMultimodalMinVersion)
        } else {
          chatMultimodalEnabled
        },
      enableCompletionsHistory =
        if (flagsFromAPI?.hasIntellijCompletionsHistoryMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijCompletionsHistoryMinVersion)
        } else {
          enableCompletionsHistory
        },
      agentModeEnabled =
        if (flagsFromAPI?.hasIntellijAgentModeMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijAgentModeMinVersion)
        } else {
          agentModeEnabled
        },
      enableRules =
        if (flagsFromAPI?.hasEnableRules() == true) {
          flagsFromAPI.enableRules
        } else {
          enableRules
        },
      promptEnhancerEnabled =
        if (flagsFromAPI?.hasIntellijPromptEnhancerEnabled() == true) {
          flagsFromAPI.intellijPromptEnhancerEnabled
        } else {
          promptEnhancerEnabled
        },
      enableChatMermaidDiagrams =
        if (flagsFromAPI?.hasIntellijEnableChatMermaidDiagramsMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijEnableChatMermaidDiagramsMinVersion)
        } else {
          enableChatMermaidDiagrams
        },
      enableDesignSystemRichTextEditor =
        if (flagsFromAPI?.hasIntellijDesignSystemRichTextEditorMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijDesignSystemRichTextEditorMinVersion)
        } else {
          enableDesignSystemRichTextEditor
        },
      smartPastePrecomputeMode =
        if (flagsFromAPI?.hasSmartPastePrecomputeMode() == true) {
          flagsFromAPI.smartPastePrecomputeMode
        } else {
          smartPastePrecomputeMode
        },
      enableSmartPaste =
        if (flagsFromAPI?.hasIntellijSmartPasteMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijSmartPasteMinVersion)
        } else {
          enableSmartPaste
        },
      useNewThreadsMenu =
        if (flagsFromAPI?.hasIntellijNewThreadsMenuMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijNewThreadsMenuMinVersion)
        } else {
          useNewThreadsMenu
        },
      enableNewThreadsList =
        if (flagsFromAPI?.hasEnableNewThreadsList() == true) {
          flagsFromAPI.enableNewThreadsList
        } else {
          enableNewThreadsList
        },
      enableExternalSourcesInChat = enableExternalSourcesInChat,
      shareServiceEnabled =
        if (flagsFromAPI?.hasIntellijShareMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijShareMinVersion)
        } else {
          shareServiceEnabled
        },
      enableHomespunGitignore =
        if (flagsFromAPI?.hasIntellijEnableHomespunGitignore() == true) {
          flagsFromAPI.intellijEnableHomespunGitignore
        } else {
          enableHomespunGitignore
        },
      memoriesParams =
        if (flagsFromAPI?.hasMemoriesParams() == true) {
          flagsFromAPI.memoriesParams
        } else {
          memoriesParams
        },
      userGuidelinesLengthLimit =
        if (flagsFromAPI?.hasUserGuidelinesLengthLimit() == true) {
          flagsFromAPI.userGuidelinesLengthLimit.toInt()
        } else {
          userGuidelinesLengthLimit
        },
      workspaceGuidelinesLengthLimit =
        if (flagsFromAPI?.hasWorkspaceGuidelinesLengthLimit() == true) {
          flagsFromAPI.workspaceGuidelinesLengthLimit.toInt()
        } else {
          workspaceGuidelinesLengthLimit
        },
      chatWithToolsEnabled =
        if (flagsFromAPI?.hasIntellijChatWithToolsMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijChatWithToolsMinVersion)
        } else {
          chatWithToolsEnabled
        },
      sentryEnabled =
        if (flagsFromAPI?.hasIntellijEnableSentry() == true) {
          flagsFromAPI.intellijEnableSentry
        } else {
          sentryEnabled
        },
      webviewErrorSamplingRate =
        if (flagsFromAPI?.hasIntellijWebviewErrorSamplingRate() == true) {
          flagsFromAPI.intellijWebviewErrorSamplingRate
        } else {
          webviewErrorSamplingRate
        },
      pluginErrorSamplingRate =
        if (flagsFromAPI?.hasIntellijPluginErrorSamplingRate() == true) {
          flagsFromAPI.intellijPluginErrorSamplingRate
        } else {
          pluginErrorSamplingRate
        },
      webviewTraceSamplingRate =
        if (flagsFromAPI?.hasIntellijWebviewTraceSamplingRate() == true) {
          flagsFromAPI.intellijWebviewTraceSamplingRate
        } else {
          webviewTraceSamplingRate
        },
      pluginTraceSamplingRate =
        if (flagsFromAPI?.hasIntellijPluginTraceSamplingRate() == true) {
          flagsFromAPI.intellijPluginTraceSamplingRate
        } else {
          pluginTraceSamplingRate
        },
      maxUploadSizeBytes =
        if (flagsFromAPI?.hasMaxUploadSizeBytes() == true) {
          flagsFromAPI.maxUploadSizeBytes.toInt()
        } else {
          maxUploadSizeBytes
        },
      bypassLanguageFilter =
        if (flagsFromAPI?.hasBypassLanguageFilter() == true) {
          flagsFromAPI.bypassLanguageFilter
        } else {
          bypassLanguageFilter
        },
      additionalChatModels =
        if (flagsFromAPI?.hasAdditionalChatModels() == true) {
          flagsFromAPI.additionalChatModelsMap()
        } else {
          additionalChatModels
        },
      enableAgentAutoMode =
        if (flagsFromAPI?.hasEnableAgentAutoMode() == true) {
          flagsFromAPI.enableAgentAutoMode
        } else {
          enableAgentAutoMode
        },
      preferenceCollectionAllowed =
        if (flagsFromAPI?.hasIntellijPreferenceCollectionAllowedMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijPreferenceCollectionAllowedMinVersion)
        } else {
          preferenceCollectionAllowed
        },
      eloModelConfiguration =
        if (flagsFromAPI?.hasEloModelConfiguration() == true) {
          parseEloModelConfiguration(flagsFromAPI.eloModelConfiguration) ?: eloModelConfiguration
        } else {
          eloModelConfiguration
        },
      enableEdtFreezeDetection =
        if (flagsFromAPI?.hasIntellijEdtFreezeDetectionEnabled() == true) {
          flagsFromAPI.intellijEdtFreezeDetectionEnabled
        } else {
          enableEdtFreezeDetection
        },
      indexingV3Enabled =
        if (flagsFromAPI?.hasIntellijIndexingV3Enabled() == true) {
          flagsFromAPI.intellijIndexingV3Enabled
        } else {
          indexingV3Enabled
        },
      enableWebviewPerformanceMonitoring =
        if (flagsFromAPI?.hasIntellijEnableWebviewPerformanceMonitoring() == true) {
          flagsFromAPI.intellijEnableWebviewPerformanceMonitoring
        } else {
          enableWebviewPerformanceMonitoring
        },
      taskListEnabled =
        if (flagsFromAPI?.hasIntellijTaskListMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijTaskListMinVersion)
        } else {
          taskListEnabled
        },
      enableExchangeStorage =
        if (flagsFromAPI?.hasEnableExchangeStorage() == true) {
          flagsFromAPI.enableExchangeStorage
        } else {
          enableExchangeStorage
        },
      enableToolUseStateStorage =
        if (flagsFromAPI?.hasEnableToolUseStateStorage() == true) {
          flagsFromAPI.enableToolUseStateStorage
        } else {
          enableToolUseStateStorage
        },
      useHistorySummary =
        if (flagsFromAPI?.hasHistorySummaryMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.historySummaryMinVersion)
        } else {
          useHistorySummary
        },
      historySummaryParams =
        if (flagsFromAPI?.hasHistorySummaryParams() == true) {
          flagsFromAPI.historySummaryParams
        } else {
          historySummaryParams
        },
      agentEditTool =
        if (flagsFromAPI?.hasVscodeAgentEditTool() == true) {
          flagsFromAPI.vscodeAgentEditTool
        } else {
          agentEditTool
        },
      agentEditToolMinViewSize =
        if (flagsFromAPI?.hasAgentEditToolMinViewSize() == true) {
          flagsFromAPI.agentEditToolMinViewSize.toInt()
        } else {
          agentEditToolMinViewSize
        },
      agentEditToolSchemaType =
        if (flagsFromAPI?.hasAgentEditToolSchemaType() == true) {
          flagsFromAPI.agentEditToolSchemaType
        } else {
          agentEditToolSchemaType
        },
      agentEditToolEnableFuzzyMatching =
        if (flagsFromAPI?.hasAgentEditToolEnableFuzzyMatching() == true) {
          flagsFromAPI.agentEditToolEnableFuzzyMatching
        } else {
          agentEditToolEnableFuzzyMatching
        },
      agentEditToolFuzzyMatchSuccessMessage =
        if (flagsFromAPI?.hasAgentEditToolFuzzyMatchSuccessMessage() == true) {
          flagsFromAPI.agentEditToolFuzzyMatchSuccessMessage
        } else {
          agentEditToolFuzzyMatchSuccessMessage
        },
      agentEditToolFuzzyMatchMaxDiff =
        if (flagsFromAPI?.hasAgentEditToolFuzzyMatchMaxDiff() == true) {
          flagsFromAPI.agentEditToolFuzzyMatchMaxDiff.toInt()
        } else {
          agentEditToolFuzzyMatchMaxDiff
        },
      agentEditToolFuzzyMatchMaxDiffRatio =
        if (flagsFromAPI?.hasAgentEditToolFuzzyMatchMaxDiffRatio() == true) {
          flagsFromAPI.agentEditToolFuzzyMatchMaxDiffRatio
        } else {
          agentEditToolFuzzyMatchMaxDiffRatio
        },
      agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs =
        if (flagsFromAPI?.hasAgentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs() == true) {
          flagsFromAPI.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs.toInt()
        } else {
          agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs
        },
      agentEditToolInstructionsReminder =
        if (flagsFromAPI?.hasAgentEditToolInstructionsReminder() == true) {
          flagsFromAPI.agentEditToolInstructionsReminder
        } else {
          agentEditToolInstructionsReminder
        },
      agentEditToolShowResultSnippet =
        if (flagsFromAPI?.hasAgentEditToolShowResultSnippet() == true) {
          flagsFromAPI.agentEditToolShowResultSnippet
        } else {
          agentEditToolShowResultSnippet
        },
      agentEditToolMaxLines =
        if (flagsFromAPI?.hasAgentEditToolMaxLines() == true) {
          flagsFromAPI.agentEditToolMaxLines.toInt()
        } else {
          agentEditToolMaxLines
        },
      agentSaveFileToolInstructionsReminder =
        if (flagsFromAPI?.hasAgentSaveFileToolInstructionsReminder() == true) {
          flagsFromAPI.agentSaveFileToolInstructionsReminder
        } else {
          agentSaveFileToolInstructionsReminder
        },
      grepSearchToolEnable =
        if (flagsFromAPI?.hasGrepSearchToolEnable() == true) {
          flagsFromAPI.grepSearchToolEnable
        } else {
          grepSearchToolEnable
        },
      grepSearchToolTimelimitSec =
        if (flagsFromAPI?.hasGrepSearchToolTimelimitSec() == true) {
          flagsFromAPI.grepSearchToolTimelimitSec.toInt()
        } else {
          grepSearchToolTimelimitSec
        },
      grepSearchToolOutputCharsLimit =
        if (flagsFromAPI?.hasGrepSearchToolOutputCharsLimit() == true) {
          flagsFromAPI.grepSearchToolOutputCharsLimit.toInt()
        } else {
          grepSearchToolOutputCharsLimit
        },
      grepSearchToolNumContextLines =
        if (flagsFromAPI?.hasGrepSearchToolNumContextLines() == true) {
          flagsFromAPI.grepSearchToolNumContextLines.toInt()
        } else {
          grepSearchToolNumContextLines
        },
      retryChatStreamTimeouts =
        if (flagsFromAPI?.hasRetryChatStreamTimeouts() == true) {
          flagsFromAPI.retryChatStreamTimeouts
        } else {
          retryChatStreamTimeouts
        },
      enableAgentTabs =
        if (flagsFromAPI?.hasEnableAgentTabs() == true) {
          flagsFromAPI.enableAgentTabs
        } else {
          enableAgentTabs
        },
      enableAgentGitTracker =
        if (flagsFromAPI?.hasEnableAgentGitTracker() == true) {
          flagsFromAPI.enableAgentGitTracker
        } else {
          enableAgentGitTracker
        },
      maxTrackableFileCount =
        if (flagsFromAPI?.hasMaxTrackableFileCount() == true) {
          flagsFromAPI.maxTrackableFileCount.toInt()
        } else {
          maxTrackableFileCount
        },
      maxTrackableFileCountWithoutPermission =
        if (flagsFromAPI?.hasMaxTrackableFileCountWithoutPermission() == true) {
          flagsFromAPI.maxTrackableFileCountWithoutPermission.toInt()
        } else {
          maxTrackableFileCountWithoutPermission
        },
      enableMemoryRetrieval =
        if (flagsFromAPI?.hasEnableMemoryRetrieval() == true) {
          flagsFromAPI.enableMemoryRetrieval
        } else {
          enableMemoryRetrieval
        },
      enableModelRegistry =
        if (flagsFromAPI?.hasEnableModelRegistry() == true) {
          flagsFromAPI.enableModelRegistry
        } else {
          enableModelRegistry
        },
      modelRegistry =
        if (flagsFromAPI?.hasModelRegistry() == true) {
          flagsFromAPI.modelRegistryMap()
        } else {
          modelRegistry
        },
      modelInfoRegistry =
        if (flagsFromAPI?.hasModelInfoRegistry() == true) {
          flagsFromAPI.modelInfoRegistryMap()
        } else {
          modelInfoRegistry
        },
      agentChatModel =
        if (flagsFromAPI?.hasAgentChatModel() == true) {
          flagsFromAPI.agentChatModel
        } else {
          agentChatModel
        },
      agentViewToolParams =
        if (flagsFromAPI?.hasAgentViewToolParams() == true) {
          flagsFromAPI.agentViewToolParams
        } else {
          agentViewToolParams
        },
      enableLucideIcons =
        if (flagsFromAPI?.hasEnableLucideIcons() == true) {
          flagsFromAPI.enableLucideIcons
        } else {
          enableLucideIcons
        },
      enableEnhancedDehydrationMode =
        if (flagsFromAPI?.hasEnableEnhancedDehydrationMode() == true) {
          flagsFromAPI.enableEnhancedDehydrationMode
        } else {
          enableEnhancedDehydrationMode
        },
      enableNotificationsService =
        if (flagsFromAPI?.hasEnableNotificationsServiceIntellij() == true) {
          flagsFromAPI.enableNotificationsServiceIntellij
        } else {
          enableNotificationsService
        },
      notificationPollingIntervalMs =
        if (flagsFromAPI?.hasNotificationPollingIntervalMs() == true) {
          flagsFromAPI.notificationPollingIntervalMs
        } else {
          notificationPollingIntervalMs
        },
      showThinkingSummary =
        if (flagsFromAPI?.hasIntellijShowThinkingSummaryMinVersion() == true) {
          isMinVersionAtLeast(pluginVersionStr, flagsFromAPI.intellijShowThinkingSummaryMinVersion)
        } else {
          showThinkingSummary
        },
      enableFileIntakeService =
        if (flagsFromAPI?.hasIntellijEnableFileIntakeService() == true) {
          flagsFromAPI.intellijEnableFileIntakeService
        } else {
          enableFileIntakeService
        },
      enableSegmentAnalyticsReporting =
        if (flagsFromAPI?.hasIntellijEnableSegmentAnalyticsReporting() == true) {
          flagsFromAPI.intellijEnableSegmentAnalyticsReporting
        } else {
          enableSegmentAnalyticsReporting
        },
      enableParallelTools =
        if (flagsFromAPI?.hasEnableParallelTools() == true) {
          flagsFromAPI.enableParallelTools
        } else {
          enableParallelTools
        },
      enableIndexFilterForRootDiscovery =
        if (flagsFromAPI?.hasIntellijEnableIndexFilterForRootDiscovery() == true) {
          flagsFromAPI.intellijEnableIndexFilterForRootDiscovery
        } else {
          enableIndexFilterForRootDiscovery
        },
    )
  }

  private fun parseEloModelConfiguration(eloModelConfiguration: String?): EloModelConfiguration? {
    if (eloModelConfiguration.isNullOrBlank()) {
      return null
    }
    try {
      return Gson().fromJson(eloModelConfiguration, EloModelConfiguration::class.java)
    } catch (e: JsonSyntaxException) {
      thisLogger().warn("Failed to parse ELO model configuration: $eloModelConfiguration", e)
      return null
    }
  }

  // Get the plugins current version
  private fun getCurrentPluginVersion(): String? {
    val plugin = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
    if (plugin == null) {
      thisLogger().warn("Failed to lookup plugin version, failed to get augment plugin details")
      return null
    }
    return plugin.version
  }
}
