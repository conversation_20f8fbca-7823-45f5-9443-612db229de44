<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import CardButton from "$common-webviews/src/apps/chat/components/buttons/CardButton/CardButton.svelte";
  import BranchIcon from "$common-webviews/src/design-system/icons/branch.svelte";
  import ChevronRight from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-right.svg?component";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  const branches = [
    { name: "main", commit: "abc123" },
    { name: "develop", commit: "def456" },
    { name: "feature/new-ui", commit: "ghi789" },
    { name: "bugfix/issue-123", commit: "jkl012" },
  ];

  const files = [
    { path: "/src/index.js", size: 1024 },
    { path: "/src/components/App.js", size: 2048 },
    { path: "/src/utils/helpers.js", size: 512 },
  ];

  const formatBranch = (branch: { name: string }) => branch?.name || "";
  const formatFile = (file: { path: string }) => file?.path || "";

  let selectedBranch = branches[0];
  let selectedFile = null;
</script>

<ColumnLayout>
  <Fieldset title="Button Type">
    <CardButton type="button" title="Simple Button" onClick={() => console.log("Clicked!")}>
      <Icon slot="iconLeft" name="file-text" />
    </CardButton>

    <CardButton
      type="button"
      title="Button with Subtitle"
      subtitle="Click me!"
      onClick={() => console.log("Clicked!")}
    >
      <Icon slot="iconLeft" name="file-text" />
      <GuardedIcon slot="iconRight" name="chevron-right"><ChevronRight /></GuardedIcon>
    </CardButton>
  </Fieldset>

  <Fieldset title="Dropdown Type">
    <CardButton
      type="dropdown"
      title="Select Branch"
      items={branches}
      selectedItem={selectedBranch}
      formatItemLabel={formatBranch}
    >
      <GuardedIcon slot="iconLeft" name="git-branch"><BranchIcon /></GuardedIcon>
    </CardButton>

    <CardButton
      type="dropdown"
      title="Select File"
      items={files}
      selectedItem={selectedFile}
      formatItemLabel={formatFile}
      noItemsLabel="No files available"
    >
      <Icon slot="iconLeft" name="file-text" />
    </CardButton>
  </Fieldset>

  <Fieldset title="Custom Dropdown Content">
    <CardButton type="dropdown" title="Custom Dropdown">
      <Icon slot="iconLeft" name="folder-open" />
      <div slot="dropdown-content" style="padding: 8px;">
        <p>This is a custom dropdown content.</p>
        <p>You can put anything here!</p>
      </div>
    </CardButton>
  </Fieldset>
</ColumnLayout>
