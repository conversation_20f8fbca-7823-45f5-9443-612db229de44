<script lang="ts">
  import TurnSummary from "$common-webviews/src/apps/chat/components/turn-summary/TurnSummary.svelte";
  import type { SummaryEntry } from "$common-webviews/src/apps/chat/components/turn-summary/types";

  // Accept the same props shape as the main story component to satisfy Storybook types
  export let entries: SummaryEntry[] | undefined = undefined;
  export let showFooter: boolean | undefined = undefined;

  let key = 0; // Force re-render to replay animations

  function replayAnimation() {
    key++;
  }

  // Sample data to demonstrate the animation
  const demoEntries: SummaryEntry[] = [
    {
      singularLabel: "file changed",
      pluralLabel: "files changed",
      value: 12,
      icon: "file-diff",
      callback: () => alert("Files clicked!"),
    },
    { singularLabel: "line added", pluralLabel: "lines added", value: 245, icon: "square-plus" },
    {
      singularLabel: "line removed",
      pluralLabel: "lines removed",
      value: 89,
      icon: "square-minus",
    },
    {
      singularLabel: "file examined",
      pluralLabel: "files examined",
      value: 34,
      icon: "file-search-2",
    },
    {
      singularLabel: "memory retrieved",
      pluralLabel: "memories retrieved",
      value: 8,
      icon: "archive",
    },
    { singularLabel: "tool used", pluralLabel: "tools used", value: 15, icon: "pencil-ruler" },
  ];

  $: displayedEntries = entries ?? demoEntries;
</script>

<div style="padding: 20px; max-width: 800px;">
  <h2>TurnSummaryAlt Reactive Animation Orchestrator Demo</h2>

  <div
    style="margin: 20px 0; padding: 15px; background: var(--ds-color-neutral-a2); border-radius: 8px;"
  >
    <h3>TurnSummaryAlt Animation Features:</h3>
    <ul style="margin: 10px 0; padding-left: 20px;">
      <li>
        <strong>Alternative design:</strong> Exploring different visual approach with same data
      </li>
      <li>
        <strong>Single intersection observer:</strong> One observer on the container, not individual
        items
      </li>
      <li>
        <strong>Declarative coordination:</strong> Uses Svelte stores to orchestrate animations
      </li>
      <li><strong>Staggered fade-in:</strong> Each item appears with a 250ms delay</li>
      <li><strong>Simultaneous animations:</strong> Number animations start as items fade in</li>
      <li><strong>Footer animation:</strong> Footer slides in after all items complete</li>
    </ul>
  </div>

  <div style="margin: 20px 0;">
    <button on:click={replayAnimation} style="padding: 8px 16px; margin-bottom: 20px;">
      🔄 Replay Animation
    </button>
  </div>

  <div style="border: 1px solid var(--ds-color-neutral-a6); border-radius: 8px; padding: 20px;">
    {#key key}
      <TurnSummary entries={displayedEntries}>
        <svelte:fragment slot="footer">
          {#if showFooter}
            <div>Demo footer content</div>
          {/if}
        </svelte:fragment>
      </TurnSummary>
    {/key}
  </div>

  <div
    style="margin-top: 30px; padding: 15px; background: var(--ds-color-neutral-a2); border-radius: 8px;"
  >
    <h3>Technical Implementation:</h3>
    <ul style="margin: 10px 0; padding-left: 20px;">
      <li>
        <strong>Reactive Stores:</strong> Uses <code>writable</code> and <code>derived</code> stores
      </li>
      <li>
        <strong>Animation Pipeline:</strong>
        <code>isVisible → shouldStartNumbers → shouldShowFooter</code>
      </li>
      <li><strong>Declarative CSS:</strong> <code>class:animate</code> applied when visible</li>
      <li>
        <strong>Coordinated Timing:</strong> Footer animation triggered after all items complete
      </li>
      <li><strong>Single Observer:</strong> One intersection observer on the container</li>
    </ul>
  </div>

  <div
    style="margin-top: 20px; padding: 15px; background: var(--ds-color-accent-a2); border-radius: 8px;"
  >
    <h3>🎯 Animation Sequence:</h3>
    <ol style="margin: 10px 0; padding-left: 20px;">
      <li><strong>Container comes into view</strong> → <code>isVisible = true</code></li>
      <li><strong>CSS fade-in starts</strong> → <code>class:animate</code> applied</li>
      <li>
        <strong>Number animations start simultaneously</strong> → <code>autoStart</code> becomes true
      </li>
      <li><strong>Items fade in staggered</strong> → 250ms delays between items</li>
      <li><strong>Numbers animate while fading in</strong> → Smooth, coordinated effect</li>
      <li><strong>Footer slides in</strong> → After all items complete their animations</li>
    </ol>
  </div>

  <div
    style="margin-top: 20px; padding: 15px; background: var(--ds-color-success-a2); border-radius: 8px;"
  >
    <h3>✅ Benefits of This Alternative Design:</h3>
    <ul style="margin: 10px 0; padding-left: 20px;">
      <li><strong>Design Exploration:</strong> Test different visual approaches with same data</li>
      <li><strong>Performance:</strong> Single intersection observer vs multiple</li>
      <li><strong>Maintainability:</strong> All animation logic in one place</li>
      <li><strong>Declarative:</strong> State flows naturally through reactive system</li>
      <li><strong>Testable:</strong> Easy to mock stores and test animation states</li>
      <li><strong>Flexible:</strong> Easy to add new animation phases</li>
    </ul>
  </div>

  <div
    style="margin-top: 20px; padding: 15px; background: var(--ds-color-warning-a2); border-radius: 8px;"
  >
    <h3>🔄 Comparing with Original TurnSummary:</h3>
    <ul style="margin: 10px 0; padding-left: 20px;">
      <li><strong>Same Data Structure:</strong> Uses identical SummaryEntry interface</li>
      <li><strong>Same Animation System:</strong> Reactive stores and intersection observer</li>
      <li><strong>Different Timing:</strong> 250ms vs 400ms delays for different feel</li>
      <li><strong>Alternative Layout:</strong> Explore different visual presentation</li>
      <li><strong>Iterative Design:</strong> Easy to compare and refine approaches</li>
    </ul>
  </div>
</div>
