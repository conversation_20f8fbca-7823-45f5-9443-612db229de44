/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./TurnSummaryStory.svelte";
import ReactiveDemo from "./TurnSummaryReactiveDemo.svelte";

const meta = {
  title: "app/Chat/components/turn-summary/TurnSummary",
  component,
  tags: ["autodocs"],
  argTypes: {
    entries: {
      control: { type: "object" },
      description: "Array of summary entries to display",
    },
    showFooter: {
      control: { type: "boolean" },
      description: "Whether to show the footer content",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default story with typical values
export const Default: Story = {
  args: {
    entries: [
      {
        singularLabel: "file changed",
        pluralLabel: "files changed",
        value: 3,
        icon: "file-diff",
        callback: () => alert("Files changed clicked"),
      },
      {
        singularLabel: "file examined",
        pluralLabel: "files examined",
        value: 7,
        icon: "file-search-2",
      },
      {
        singularLabel: "memory retrieved",
        pluralLabel: "memories retrieved",
        value: 2,
        icon: "archive",
      },
      { singularLabel: "tool used", pluralLabel: "tools used", value: 5, icon: "pencil-ruler" },
    ],
    showFooter: false,
  },
};

// Story with zero values (should show "No activity to report")
export const Empty: Story = {
  args: {
    entries: [
      { singularLabel: "file changed", pluralLabel: "files changed", value: 0, icon: "file-diff" },
      { singularLabel: "line added", pluralLabel: "lines added", value: 0, icon: "square-plus" },
      {
        singularLabel: "line removed",
        pluralLabel: "lines removed",
        value: 0,
        icon: "square-minus",
      },
      {
        singularLabel: "file examined",
        pluralLabel: "files examined",
        value: 0,
        icon: "file-search-2",
      },
      {
        singularLabel: "memory retrieved",
        pluralLabel: "memories retrieved",
        value: 0,
        icon: "archive",
      },
      { singularLabel: "tool used", pluralLabel: "tools used", value: 0, icon: "pencil-ruler" },
    ],
    showFooter: false,
  },
};

// Story with high values to test layout
export const FewItems: Story = {
  args: {
    entries: [
      {
        singularLabel: "file changed",
        pluralLabel: "files changed",
        value: 127,
        icon: "file-diff",
      },
      { singularLabel: "line added", pluralLabel: "lines added", value: 1543, icon: "square-plus" },
    ],
    showFooter: false,
  },
};

// Story with high values to test layout
export const HighValues: Story = {
  args: {
    entries: [
      {
        singularLabel: "file changed",
        pluralLabel: "files changed",
        value: 127,
        icon: "file-diff",
      },
      { singularLabel: "line added", pluralLabel: "lines added", value: 1543, icon: "square-plus" },
      {
        singularLabel: "line removed",
        pluralLabel: "lines removed",
        value: 892,
        icon: "square-minus",
      },
      {
        singularLabel: "file examined",
        pluralLabel: "files examined",
        value: 45,
        icon: "file-search-2",
      },
      {
        singularLabel: "memory retrieved",
        pluralLabel: "memories retrieved",
        value: 23,
        icon: "archive",
      },
      { singularLabel: "tool used", pluralLabel: "tools used", value: 18, icon: "pencil-ruler" },
    ],
    showFooter: false,
  },
};

// Story with mixed values - some zero, some non-zero (zeros will be filtered out)
export const Mixed: Story = {
  args: {
    entries: [
      { singularLabel: "file changed", pluralLabel: "files changed", value: 5, icon: "file-diff" },
      { singularLabel: "line added", pluralLabel: "lines added", value: 0, icon: "square-plus" },
      {
        singularLabel: "line removed",
        pluralLabel: "lines removed",
        value: 12,
        icon: "square-minus",
      },
      {
        singularLabel: "file examined",
        pluralLabel: "files examined",
        value: 0,
        icon: "file-search-2",
      },
      {
        singularLabel: "memory retrieved",
        pluralLabel: "memories retrieved",
        value: 1,
        icon: "archive",
      },
      { singularLabel: "tool used", pluralLabel: "tools used", value: 0, icon: "pencil-ruler" },
    ],
    showFooter: false,
  },
};

// Story demonstrating the footer slot with checkpoint-style actions
export const WithFooter: Story = {
  args: {
    entries: [
      { singularLabel: "file changed", pluralLabel: "files changed", value: 8, icon: "file-diff" },
      { singularLabel: "line added", pluralLabel: "lines added", value: 156, icon: "square-plus" },
      {
        singularLabel: "line removed",
        pluralLabel: "lines removed",
        value: 43,
        icon: "square-minus",
      },
      {
        singularLabel: "file examined",
        pluralLabel: "files examined",
        value: 12,
        icon: "file-search-2",
      },
      {
        singularLabel: "memory retrieved",
        pluralLabel: "memories retrieved",
        value: 3,
        icon: "archive",
      },
      { singularLabel: "tool used", pluralLabel: "tools used", value: 7, icon: "pencil-ruler" },
    ],
    showFooter: true,
  },
};

// Story with single column layout (fewer items)
export const SingleColumn: Story = {
  args: {
    entries: [
      { singularLabel: "file changed", pluralLabel: "files changed", value: 2, icon: "file-diff" },
      { singularLabel: "line added", pluralLabel: "lines added", value: 15, icon: "square-plus" },
      { singularLabel: "tool used", pluralLabel: "tools used", value: 3, icon: "pencil-ruler" },
    ],
    showFooter: false,
  },
};

// Story with odd number of items to test grid layout
export const OddItems: Story = {
  args: {
    entries: [
      { singularLabel: "file changed", pluralLabel: "files changed", value: 4, icon: "file-diff" },
      { singularLabel: "line added", pluralLabel: "lines added", value: 67, icon: "square-plus" },
      {
        singularLabel: "line removed",
        pluralLabel: "lines removed",
        value: 23,
        icon: "square-minus",
      },
      {
        singularLabel: "file examined",
        pluralLabel: "files examined",
        value: 9,
        icon: "file-search-2",
      },
      {
        singularLabel: "memory retrieved",
        pluralLabel: "memories retrieved",
        value: 1,
        icon: "archive",
      },
    ],
    showFooter: false,
  },
};

// Reactive animation demo story
export const ReactiveAnimationDemo: Story = {
  render: () => ({
    Component: ReactiveDemo,
  }),
  parameters: {
    docs: {
      description: {
        story:
          "Interactive demo showing the reactive animation orchestration system with replay functionality.",
      },
    },
  },
};
