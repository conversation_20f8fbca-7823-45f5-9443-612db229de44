<script lang="ts">
  import { onMount } from "svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { Memory } from "./types";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  // Props - all data and callbacks come from parent
  export let memory: Memory;
  export let onSave: (editedMemory: string) => void;
  export let onCancel: () => void;

  // Local state for edited values
  let editedMemory = memory.memory;

  // Reference to the first textarea for focus
  let textArea: HTMLTextAreaElement;

  // Handle save with edited values
  const handleSave = () => {
    onSave(editedMemory.trim());
  };

  // <PERSON>le cancel
  const handleCancel = () => {
    // Reset to original values
    editedMemory = memory.memory;
    onCancel();
  };

  // Handle keyboard shortcuts
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      if (event.key === "Enter") {
        event.preventDefault();
        handleSave();
      }
    }

    if (event.key === "Escape") {
      event.preventDefault();
      handleCancel();
    }
  };

  // Update local state when memory prop changes
  $: if (memory) {
    editedMemory = memory.memory;
  }

  // Focus the first textarea when component mounts
  onMount(() => {
    if (textArea) {
      textArea.focus();
    }
  });
</script>

<form class="memory-editor" on:submit|preventDefault={handleSave}>
  <div class="memory-editor__scope">
    <div class="memory-editor__memory">
      <h3 class="memory-editor__memory-label">
        <TextAugment size={1} color="secondary">Memory</TextAugment>
      </h3>
      <TextAreaAugment
        rows={3}
        bind:textInput={textArea}
        placeholder="Edit memory..."
        bind:value={editedMemory}
        onkeydown={handleKeydown}
      />
    </div>

    <footer class="memory-editor__actions" role="group" aria-label="Memory actions">
      <ButtonAugment size={1} variant="solid" color="accent" type="submit">
        <Icon name="check" slot="iconLeft" />
        Save Memory
      </ButtonAugment>
      <ButtonAugment size={1} variant="ghost" color="neutral" type="button" on:click={handleCancel}>
        Cancel
      </ButtonAugment>
    </footer>
  </div>
</form>

<style>
  .memory-editor {
    width: 100%;
  }

  .memory-editor__memory-label {
    margin-bottom: var(--ds-spacing-1);
    display: block;
  }

  .memory-editor__actions {
    display: flex;
    gap: 0.5rem;
    padding-top: var(--ds-spacing-4);
    border-top: 1px solid var(--augment-border-color-subtle);
  }
</style>
