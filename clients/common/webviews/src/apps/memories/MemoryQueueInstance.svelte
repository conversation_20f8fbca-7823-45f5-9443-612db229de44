<script lang="ts">
  import type { Memory } from "./types";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import { type MemoryState } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  export let memory: Memory;
  export let onSave: ((memoryId: string) => Promise<void>) | undefined = undefined;
  export let onDiscard: ((memoryId: string) => Promise<void>) | undefined = undefined;
  export let element: HTMLElement | undefined = undefined;

  export let toggleEditMode: (() => void) | undefined = undefined;
  export let numberOfSavedMemories: number = 0;

  // Internal state management
  let memoryReviewState: MemoryState = memory.state || "pending";
  let isLoading: boolean = false;

  // Internal action interface for simplified callbacks
  function handleKeydown(event: KeyboardEvent, callback?: () => void) {
    if (event.key === "Enter" && callback) {
      event.preventDefault();
      callback();
    }
  }

  async function handleSave() {
    if (!onSave) return;

    isLoading = true;
    try {
      await onSave(memory.id);
      numberOfSavedMemories += 1;
      memoryReviewState = "accepted";
    } finally {
      isLoading = false;
    }
  }

  async function handleDiscard() {
    if (!onDiscard) return;

    isLoading = true;
    try {
      await onDiscard(memory.id);
      memoryReviewState = "rejected";
    } finally {
      isLoading = false;
    }
  }
</script>

<div
  bind:this={element}
  class:memory-queue-instance__callout={memoryReviewState === "accepted" ||
    memoryReviewState === "rejected"}
  class:memory-queue-instance__pending={memoryReviewState === "pending"}
  class:memory-queue-instance__editing={memoryReviewState === "editing"}
  aria-labelledby="memory-{memory.id}-title"
  role="article"
>
  {#if memoryReviewState === "accepted"}
    <CalloutAugment variant="soft" color="success" size={1} role="status" aria-live="polite">
      {#snippet icon()}
        <Icon name="check" />
      {/snippet}
      Memory has been saved.
    </CalloutAugment>
  {/if}

  {#if memoryReviewState === "rejected"}
    <CalloutAugment variant="soft" color="error" size={1} role="status" aria-live="polite">
      {#snippet icon()}
        <Icon name="check" />
      {/snippet}
      Memory has been rejected.
    </CalloutAugment>
  {/if}
  {#if memoryReviewState === "editing"}
    <div class="memory-queue-instance__edit-indicator">Memory is being edited.</div>
  {/if}

  {#if memoryReviewState === "pending"}
    <div class="memory-queue-instance__content">
      <h3 class="memory-queue-instance__label">
        <TextAugment size={1} color="secondary">Memory</TextAugment>
      </h3>
      <TextAugment color="primary" size={1}>
        {memory.memory}
      </TextAugment>
    </div>

    <div class="memory-queue-instance__metadata" aria-label="Memory metadata">
      <ul>
        {#if memory.scope}
          <li>
            <span class="memory-queue-instance__metadata-label">Scope:</span>
            <span class="memory-queue-instance__metadata-value">{memory.scope}</span>
          </li>
        {/if}
        <li>
          <span class="memory-queue-instance__metadata-label">Created:</span>
          <span class="memory-queue-instance__metadata-value"
            >{new Date(memory.created).toLocaleDateString()}</span
          >
        </li>
        <li>
          <span class="memory-queue-instance__metadata-label">Source:</span>
          <span class="memory-queue-instance__metadata-value">{memory.source}</span>
        </li>
      </ul>
    </div>

    <footer class="memory-queue-instance__actions" role="group" aria-label="Memory actions">
      {#if isLoading}
        <div class="memory-queue-instance__loading" role="status" aria-live="polite">
          <SpinnerAugment size={1} />
          <TextAugment size={1} color="secondary">Processing...</TextAugment>
        </div>
      {:else}
        <!-- Save button -->
        {#if onSave}
          <ButtonAugment
            size={1}
            color="accent"
            variant="soft"
            on:click={handleSave}
            on:keydown={(e) => handleKeydown(e, handleSave)}
            aria-label="Save memory"
          >
            <Icon name="check" slot="iconLeft" />
            Save
          </ButtonAugment>
        {/if}
        <!-- Discard button -->
        {#if onDiscard}
          <ButtonAugment
            size={1}
            color="error"
            variant="soft"
            on:click={handleDiscard}
            on:keydown={(e) => handleKeydown(e, handleDiscard)}
            aria-label="Discard memory"
          >
            <Icon name="x" slot="iconLeft" />
            Discard
          </ButtonAugment>
        {/if}
        <!-- Edit button -->
        {#if toggleEditMode}
          <ButtonAugment
            size={1}
            color="neutral"
            variant="ghost"
            on:click={toggleEditMode}
            on:keydown={(e) => handleKeydown(e, toggleEditMode)}
            aria-label="Edit memory"
          >
            <Icon name="pencil" slot="iconLeft" />
            Edit
          </ButtonAugment>
        {/if}
      {/if}
    </footer>
  {/if}
</div>

<style>
  .memory-queue-instance__pending {
    border-bottom: 1px solid var(--ds-color-neutral-6);
    padding: var(--ds-spacing-4) 0;
  }

  .memory-queue-instance__callout {
    padding: var(--ds-spacing-2) 0;
  }

  .memory-queue-instance__metadata {
    font-size: 0.875em;
    color: var(--augment-text-color-tertiary);
    margin-bottom: 0.5rem;
  }

  .memory-queue-instance__metadata ul {
    display: flex;
    flex-direction: row;
    gap: 0.25rem;
    justify-content: space-between;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .memory-queue-instance__metadata li {
    display: flex;
    align-items: baseline;
    gap: calc(var(--ds-spacing-1) / 2);
  }

  .memory-queue-instance__metadata-label {
    font-weight: 500;
  }

  .memory-queue-instance__content {
    margin-bottom: 1rem;
  }

  .memory-queue-instance__label {
    margin: 0;
  }

  .memory-queue-instance__actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .memory-queue-instance__loading {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
