// Read more about shortcuts at https://www.notion.so/Keyboard-Shortcuts-Svelte-249bba10175a805bac4dcea8714e43f4?source=copy_link
// Keys are in mac format by default and mapped for windows/linux later
/* eslint-disable @typescript-eslint/naming-convention */
const rawConfig = [
  {
    key: "enter",
    name: "Focus Chat Input",
    description: "Focuses main chat input textarea",
    commandName: "focusChatInput",
  },
  {
    key: "enter",
    name: "Send Message",
    description: "Send message to agent or chat",
    commandName: "sendChatAction",
  },
  {
    key: "command+enter",
    name: "Send Message",
    description: "Send message to agent or chat",
    commandName: "sendChatAction",
  },
  {
    key: "enter",
    name: "Send Message edit",
    description: "Send edited message to agent or chat",
    commandName: "submitMessageEdit",
  },
  {
    key: "escape",
    name: "Cancel message edit",
    description: "Cancels current agent or chat response generation",
    commandName: "cancelMessageEdit",
  },
  {
    key: "escape",
    name: "Close",
    description: "Closes full size overlay",
    commandName: "closeFullSizeOverlay",
  },
  {
    key: "escape",
    name: "Cancel generation",
    description: "Cancels current agent or chat response generation",
    commandName: "cancelGeneration",
  },
  {
    key: "command+shift+backspace",
    name: "Cancel generation",
    description: "Cancels current agent or chat response generation",
    commandName: "cancelGeneration",
  },
  {
    key: "option+enter",
    name: "Resend Message",
    description: "Resend message after interruption",
    commandName: "resendMessage",
  },
  {
    key: "command+.",
    name: "Toggle Agent Chat Auto Mode",
    description: "Toggle chat agent mode and focus input",
    commandName: "toggleAgentMode",
  },
  {
    key: "command+n",
    name: "New Conversation",
    description: "Create a new conversation thread of current type",
    commandName: "newConversation, closeThreadsPanel",
  },
  {
    key: "command+up",
    name: "Scroll Conversation Up",
    description: "Scrolls conversation to previous message",
    commandName: "scrollConversationUp",
  },
  {
    key: "command+down",
    name: "Scroll Conversation Down",
    description: "Scrolls conversation to next message",
    commandName: "scrollConversationDown",
  },
  {
    key: "command+option+'",
    name: "Toggle Threads",
    description: "Toggle the threads panel open/closed (debug feature)",
    commandName: "toggleThreadsPanel",
  },
  {
    key: "option+'",
    name: "Close Threads Panel",
    description: "Close the threads panel (debug feature)",
    commandName: "closeThreadsPanel",
  },

  // Conversation navigation
  {
    key: "command+]",
    name: "Next Conversation",
    description: "Switch to next conversation (debug feature)",
    commandName: "goToNextConversation",
  },
  {
    key: "command+[",
    name: "Previous Conversation",
    description: "Switch to previous conversation (debug feature)",
    commandName: "goToPreviousConversation",
  },
  {
    key: "command+/",
    name: "Enhance Prompt",
    description: "Enhance the current prompt",
    commandName: "enhancePrompt",
  },
  {
    key: "command+option+/",
    name: "Toggle Ask Mode",
    description: "Toggle ask mode",
    commandName: "toggleAskMode",
  },
  {
    key: "command+shift+enter",
    name: "Allow tool",
    description: "Allow tool to run",
    commandName: "allowTool",
  },
  {
    key: "command+option+A",
    name: "Open Context Menu",
    description: "Open context menu",
    commandName: "openContextMenu",
  },
  {
    key: "command+option+;",
    name: "Open Guidelines Settings",
    description: "Open guidelines settings",
    commandName: "openRulesAndGuidelinesSettings",
  },
  {
    key: "command+;",
    name: "Open Agent Memories",
    description: "Open Agent Memories",
    commandName: "openAgentMemories",
  },
  {
    key: "command+option+.",
    name: "Open Model Picker",
    description: "Open Model Picker",
    commandName: "openModalPicker",
  },
] as const;

const _commandNames = rawConfig.reduce((acc, item) => {
  // some commands are combined, so we need to split them
  return acc.concat(item.commandName.split(/,\s*/g));
}, [] as string[]);

export type ShortcutCommandName = Extract<(typeof _commandNames)[number], string>;
export type HotKeyCommandConfiguration = {
  key: string;
  name: string;
  description: string;
  commandName: ShortcutCommandName;
};

export const hotkeyConfig: readonly HotKeyCommandConfiguration[] = rawConfig;
