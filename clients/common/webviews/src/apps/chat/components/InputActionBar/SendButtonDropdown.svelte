<script lang="ts">
  import { getContext } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import SplitSendButton from "./SplitSendButton.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import {
    SendMode,
    SEND_MODE_OPTIONS,
    SEND_MODE,
    type SendModeOption,
  } from "../../types/send-mode";
  import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import { type Readable } from "svelte/store";

  const SEND_BUTTON_TOOLTIP_OFFSET_Y = 10; // Accomodates the top padding of the input action bar

  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");

  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
  if (!agentConversationModel) {
    throw new Error("AgentConversationModel not found in context");
  }

  export let primaryAction: () => boolean;
  export let isDisabled: boolean = false;
  export let disabledReason: string = "";
  export let showOnlySend = false;

  // TODO: Temporary prop for Svelte 5 migration
  export const tooltipContent: any = undefined;

  $: conversationModel = $chatModel.currentConversationModel;
  $: flagsModel = $chatModel.flags;

  // Send mode logic
  $: currentSendMode = $chatModel.sendModeModel.mode;
  $: currentModeOption = showOnlySend
    ? SEND_MODE
    : SEND_MODE_OPTIONS.find((opt) => opt.id === $currentSendMode);
  // Always show Send icon when not in agent mode or when task lists are disabled
  $: currentModeIcon =
    $isCurrConversationAgentic && $flagsModel.enableTaskList
      ? currentModeOption?.icon || SEND_MODE.icon
      : SEND_MODE.icon;

  function getCurrentModeLucideIcon(
    currentModeOption: SendModeOption | undefined,
    isCurrConversationAgentic: Readable<boolean>,
    enableTaskList: boolean
  ) {
    return isCurrConversationAgentic && enableTaskList
      ? currentModeOption?.lucideIcon || SEND_MODE.lucideIcon
      : SEND_MODE.lucideIcon;
  }

  // Tooltip content for the primary action
  $: primaryActionLabel = (() => {
    if (isDisabled) {
      return disabledReason;
    }

    if ($isCurrConversationAgentic && $flagsModel.enableTaskList) {
      return currentModeOption?.label || "Send to Agent";
    }

    return "Send message";
  })();

  // Check if current conversation is agentic
  $: isCurrConversationAgentic = agentConversationModel.isCurrConversationAgentic;

  // Only show mode options in agent mode AND when task lists are enabled
  $: availableModeOptions =
    $isCurrConversationAgentic && $flagsModel.enableTaskList && !showOnlySend
      ? SEND_MODE_OPTIONS
      : [];

  // Reset to send mode when switching to chat mode OR when task lists are disabled
  $: {
    if (
      (!$isCurrConversationAgentic || !$flagsModel.enableTaskList) &&
      $currentSendMode === SendMode.addTask
    ) {
      $chatModel.sendModeModel.setMode(SendMode.send);
      $chatModel.save();
    }
  }

  // Mode selection handler
  function handleModeSelect(mode: SendMode) {
    $chatModel.sendModeModel.setMode(mode);
    // Trigger state save to persist the mode change
    $chatModel.save();
  }

  let modelIdToDisplayName: { [modelId: string]: string } = {};
  $: {
    modelIdToDisplayName = Object.fromEntries(
      Object.entries($chatModel.flags.modelDisplayNameToId).map(([k, v]) => [v, k])
    );
  }

  function handleSendClick() {
    // Track the event
    $chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.SEND_ACTION_TRIGGERED, {
      source: "button",
    });

    return primaryAction();
  }
</script>

<div class="c-send-button">
  <SplitSendButton
    disabled={{
      primaryDisabled: isDisabled,
      dropdownDisabled: false,
    }}
    onSelectModel={(modelId) => conversationModel.setSelectedModelId(modelId)}
    onSend={handleSendClick}
    {modelIdToDisplayName}
    modeOptions={availableModeOptions}
    currentMode={$currentSendMode}
    onSelectMode={handleModeSelect}
  >
    <TextTooltipAugment
      content={$$slots.tooltipContent ? undefined : primaryActionLabel}
      triggerOn={[TooltipTriggerOn.Hover]}
      side="top"
      delayDurationMs={500}
      offset={[0, SEND_BUTTON_TOOLTIP_OFFSET_Y]}
    >
      <svelte:fragment slot="content">
        {#if $$slots.tooltipContent}
          {primaryActionLabel} <slot name="tooltipContent" />
        {/if}
      </svelte:fragment>
      <GuardedIcon
        name={getCurrentModeLucideIcon(
          currentModeOption,
          isCurrConversationAgentic,
          flagsModel.enableTaskList
        )}
      >
        <svelte:component this={currentModeIcon} />
      </GuardedIcon>
    </TextTooltipAugment>
  </SplitSendButton>
</div>

<style>
  .c-send-button {
    display: flex;
  }
  .c-send-button :global(svg) {
    --icon-size: var(--ds-spacing-4);
    width: var(--icon-size);
    height: var(--icon-size);
    padding: var(--ds-spacing-0_5);
    opacity: 1;
  }
</style>
