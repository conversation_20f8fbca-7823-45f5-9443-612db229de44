<script lang="ts">
  import { getContext } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { SendMode, SEND_MODE, ADD_TASK_MODE } from "../../types/send-mode";
  import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";

  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");

  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
  if (!agentConversationModel) {
    throw new Error("AgentConversationModel not found in context");
  }

  export let primaryAction: () => boolean;
  export let isDisabled: boolean = false;
  export let disabledReason: string = "";

  // TODO: Temporary prop for Svelte 5 migration
  export const tooltipContent: any = undefined;

  // Send mode logic
  $: currentSendMode = $chatModel.sendModeModel.mode;
  $: currentModeOption = $currentSendMode === SendMode.send ? SEND_MODE : ADD_TASK_MODE;

  // Tooltip content for the primary action
  $: primaryActionLabel = (() => {
    if (isDisabled) {
      return disabledReason;
    }

    if ($currentSendMode === SendMode.send) {
      return $isCurrConversationAgentic ? currentModeOption?.label : "Send Message";
    }
    return currentModeOption.label;
  })();

  // Check if current conversation is agentic
  $: isCurrConversationAgentic = agentConversationModel.isCurrConversationAgentic;

  function handleSendClick() {
    // Track the event
    $chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.SEND_ACTION_TRIGGERED, {
      source: "button",
    });

    return primaryAction();
  }
</script>

<div class="c-send-button">
  <ButtonAugment
    size={0.5}
    variant="solid"
    color="accent"
    disabled={isDisabled}
    on:click={handleSendClick}
  >
    <TextTooltipAugment
      content={$$slots.tooltipContent ? undefined : primaryActionLabel}
      triggerOn={[TooltipTriggerOn.Hover]}
      side="top"
      delayDurationMs={500}
    >
      <svelte:fragment slot="content">
        {#if $$slots.tooltipContent}
          {primaryActionLabel} <slot name="tooltipContent" />
        {:else}
          {primaryActionLabel}
        {/if}
      </svelte:fragment>
      <GuardedIcon name={currentModeOption.lucideIcon}>
        <svelte:component this={currentModeOption.icon} />
      </GuardedIcon>
    </TextTooltipAugment>
  </ButtonAugment>
</div>

<style>
  .c-send-button {
    display: flex;
  }
  .c-send-button :global(svg) {
    --icon-size: var(--ds-spacing-4);
    width: var(--icon-size);
    height: var(--icon-size);
    padding: var(--ds-spacing-0_5);
    opacity: 1;
  }
</style>
