<script lang="ts">
  import {
    isItemAllDefaultContext,
    isItemClearContext,
    isItemExternalSource,
    isItemFile,
    isItemFolder,
    isItemGroup,
    isItemPersonality,
    isItemRecentFile,
    isItemRule,
    isItemSelection,
    isItemSourceFolder,
    isItemGuidelines,
  } from "../../types/mention-option";
  import { getPersonalityIconComponent } from "../../utils/personality-icon-mapper";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { type ChatMentionableDropdownData } from "./context-menu-controller";
  import { type ComponentType } from "svelte";
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  export let item: ChatMentionableDropdownData;
  export let onSelect: () => void;
  export let highlight: boolean | undefined = undefined;

  let component: ComponentType | undefined;
  $: {
    if (item.type === "breadcrumb-back") {
      component = DropdownMenuAugment.BreadcrumbBackItem;
    } else if (item.type === "breadcrumb" && isItemGroup(item)) {
      component = DropdownMenuAugment.BreadcrumbItem;
    } else if (item.type === "item" && !isItemGroup(item)) {
      component = DropdownMenuAugment.Item;
    }
  }
</script>

<svelte:component this={component} {highlight} {onSelect}>
  {#if isItemFile(item)}
    <Filespan filepath={item.file.pathName}>
      {#snippet leftIcon()}
        <Icon name="file-text" />
      {/snippet}
    </Filespan>
  {:else if isItemFolder(item)}
    <Filespan filepath={item.folder.pathName}>
      {#snippet leftIcon()}
        <Icon name="folder-open" />
      {/snippet}
    </Filespan>
  {:else if isItemExternalSource(item)}
    <Filespan filepath={item.externalSource.name}>
      {#snippet leftIcon()}
        <Icon name="book-open" />
      {/snippet}
    </Filespan>
  {:else if isItemSourceFolder(item)}
    <Filespan filepath={item.sourceFolder.folderRoot}>
      {#snippet leftIcon()}
        <Icon name="folder-root" />
      {/snippet}
    </Filespan>
  {:else if isItemSelection(item)}
    <Filespan filepath={item.selection.pathName}>
      {#snippet leftIcon()}
        <Icon name="text-cursor" />
      {/snippet}
    </Filespan>
  {:else if isItemRecentFile(item)}
    <Filespan filepath={item.recentFile.pathName}>
      {#snippet leftIcon()}
        <Icon name="file-text" />
      {/snippet}
    </Filespan>
  {:else if isItemRule(item)}
    <Filespan filepath={item.rule.path}>
      {#snippet leftIcon()}
        <Icon name="list-todo" />
      {/snippet}
    </Filespan>
  {:else if isItemGroup(item)}
    {item.label}
  {:else if isItemPersonality(item)}
    <TextCombo>
      <span slot="leftIcon" class="c-context-menu-item__icon">
        <svelte:component this={getPersonalityIconComponent(item.personality.type)} />
      </span>
      <span slot="text">{item.label}</span>
    </TextCombo>
  {:else if isItemAllDefaultContext(item) || isItemClearContext(item) || isItemGuidelines(item)}
    <span class="c-mentionable-group-label">
      <span class="c-mentionable-group-label__text right">{item.label}</span>
    </span>
  {/if}
</svelte:component>

<style>
  .c-context-menu-item__icon > :global(svg) {
    height: var(--ds-icon-size-1);
    width: var(--ds-icon-size-1);
  }
</style>
