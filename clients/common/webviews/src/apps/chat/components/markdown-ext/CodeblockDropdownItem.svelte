<script lang="ts">
  import type { ICodeblockActionButton } from "$common-webviews/src/apps/chat/components/markdown-ext/utils";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  interface Props {
    data: ICodeblockActionButton;
    closeDropdown: () => void;
  }

  let { data, closeDropdown }: Props = $props();

  let {
    onClick,
    onSuccessMessage,
    iconName,
    buttonText,
    onMouseDown,
    onMouseLeave,
    onMouseOver,
    onBlur,
    onFocus,
  } = $derived(data);

  let displaySuccessText: string | undefined = $state(undefined);
  let successTimer: ReturnType<typeof setTimeout> | undefined = undefined;

  function onClickWrapper() {
    // If timer is running, do nothing
    if (successTimer) {
      return;
    }

    onClick();
    displaySuccessText = onSuccessMessage;

    // Set a new timer
    successTimer = setTimeout(() => {
      displaySuccessText = undefined;
      successTimer = undefined;
      closeDropdown();
    }, 1500);
  }
</script>

<DropdownMenuAugment.Item
  onSelect={onClickWrapper}
  on:mouseover={(e) => onMouseOver?.(e.detail)}
  on:mouseleave={(e) => onMouseLeave?.(e.detail)}
  on:blur={(e) => onBlur?.(e.detail)}
  on:focus={(e) => onFocus?.(e.detail)}
  on:mousedown={(e) => onMouseDown?.(e.detail)}
>
  {#snippet iconLeft()}
    {#if iconName}
      <Icon name={iconName} />
    {/if}
  {/snippet}
  {displaySuccessText ?? buttonText}
</DropdownMenuAugment.Item>
