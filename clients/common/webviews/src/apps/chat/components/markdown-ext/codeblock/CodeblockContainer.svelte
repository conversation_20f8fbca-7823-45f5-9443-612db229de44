<script lang="ts">
  import { type Tokens } from "marked";
  import CodeblockSuccessfulButton from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/CodeblockSuccessfulButton.svelte";
  import ExpandButton from "$common-webviews/src/common/components/ExpandButton.svelte";
  import { getIntersectionObserverManagerContext } from "$common-webviews/src/common/components/intersection-display";
  import LanguageIcon from "$common-webviews/src/common/components/language-icons/LanguageIcon.svelte";
  import { DEFAULT_MONACO_OPTIONS } from "$common-webviews/src/common/components/markdown/codeblock/monaco";
  import { themeStore } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import { normalizeFilePath } from "$common-webviews/src/common/utils/file-paths";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import { STICKY_HEADER_TOP_PX } from "$common-webviews/src/design-system/components/CollapsibleAugment/constants";
  import DeferredLoadingAugment from "$common-webviews/src/design-system/components/DeferredLoadingAugment.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import { ChatMetricName } from "$vscode/src/metrics/types";
  import { ExchangeStatus } from "../../../types/chat-message";
  import CollapsibleIcon from "../../CollapsibleIcon.svelte";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import FilespanOpen from "../../conversation/blocks/tools/FilespanOpen.svelte";
  import type { ICodeblockActionButton, ICodeblockMetadata } from "../utils";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  export let token: Tokens.Code;
  export let codeblockMetadata: ICodeblockMetadata | undefined;
  export let collapsed: boolean = true;
  export let element: HTMLDivElement | undefined = undefined;
  let isHeaderStuck: boolean;

  export let lineHeight = DEFAULT_MONACO_OPTIONS.lineHeight ?? 18;
  /** Number of codeblock lines to show when truncated */
  export let truncateLines = 10;

  export let requestStatus: ExchangeStatus | undefined;
  export let primaryButton: ICodeblockActionButton;
  export let buttons: ICodeblockActionButton[];
  export let truncate: boolean = false;
  export let opened: boolean = false;
  export let codeContainerElement: HTMLDivElement | undefined = undefined;

  const { enabled: intersectionObserverEnabled } = getIntersectionObserverManagerContext();

  $: themeCategory = $themeStore?.category;
  $: normalizedPath = normalizeFilePath(codeblockMetadata?.relPath ?? "");
  $: language = codeblockMetadata?.language || token.lang;

  $: numLinesOfCode = (token.text.match(/\r?\n/g) || []).length + 1;
  $: deferredEstimatedHeight = Math.min(numLinesOfCode, truncateLines) * lineHeight;
  $: isMessageLoading = requestStatus === ExchangeStatus.sent;
</script>

<div
  bind:this={element}
  class:is-sticky={isHeaderStuck}
  class={`c-codeblock c-codeblock__${themeCategory}`}
  style={`--codeblock-line-height: ${lineHeight}px; --codeblock-truncate-lines: ${truncateLines};`}
>
  <CollapsibleAugment
    stickyHeaderTop={STICKY_HEADER_TOP_PX}
    stickyHeader
    bind:isHeaderStuck
    bind:collapsed
    toggleHeader
    expandable
    class="c-codeblock__container"
  >
    <div slot="header" class="c-codeblock__header-content">
      <CollapsibleIcon {collapsed} expandable>
        <!-- TODO: add support of data-language={language} data-filename={normalizedPath} to lucide flie icons -->
        <GuardedIcon name="file" slot="icon">
          <LanguageIcon {language} filename={normalizedPath} class="c-icon-file-path__lang-icon" />
        </GuardedIcon>
      </CollapsibleIcon>
      <div class="c-codeblock__relpath">
        {#if codeblockMetadata?.relPath}
          <FilespanOpen
            filepath={normalizedPath}
            metric={ChatMetricName.chatCodeblockGoToFile}
            codeSnippetToLocate={token.text}
          />
        {/if}
      </div>
      <div class="c-codeblock__action-bar-right">
        {#if isMessageLoading}
          <div class="c-codeblock__loading">
            <SpinnerAugment size={1} />
          </div>
        {:else}
          <CodeblockSuccessfulButton {primaryButton} />
        {/if}
        <DropdownMenu.Root nested={false}>
          <DropdownMenu.Trigger>
            <IconButtonAugment
              variant="ghost-block"
              color="neutral"
              size={0}
              data-collapsible-button="false"
            >
              <GuardedIcon name="ellipsis"><DotsHorizontal /></GuardedIcon>
            </IconButtonAugment>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content size={1} side="bottom" align="end">
            {#each buttons as button}
              <DropdownMenu.Item onSelect={button.onClick} disabled={button.disabled}>
                {#if button.iconName}
                  <Icon slot="iconLeft" name={button.iconName} />
                {/if}
                {button.buttonText}
              </DropdownMenu.Item>
            {/each}
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </div>
    </div>

    <div
      class="c-codeblock__container-inner"
      class:c-codeblock__truncated={truncate}
      bind:this={codeContainerElement}
    >
      <slot>
        {#if $intersectionObserverEnabled}
          <SimpleMonaco
            options={DEFAULT_MONACO_OPTIONS}
            text={token.text}
            lang={language || token.lang}
          />
        {:else}
          <DeferredLoadingAugment minHeight="{deferredEstimatedHeight}px">
            <SimpleMonaco
              options={DEFAULT_MONACO_OPTIONS}
              text={token.text}
              lang={language || token.lang}
            />
          </DeferredLoadingAugment>
        {/if}
      </slot>
      {#if truncate}
        <div class="c-codeblock__truncated-surface">
          <ExpandButton
            expanded={false}
            onClick={() => {
              truncate = false;
              opened = true;
            }}
          />
        </div>
      {/if}
    </div>
  </CollapsibleAugment>
</div>

<style>
  .c-codeblock {
    --augment-file-icon-font-size: 20px;
    margin: var(--ds-spacing-1) 0;
  }

  .c-codeblock :global(.c-collapsible__header-inner) {
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
  }

  .c-codeblock :global(.c-collapsible__header) {
    /**
     * When the window is small, we need the left side to shrink faster in
     * order for the directory to not overflow into the buttons on the right
     * side.
     */
    flex-shrink: 10;
    /**
     * But we still want the left side to take all the available space
     */
    width: 100%;
  }

  .c-codeblock__header-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    width: 100%;
    cursor: pointer;
    min-height: 20px;
  }

  .c-codeblock__collapse-btn {
    margin-right: var(--ds-spacing-1);
  }

  .c-codeblock__light .c-codeblock__truncated-surface {
    background: var(--light-gradient);
  }
  .c-codeblock__relpath {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 0;
    width: 100%;
    flex: 1;
  }

  .c-codeblock__relpath :global(.l-tooltip-trigger) {
    max-width: 100%;
  }

  .c-codeblock__relpath :global(.c-codeblock__filename) {
    width: 100%;
    color: var(--ds-color-neutral-12);
    max-width: 100%;
    overflow: hidden;
  }

  .c-codeblock__relpath :global(.c-codeblock__filename .c-button--content) {
    display: block;
    overflow: hidden;
    text-wrap: nowrap;
    text-overflow: ellipsis;
    width: 100%;
  }

  .c-codeblock__action-bar-right {
    padding-right: var(--ds-spacing-1);
    display: flex;
    /* Words with spaces get automatically wrapped */
    text-wrap: nowrap;
    /** Right align the buttons */
    justify-content: end;
    /** Vertically center the buttons */
    align-items: center;
  }

  .c-codeblock__loading {
    display: flex;
    align-items: center;
    margin-right: var(--ds-spacing-1);
  }

  /* Styles for the CollapsibleAugment container */
  .c-codeblock :global(.c-codeblock__container) {
    --base-btn-disabled-color: var(--augment-text-color);
    gap: 0;
    padding: 0;
    border: 0;
    overflow: visible;
  }

  /* Styles for the inner container that holds the code */
  .c-codeblock__container-inner {
    position: relative;
  }

  .c-codeblock__truncated {
    max-height: calc(var(--codeblock-line-height) * var(--codeblock-truncate-lines));
    overflow: hidden;

    border-bottom-left-radius: var(--ds-radius-2);
    border-bottom-right-radius: var(--ds-radius-2);
  }

  .c-codeblock__truncated-surface {
    /**
     * Gradients to fade out the bottom of the codeblock to represent that
     * there is more content hidden.
     */
    --dark-gradient: linear-gradient(
      180deg,
      oklch(from var(--user-theme-panel-background) l c h / 0) 0%,
      var(--user-theme-panel-background) 100%
    );

    /** Note that for light themes, we use the sidebar background color,
    because it is darker than the panel background. */
    --light-gradient: linear-gradient(
      180deg,
      oklch(from var(--user-theme-sidebar-background) l c h / 0) 0%,
      var(--user-theme-sidebar-background) 100%
    );

    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    align-items: center;
    padding: var(--ds-spacing-2);
    background: var(--dark-gradient);
  }

  /** Overwrite the default Monaco styles to align with designs */
  .c-codeblock__container-inner :global(.c-codeblock__monaco) {
    border: none;
    border-radius: unset;

    border-bottom-left-radius: var(--ds-radius-2);
    border-bottom-right-radius: var(--ds-radius-2);

    & :global(.monaco-editor) {
      outline: none;
    }

    &:global(:has(.monaco-editor.focused)) {
      border: none;
    }
  }

  /* Theme-specific codeblock lightness */
  :global([data-augment-theme-category="light"]) .c-codeblock {
    --codeblock-lightness: 0.075; /* Lighten the codeblock background */
  }

  :global([data-augment-theme-category="dark"]) .c-codeblock {
    --codeblock-lightness: -0.075; /* Darken the codeblock background */
  }

  .c-codeblock__container-inner :global(.monaco-editor-background),
  .c-codeblock__container-inner :global(.monaco-editor),
  .c-codeblock__container-inner :global(.monaco-editor .margin) {
    /* Override monaco's background color with the block container's color,
    with oklch magic to brighten or darken depending on theme. */
    background-color: oklch(
      from var(--user-theme-sidebar-background) calc(l + var(--codeblock-lightness)) c h
    );
  }
</style>
