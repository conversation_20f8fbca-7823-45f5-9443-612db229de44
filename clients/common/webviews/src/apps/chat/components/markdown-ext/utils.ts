import { type IconName } from "$common-webviews/src/design-system/icons/lucide-icon-registry";
import { ChatMetricName } from "$vscode/src/metrics/types";
import { type IExtensionClient } from "../../extension-client";
import { type ButtonStateText } from "../buttons/types";
import { type CodeblockActionType } from "./codeblock/types";

export interface ICodeblockMetadata {
  language: string | null;
  relPath: string | null;
  mode: string | null;
}

/** Props for a Codeblock's action button */
export interface ICodeblockActionButton {
  codeblockActionType: CodeblockActionType;
  /** The title shown on hover when this is a primary button */
  title: string;
  /**
   * The title shown on hover based on the button state. This will override the
   * `title` property
   */
  stateTitle?: ButtonStateText;
  /** Callback for when the button is clicked */
  onClick: () => void;
  /** The message shown on success when this is a primary button */
  onSuccessMessage?: string;
  /** The icon name for the button */
  iconName?: IconName;
  /** The text shown on the button */
  buttonText?: string;
  /** Whether this is a primary button */
  primary?: boolean;
  onMouseDown?: (e: MouseEvent) => void;
  onMouseLeave?: (e: MouseEvent) => void;
  onMouseOver?: (e: MouseEvent) => void;
  onFocus?: (e: FocusEvent) => void;
  onBlur?: (e: FocusEvent) => void;
  disabled?: boolean;
}

export function getFirstLine(rawCodeblock: string): string | undefined {
  // Search for first newline
  const firstNewlineIndex = rawCodeblock.indexOf("\n");

  // If not found, return undefined
  if (firstNewlineIndex === -1) {
    return undefined;
  }

  // Return the first line
  return rawCodeblock.slice(0, firstNewlineIndex);
}

/**
 * Parses metadata from a codeblock's opening line.
 * Handles formats like:
 * ```typescript path=src/file.ts mode=EDIT
 * ```python path=src/my folder/script.py mode=EDIT
 * ``` path=src/file.ts
 * ```typescript mode=EDIT
 */
export function parseCodeblockMetadata(rawCodeblock: string): ICodeblockMetadata {
  // Match language (everything between ``` and first space or end)
  const match = /^`{3,}(\w+)?/.exec(rawCodeblock);
  const language = match?.[1] ?? null;

  if (!match) {
    return { language: null, relPath: null, mode: null };
  }

  // Get the rest of the string after language
  const rest = rawCodeblock.slice(match[0].length);

  // Extract path if present (between 'path=' and ' mode=' or end)
  let relPath = null;
  const pathKey = " path=";
  const pathStart = rest.indexOf(pathKey);
  if (pathStart !== -1) {
    const pathContent = rest.slice(pathStart + pathKey.length); // Skip ' path='
    const modeIndex = pathContent.indexOf(" mode=");
    relPath = (modeIndex !== -1 ? pathContent.slice(0, modeIndex) : pathContent).trim();
  }

  // Extract mode if present
  let mode = null;
  const modeStart = rest.indexOf(" mode=");
  if (modeStart !== -1) {
    mode = rest.slice(modeStart + 6).trim(); // Skip ' mode='
  }

  return { language, relPath, mode };
}

export function isNotebook(relPath: string | null): boolean {
  if (!relPath) {
    return false;
  }
  const notebookExtensions = [".ipynb", ".rmd", ".qmd", ".jl", ".zmd"];
  return notebookExtensions.some((ext) => relPath.toLowerCase().endsWith(ext));
}

/** Writes the given text to the clipboard */
export function writeToClipboard(text: string) {
  navigator.clipboard.writeText(text);
}

/** Creates a new file with the given code at the relative path */
export function createNewFile(
  code: string,
  extensionClient: IExtensionClient,
  relPath: string | null | undefined
) {
  extensionClient.createFile(code, relPath || undefined);

  // We only create at the path if there is no file already there
  extensionClient.reportWebviewClientEvent(ChatMetricName.chatCodeblockCreate);
}
