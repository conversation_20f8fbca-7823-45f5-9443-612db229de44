import { render } from "@testing-library/svelte";
import { expect, describe, test, vi, beforeEach, afterEach } from "vitest";

import CodeblockDropdownItemTest from "./CodeblockDropdownItem.test.svelte";
import userEvent from "@testing-library/user-event";
import { type ICodeblockActionButton } from "./utils";
import { CodeblockActionType } from "./codeblock/types";

describe("CodeblockDropdownItem.svelte", async () => {
  let data: ICodeblockActionButton;
  let closeSpy: ReturnType<typeof vi.fn>;
  let component: ReturnType<typeof render<typeof CodeblockDropdownItemTest>>;

  beforeEach(() => {
    data = {
      codeblockActionType: CodeblockActionType.copy,
      title: "Copy to clipboard",
      onClick: () => {},
      onSuccessMessage: "Copied!",
      iconName: "files",
      buttonText: "Copy",
    };
    closeSpy = vi.fn();
    component = render(CodeblockDropdownItemTest, {
      data,
      closeDropdown: closeSpy,
    });
  });

  afterEach(() => {
    component.unmount();
  });

  test("should render", async () => {
    expect(component.getByRole("button")).toBeInTheDocument();
  });

  test("should call onClick when clicked", async () => {
    const onClick = vi.fn();
    data = { ...data, onClick };
    component.rerender({ data });

    const button = component.getByRole("button");
    await userEvent.click(button);
    expect(onClick).toHaveBeenCalled();
  });

  test("should call onClick when enter is pressed if focused", async () => {
    const onClick = vi.fn();
    data = { ...data, onClick };
    component.rerender({ data });

    const button = component.getByRole("button");
    button.focus();
    await userEvent.keyboard("{Enter}");
    expect(onClick).toHaveBeenCalled();
  });

  test("should not call onClick when enter is pressed if not focused", async () => {
    const onClick = vi.fn();
    data = { ...data, onClick };
    component.rerender({ data });

    await userEvent.keyboard("{Enter}");
    expect(onClick).not.toHaveBeenCalled();
  });

  test("should display success text when clicked", async () => {
    const button = component.getByRole("button");
    await userEvent.click(button);
    expect(component.getByText("Copied!")).toBeInTheDocument();
  });

  test("should display success text when enter is pressed if focused", async () => {
    const button = component.getByRole("button");
    button.focus();
    await userEvent.keyboard("{Enter}");
    expect(component.getByText("Copied!")).toBeInTheDocument();
  });

  test("should not display success text when enter is pressed if not focused", async () => {
    await userEvent.keyboard("{Enter}");
    expect(component.queryByText("Copied!")).not.toBeInTheDocument();
  });

  test("should close dropdown when clicked", async () => {
    vi.useFakeTimers();

    component.rerender({ data });

    const button = component.getByRole("button");
    userEvent.click(button);
    await vi.runAllTimersAsync();

    expect(closeSpy).toHaveBeenCalled();
    vi.useRealTimers();
  });

  test("should close dropdown when enter is pressed if focused", async () => {
    vi.useFakeTimers();

    component.rerender({ data });

    const button = component.getByRole("button");
    button.focus();
    userEvent.keyboard("{Enter}");
    await vi.runAllTimersAsync();

    expect(closeSpy).toHaveBeenCalled();
    vi.useRealTimers();
  });

  test("should not close dropdown when enter is pressed if not focused", async () => {
    vi.useFakeTimers();

    component.rerender({ data });

    userEvent.keyboard("{Enter}");
    await vi.runAllTimersAsync();

    expect(closeSpy).not.toHaveBeenCalled();
    vi.useRealTimers();
  });

  test("clicking twice should not trigger onClick twice", async () => {
    const onClick = vi.fn();
    data = { ...data, onClick };
    component.rerender({ data });

    const button = component.getByRole("button");
    await userEvent.click(button);
    // Wait 100 ms
    await new Promise((resolve) => setTimeout(resolve, 100));
    await userEvent.click(button);
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  test("entering twice should not trigger onClick twice", async () => {
    const onClick = vi.fn();
    data = { ...data, onClick };
    component.rerender({ data });

    const button = component.getByRole("button");
    button.focus();
    await userEvent.keyboard("{Enter}");
    // Wait 100 ms
    await new Promise((resolve) => setTimeout(resolve, 100));
    await userEvent.keyboard("{Enter}");
    expect(onClick).toHaveBeenCalledTimes(1);
  });
});
