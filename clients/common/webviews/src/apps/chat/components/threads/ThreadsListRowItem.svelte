<script lang="ts" module>
  export const THREAD_ITEM_TEST_ID = "thread-list-item";

  const typeLabels = {
    chat: "Chat",
    localAgent: "Local Agent",
    remoteAgent: "Remote Agent",
  };

  function hasDraft(thread: ChatThread | LocalAgentThread | RemoteAgentThread): boolean {
    return !!thread.conversation?.draftExchange?.request_message?.trim();
  }

  const getFirstMessage = (thread: ChatThread | LocalAgentThread | RemoteAgentThread) => {
    const allMessages = thread?.conversation?.chatHistory || [];
    const firstUserMessage = allMessages.find(
      (item) => (item as ExchangeWithStatus).request_message
    ) as ExchangeWithStatus;
    if (!firstUserMessage) {
      return "";
    }
    return firstUserMessage.request_message || "";
  };

  function truncate(str = "", maxLength: number) {
    if (str.length <= maxLength) {
      return str;
    }
    return str.slice(0, maxLength - 3) + "...";
  }
</script>

<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import RemoteAgentIcon from "./RemoteAgentIcon.svelte";
  import Terminal from "$common-webviews/src/design-system/icons/augment/terminal.svelte";
  import ThreadMenu from "./ThreadMenu.svelte";
  import ThreadChatHistory from "./ThreadChatHistory.svelte";
  import NotifyButton from "$common-webviews/src/apps/chat/components/InputActionBar/NotifyButton.svelte";
  import Github from "$common-webviews/src/design-system/icons/github.svelte";
  import ThreadTitleEditor from "./ThreadTitleEditor.svelte";
  import type { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  // eslint-disable-next-line @typescript-eslint/consistent-type-imports
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import type { ChatThread, LocalAgentThread, RemoteAgentThread } from "./ThreadsPanel.svelte";
  import { NEW_AGENT_KEY } from "../../models/agent-constants";
  import { isChatItemHistorySummary, type ExchangeWithStatus } from "../../types/chat-message";
  import StatusIndicator from "$common-webviews/src/apps/remote-agent-manager/components/StatusIndicator.svelte";
  import { FOCUS_LIST_ITEM_CLASS } from "./list-focus-navigation";
  import { autofocus } from "$common-webviews/src/common/actions/autofocus";
  import { MODE_DISPLAY_INFO } from "../buttons/mode-display-helper";
  import {
    getRepoInfo,
    isFromDifferentRepo,
  } from "$common-webviews/src/apps/remote-agent-manager/utils/repository-utils";
  import Pin from "$common-webviews/src/design-system/icons/pin.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";

  interface Props {
    thread: ChatThread | LocalAgentThread | RemoteAgentThread;
    isSelected?: boolean;
    showIfPinned?: boolean;
    containerWidth?: number;
    afterDelete?: (() => void) | undefined;
    onSelect: () => void;
    onTogglePinned: (event?: Event) => void;
    currentRepoUrl?: string;
    enableShareService?: boolean;
    chatModel: ChatModel;
    remoteAgentsModel: RemoteAgentsModel;
  }

  let {
    thread = $bindable(),
    isSelected = false,
    showIfPinned = false,
    containerWidth = 0,
    afterDelete = undefined,
    onSelect,
    onTogglePinned,
    currentRepoUrl = "",
    enableShareService = false,
    chatModel,
    remoteAgentsModel,
  }: Props = $props();

  // State for editing thread title
  let isEditing = $state(false);

  // Function to handle thread renaming
  async function handleRename(newTitle: string) {
    if (newTitle.trim() && newTitle !== thread.title) {
      if (thread.type === "remoteAgent") {
        // Handle remote agent renaming via API call
        try {
          if (remoteAgentsModel) {
            await remoteAgentsModel.updateRemoteAgentTitle(thread.id, newTitle);
          }
        } catch (_error) {
          // Failed to update remote agent title
        }
      } else if (["chat", "localAgent"].includes(thread.type)) {
        // Handle chat and local agent renaming
        if (chatModel) {
          chatModel.renameConversation(thread.id, newTitle);
          thread.title = newTitle;
        }
      }
    }
    cancelEditing();
  }

  // Function to cancel editing
  function cancelEditing() {
    isEditing = false;
  }

  // Start editing mode when receiving the edit-thread event from ThreadMenu
  function startEditing() {
    isEditing = true;
  }

  let isNewThread = $derived(thread.isNew);
  let modeInfo = $derived(MODE_DISPLAY_INFO[thread.type]);
  let type = $derived(thread.type);

  /* eslint-disable @typescript-eslint/naming-convention */
  let userMessages = $derived(
    (thread.conversation?.chatHistory ?? [])
      .filter((item) => !isChatItemHistorySummary(item as any))
      .map((item) => ("request_message" in item ? item.request_message?.trim() : ""))
      .filter(Boolean) as string[]
  );
  /* eslint-enable @typescript-eslint/naming-convention */
</script>

<div
  use:autofocus={isSelected}
  class="agent-row agent-row--{thread.type} {FOCUS_LIST_ITEM_CLASS}"
  class:agent-row--active={isSelected}
  class:agent-row--setup-script={thread.type === "remoteAgent" && thread.is_setup_script_agent}
  onclick={onSelect}
  onkeydown={(e) => {
    if (e.key === "Enter" || e.key === " ") {
      onSelect();
    }
  }}
  role="button"
  tabindex="0"
  data-testid={THREAD_ITEM_TEST_ID}
>
  {#key `${thread.title}--${thread.isPinned}`}
    <div class="agent-content">
      <div class="agent-details">
        <div class="agent-details--left">
          <div class="agent-icon">
            {#if thread.type === "remoteAgent" && thread.has_updates}
              <div class="update-indicator"></div>
            {/if}
            {#if thread.type === "remoteAgent"}
              <RemoteAgentIcon remoteAgent={thread} />
            {:else}
              <div class="thread-type-icon thread-type-icon--{modeInfo.type}">
                <GuardedIcon name={modeInfo.lucideIcon}>
                  <modeInfo.icon />
                </GuardedIcon>
              </div>
            {/if}
          </div>

          {#if !isNewThread && thread.isPinned && showIfPinned}
            <div class="pin-icon" class:is-pinned={thread.isPinned}>
              <TextTooltipAugment
                nested={false}
                hasPointerEvents={false}
                content={thread.isPinned ? "Unpin thread" : "Pin thread"}
                triggerOn={[TooltipTriggerOn.Hover]}
              >
                <IconButtonAugment
                  variant="ghost-block"
                  color="accent"
                  size={0}
                  class="pin-icon__button"
                  onclick={(e) => {
                    e.stopPropagation();
                    onTogglePinned(e);
                  }}
                  onkeydown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.stopPropagation();
                      onTogglePinned(e);
                    }
                  }}
                  type="button"
                  aria-label={thread.isPinned ? "Unpin thread" : "Pin thread"}
                >
                  <GuardedIcon name="pin"><Pin /></GuardedIcon>
                </IconButtonAugment>
              </TextTooltipAugment>
            </div>
          {/if}

          {#if thread.type === "remoteAgent" && thread.agent && isFromDifferentRepo(thread.agent, currentRepoUrl)}
            {@const repoInfo = getRepoInfo(thread.agent)}
            <TextTooltipAugment
              nested={false}
              hasPointerEvents={false}
              content={`Repository: ${repoInfo.fullPath}`}
              triggerOn={[TooltipTriggerOn.Hover]}
            >
              <div class="repo-pill">
                <div class="repo-pill-icon">
                  <GuardedIcon name="github"><Github /></GuardedIcon>
                </div>
                <div class="repo-pill-text">
                  {repoInfo.name}
                </div>
              </div>
            </TextTooltipAugment>
          {/if}

          <div class="agent-title">
            <TextTooltipAugment
              nested={false}
              hasPointerEvents={false}
              content={truncate(thread.title, 200)}
              triggerOn={[TooltipTriggerOn.Hover]}
              delayDurationMs={1000}
            >
              <div class="agent-title-content">
                {#if thread.type === "remoteAgent" && thread.is_setup_script_agent}
                  <TextTooltipAugment
                    nested={false}
                    hasPointerEvents={false}
                    triggerOn={[TooltipTriggerOn.Hover]}
                    content="Setup Script Agent"
                  >
                    <div class="setup-script-badge">
                      <GuardedIcon name="terminal"><Terminal /></GuardedIcon>
                    </div>
                  </TextTooltipAugment>
                {/if}
                {#if isNewThread && hasDraft(thread)}
                  <TextTooltipAugment
                    nested={false}
                    hasPointerEvents={false}
                    triggerOn={[TooltipTriggerOn.Hover]}
                    content={"Draft message"}
                  >
                    <BadgeAugment.Root color="neutral" size={0} variant="soft">
                      <TextAugment size={0}>Draft</TextAugment>
                    </BadgeAugment.Root>
                  </TextTooltipAugment>
                {/if}
                {#if thread.type === "remoteAgent" && thread.is_setup_script_agent}
                  <TextAugment size={1} truncate class="setup-script-title"
                    >Generate a setup script</TextAugment
                  >
                {:else if isNewThread}
                  <TextAugment size={1} truncate class="draft-title">
                    {thread.title ||
                      thread.conversation?.draftExchange?.request_message ||
                      "New Thread"}
                  </TextAugment>
                {:else if isEditing}
                  <ThreadTitleEditor
                    value={thread.title}
                    onRename={handleRename}
                    onCancel={cancelEditing}
                  />
                {:else}
                  <TextAugment
                    size={1}
                    truncate
                    weight={thread.type === "remoteAgent" && thread.has_updates
                      ? "medium"
                      : "regular"}
                  >
                    {thread.title || getFirstMessage(thread) || `New ${typeLabels[thread.type]}`}
                  </TextAugment>
                {/if}
              </div>
            </TextTooltipAugment>
          </div>
        </div>

        {#if thread.type === "remoteAgent" && thread.status}
          <div class="agent-status">
            <StatusIndicator
              workspaceStatus={thread.workspace_status}
              status={thread.status}
              isExpanded={containerWidth > 300}
              hasUpdates={thread.has_updates}
            />
          </div>
          <NotifyButton
            agentId={thread.agent.remote_agent_id}
            status={thread.status}
            workspaceStatus={thread.workspace_status}
          />
        {/if}
      </div>

      <ThreadChatHistory {userMessages} />

      {#if thread.id !== NEW_AGENT_KEY}
        <div class="agent-actions">
          <ThreadMenu
            {thread}
            {type}
            {afterDelete}
            {enableShareService}
            {onTogglePinned}
            onRename={startEditing}
          />
        </div>
      {/if}
    </div>
  {/key}
</div>

<style>
  .agent-row {
    width: 100%;
    display: flex;
    align-items: center;
    height: 25px;
    padding-left: var(--ds-spacing-3);
    padding-right: var(--ds-spacing-1);
    cursor: pointer;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    border-radius: var(--ds-radius-2);
  }

  .agent-row:is(:hover, :focus, :focus-visible) {
    background: var(--ds-color-neutral-a3);
    color: var(--ds-color-neutral-12);
  }

  .agent-row:focus-visible {
    outline: none;
  }

  :is(.agent-row--active, .agent-row:hover) :global(svg) {
    opacity: 1;
  }

  .agent-row--active {
    background: var(--ds-color-neutral-a3);
    color: var(--ds-color-neutral-12);
  }

  .agent-row--active:hover {
    background: var(--ds-color-neutral-a4);
  }

  .agent-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 100%;
  }

  .agent-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    min-width: 0;
    gap: 2px;
  }

  .agent-details--left {
    display: inline-flex;
    gap: 6px;
    flex: 1;
    min-width: 0;
  }

  .agent-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: none;
    width: 14px;
  }
  .agent-icon {
    --icon-size: var(--ds-icon-size-1);
  }

  .thread-type-icon {
    flex: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 1em;
  }

  .pin-icon {
    display: flex;
    flex: none;
    align-items: center;
    justify-content: center;
    margin-inline: calc(var(--ds-spacing-0_5) * -1);
  }

  .repo-pill {
    display: inline-flex;
    align-items: center;
    flex: none;
    background-color: var(--ds-color-neutral-3);
    color: var(--ds-color-neutral-11);
    border-radius: var(--ds-radius-6);
    padding-inline: var(--ds-spacing-1);
    font-size: 0.7em;
    max-width: 80px;
    transition: max-width 0.2s ease-in-out;
    line-height: 1.2;
    vertical-align: middle;
    height: 18px;
  }

  .repo-pill-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2px;
    color: var(--ds-color-neutral-9);
  }
  .repo-pill-icon :global(svg) {
    width: 11px;
  }

  .repo-pill-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  @media (max-width: 300px) {
    .repo-pill {
      max-width: 19px;
    }
  }

  .repo-pill:hover {
    max-width: 200px;
  }

  .agent-title {
    max-width: 100%;
    min-width: 0;
    flex: 1;
    display: flex;
    align-items: center;
  }

  .agent-title-content {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 5px;
  }

  .agent-title :global(> .l-tooltip-trigger) {
    width: 100%;
    min-width: 0;
  }

  .agent-title-content :global(.c-text) {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }

  .agent-title-content :global(.setup-script-title),
  .agent-title-content :global(.draft-title) {
    color: var(--ds-color-neutral-11);
  }

  .setup-script-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-color-neutral-10);
    flex-shrink: 0;
  }

  .setup-script-badge :global(svg) {
    width: 14px;
    height: 14px;
  }

  .agent-actions {
    position: relative;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
  }

  .agent-actions :global(.l-tooltip-contents) {
    position: absolute;
    right: 0;
    bottom: 0;
    transform: translate(0, 100%);
    z-index: 10;
  }

  .agent-actions :global(.c-thread-menu__share-link) {
    white-space: nowrap;
  }

  .agent-status {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-left: var(--ds-spacing-1);
  }

  .update-indicator {
    --size: 4.5px;
    --gap: var(--ds-spacing-1);
    margin-right: var(--gap);
    margin-left: calc((var(--size) + var(--gap)) * -1);
    width: var(--size);
    height: var(--size);
    border-radius: 50%;
    background-color: var(--ds-color-accent-9);
  }
</style>
