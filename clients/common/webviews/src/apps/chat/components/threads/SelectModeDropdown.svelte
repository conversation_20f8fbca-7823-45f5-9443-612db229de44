<script lang="ts" context="module">
  export const NEW_THREADS_BUTTON_TEST_ID = "new-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_CHAT_TEST_ID = "new-chat-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_LOCAL_AGENT_TEST_ID = "new-local-agent-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_REMOTE_AGENT_TEST_ID = "new-remote-agent-thread-button";
</script>

<script lang="ts">
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import ModeDisplayDropdownItem from "$common-webviews/src/apps/chat/components/buttons/ModeDisplayDropdownItem.svelte";
  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getContext } from "svelte";
  import { getModeDisplayInfo } from "$common-webviews/src/apps/chat/components/buttons/mode-display-helper";
  import type { AgentConversationModel } from "$common-webviews/src/apps/chat/models/agent-conversation-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import {
    ModeSelectorAction,
    ModeSelectorMode,
    RemoteAgentNewThreadButtonAction,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import ToggleModeButtonContent from "../buttons/ToggleModeButtonContent.svelte";
  import { getThreadTypeFromMode } from "./utils";
  import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";

  // Get models from context
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);
  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");

  // Determine if background agents are enabled
  $: enableBackgroundAgents = chatModel ? $chatModel.flags.enableBackgroundAgents : false;
  $: enableAgentMode = chatModel ? $chatModel.flags.enableAgentMode : false;

  // Access store properties safely with proper reactive declarations
  $: isCurrConversationAgentic = agentConversationModel?.isCurrConversationAgentic;
  $: agentExecutionMode = chatModel?.agentExecutionMode;

  // Get current mode info for the main button icon with fallback values
  $: currentModeInfo = getModeDisplayInfo({
    isConversationAgentic: isCurrConversationAgentic ? $isCurrConversationAgentic : false,
    agentExecutionMode: agentExecutionMode ? $agentExecutionMode : undefined,
    isBackgroundAgent: remoteAgentsModel ? $remoteAgentsModel.isActive : false,
  });
  // Get tooltip text based on current mode
  $: tooltipText =
    currentModeInfo.type === "remoteAgent"
      ? "New Remote Agent"
      : currentModeInfo.type === "localAgent"
        ? "New Agent"
        : "New Chat";

  async function handleNewThreadButtonClick() {
    const threadType = getThreadTypeFromMode(currentModeInfo.type);
    await remoteAgentsModel.reportRemoteAgentNewThreadButtonEvent(
      RemoteAgentNewThreadButtonAction.click,
      threadType
    );

    chatModeModel.createThreadOfCurrentType();
    chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.THREAD_CREATION_ATTEMPTED, {
      source: "button",
    });
  }

  async function createNewCurrentTypeAgentThread() {
    const threadType = getThreadTypeFromMode(currentModeInfo.type);
    await remoteAgentsModel.reportRemoteAgentNewThreadButtonEvent(
      RemoteAgentNewThreadButtonAction.click,
      threadType
    );

    chatModeModel.createThreadOfCurrentType();
  }

  $: hasEverUsedAgent = agentConversationModel.hasEverUsedAgent;
  $: hasEverUsedRemoteAgent = remoteAgentsModel?.hasEverUsedRemoteAgent;

  let dropdownRoot: DropdownMenuRoot;

  let isDropdownOpen = false;

  function onOpenChange(open: boolean) {
    isDropdownOpen = open;

    // Log mode selector open/close events
    if (open) {
      chatModeModel.reportModeSelectorOpen();
    } else {
      chatModeModel.reportModeSelectorClose();
    }
  }

  function handleSetToChat() {
    // Log mode selection event directly from UI interaction
    chatModeModel.reportModeSelectorEvent(ModeSelectorAction.select, ModeSelectorMode.chat);
    chatModeModel.handleSetToChat();
    dropdownRoot?.requestClose();
  }

  async function handleSetToAgent() {
    // Log mode selection event directly from UI interaction
    await chatModeModel.reportModeSelectorEvent(ModeSelectorAction.select, ModeSelectorMode.agent);
    await chatModeModel.handleSetToAgent($agentExecutionMode);
    // Mark that the user has used the agent
    if ($hasEverUsedAgent === false) {
      agentConversationModel.setHasEverUsedAgent(true);
    }
    dropdownRoot?.requestClose();
  }

  function handleSetToBackgroundAgent() {
    // Log mode selection event directly from UI interaction
    chatModeModel.reportModeSelectorEvent(ModeSelectorAction.select, ModeSelectorMode.remoteAgent);

    chatModeModel.handleSetToBackgroundAgent();
    // Mark that the user has used a remote agent
    if ($hasEverUsedRemoteAgent === false) {
      remoteAgentsModel?.setHasEverUsedRemoteAgent(true);
    }
    dropdownRoot?.requestClose();
  }

  const color = "neutral";
</script>

<div class="c-select-mode-dropdown">
  {#if enableAgentMode || enableBackgroundAgents}
    <RegisterCommand name="newConversation" handler={createNewCurrentTypeAgentThread} />
    <DropdownMenu.Root bind:this={dropdownRoot} {onOpenChange} nested={false}>
      <DropdownMenu.Trigger
        on:click={(e) => {
          e.stopPropagation();
        }}
      >
        <ToggleModeButtonContent
          {isDropdownOpen}
          hideText
          isCurrConversationAgentic={$isCurrConversationAgentic}
          agentExecutionMode={$agentExecutionMode}
          isBackgroundAgent={$remoteAgentsModel?.isActive}
          variant="ghost"
          {color}
        />
      </DropdownMenu.Trigger>
      <DropdownMenu.Content size={1} side="bottom" align="start">
        <div class="c-mode-display-dropdown">
          <ModeDisplayDropdownItem
            onSelect={handleSetToChat}
            isConversationAgentic={false}
            selected={currentModeInfo.type === "chat"}
          />
          {#if enableAgentMode}
            <ModeDisplayDropdownItem
              onSelect={handleSetToAgent}
              isConversationAgentic={true}
              selected={currentModeInfo.type === "localAgent"}
            />
          {/if}
          {#if enableBackgroundAgents}
            <ModeDisplayDropdownItem
              onSelect={handleSetToBackgroundAgent}
              isBackgroundAgent={true}
              selected={currentModeInfo.type === "remoteAgent"}
            />
          {/if}
        </div>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  {/if}

  <TextTooltipAugment side="top" nested={false}>
    <div slot="content" class="c-select-mode-dropdown__tooltip">
      {tooltipText}
      <ShortcutHint commandName="newConversation" keysOnly />
    </div>
    <IconButtonAugment
      size={0.5}
      variant="ghost-block"
      {color}
      on:click={handleNewThreadButtonClick}
      class="c-select-mode-dropdown__new-thread-button"
      data-testid={NEW_THREADS_BUTTON_TEST_ID}
    >
      <GuardedIcon name="plus"><Plus /></GuardedIcon>
    </IconButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-select-mode-dropdown {
    display: inline-flex;
    align-items: center;

    :global(.c-select-mode-dropdown__new-thread-button svg) {
      --button-icon-size: 12px;
    }
  }

  .c-select-mode-dropdown__tooltip {
    --keyboard-shortcut-hint-border: currentColor;
    --keyboard-shortcut-hint-color: currentColor;

    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
