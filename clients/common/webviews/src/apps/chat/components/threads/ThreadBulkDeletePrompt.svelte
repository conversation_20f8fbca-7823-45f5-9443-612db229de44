<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";
  import { OLD_THREAD_THRESHOLD_DAYS } from "./ThreadsPanel.svelte";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  interface Props {
    bulkDeleteCallback: () => void;
    dismissBulkDeletePrompt: () => void;
  }

  let { bulkDeleteCallback, dismissBulkDeletePrompt }: Props = $props();
</script>

<div class="c-thread-bulk-delete-prompt">
  <CalloutAugment variant="soft" color="neutral" size={1} padding="var(--ds-spacing-3)">
    {#snippet icon()}
      <GuardedIcon name="info"><InfoCircled /></GuardedIcon>
    {/snippet}
    <div class="c-thread-bulk-delete-prompt__content">
      Deleting inactive threads improves performance.

      <div class="c-thread-bulk-delete-prompt__action">
        <ButtonAugment variant="soft" color="neutral" size={1} on:click={bulkDeleteCallback}
          >Delete Threads Older Than {OLD_THREAD_THRESHOLD_DAYS} Days</ButtonAugment
        >
      </div>
    </div>

    <div class="c-thread-bulk-delete-prompt__dismiss">
      <IconButtonAugment
        on:click={dismissBulkDeletePrompt}
        variant="ghost-block"
        color="neutral"
        size={0.5}
      >
        <Icon name="x" />
      </IconButtonAugment>
    </div>
  </CalloutAugment>
</div>

<style>
  .c-thread-bulk-delete-prompt {
    font-size: var(--ds-font-size-1);
    margin: 0 0 0 var(--ds-spacing-1);
    position: relative;
  }
  .c-thread-bulk-delete-prompt__content {
    padding-left: var(--ds-spacing-1);
  }
  .c-thread-bulk-delete-prompt__action {
    margin-top: var(--ds-spacing-2);
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
  }
  .c-thread-bulk-delete-prompt__dismiss {
    position: absolute;
    top: var(--ds-spacing-1);
    right: var(--ds-spacing-1);
  }
</style>
