<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";
  import ContextEngineToolComponent from "../ContextEngineToolComponent.svelte";

  export let toolUseInput: Record<string, unknown> = {};
</script>

<ContextEngineToolComponent retrieval="Commit History" {toolUseInput}>
  <TextAugment size={1} color="neutral" slot="retrieval">
    <Icon name="git-commit-horizontal" />Commit History
  </TextAugment>
</ContextEngineToolComponent>
