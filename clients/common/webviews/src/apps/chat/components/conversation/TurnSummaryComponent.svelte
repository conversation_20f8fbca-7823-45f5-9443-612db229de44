<script lang="ts">
  import {
    ChatResultNodeType,
    type ChatResultNode,
  } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import {
    countUniquePaths,
    isMemoryNode,
    isFileChangeToolNode,
    isViewToolNode,
    flattenGroupIntoNodesCached,
  } from "../../types/chat-message";

  import { type GroupedChatItem } from "../../utils/message-list-context";
  import TurnSummary, { type SummaryEntry } from "../turn-summary/TurnSummary.svelte";
  import { memoryModel } from "../../models/memory-model";
  import { getChatModel } from "../../contexts/chat-model-context";
  import {
    ChatThreadBodyModel,
    ChatThreadBodyTab,
  } from "../chat-thread-body/chat-thread-body-model";
  import { BUTTON_LABELS } from "../turn-summary/constants";

  const chatModel = getChatModel();
  const { pendingMemoriesCount } = memoryModel;

  // Get thread body model for navigation
  const threadBodyModel = ChatThreadBodyModel.getFromContext();
  const { availableTabs } = threadBodyModel;

  // Remove entriesSum calculation since we don't need the key block anymore

  interface Props {
    group: GroupedChatItem | undefined;
    // Hide the component if there are no memories to show
    isTurnSummaryVisible?: boolean;
  }

  let { group, isTurnSummaryVisible = $bindable(false) }: Props = $props();

  function navigateToMemoryTab() {
    if (chatModel?.flags?.enableMemoryRetrieval) {
      memoryModel.openModal();
    }
  }

  function navigateToChangesTab() {
    // Check if Changes tab is available before navigating
    if (hasEditTab) {
      threadBodyModel.setActiveTab(ChatThreadBodyTab.agentEdits);
    }
  }
  let hasEditTab = $derived(
    chatModel.flags.enableAgentTabs && $availableTabs.includes(ChatThreadBodyTab.agentEdits)
  );
  // Optimize by doing all filtering and counting in a single pass
  let conversationStats = $derived(
    (() => {
      const allNodes = flattenGroupIntoNodesCached(group) || [];
      const strReplaceNodes: ChatResultNode[] = [];
      const viewNodes: ChatResultNode[] = [];
      let memoryCount = 0;
      let toolUseCount = 0;

      for (const node of allNodes) {
        // Only count memory nodes if memory retrieval is enabled
        if (chatModel?.flags?.enableMemoryRetrieval && isMemoryNode(node)) {
          memoryCount++;
        }
        if (node.type === ChatResultNodeType.TOOL_USE) {
          toolUseCount++;
        }
        if (isFileChangeToolNode(node)) {
          strReplaceNodes.push(node);
        }
        if (isViewToolNode(node)) {
          viewNodes.push(node);
        }
      }

      return {
        conversationMemoryCount: memoryCount,
        conversationToolUseCount: toolUseCount,
        conversationStrReplaceFiles: countUniquePaths(strReplaceNodes),
        conversationViewFiles: countUniquePaths(viewNodes),
      };
    })()
  );

  const memoryRetrievalEnabled = $derived(chatModel?.flags?.enableMemoryRetrieval);
  let entries = $derived<SummaryEntry[]>(
    (memoryRetrievalEnabled
      ? ([
          {
            singularLabel: BUTTON_LABELS.PENDING_MEMORY.SINGULAR,
            pluralLabel: BUTTON_LABELS.PENDING_MEMORY.PLURAL,
            value: $pendingMemoriesCount,
            icon: "archive",
            callback: () => navigateToMemoryTab(),
          },
          {
            singularLabel: BUTTON_LABELS.MEMORY_CREATED.SINGULAR,
            pluralLabel: BUTTON_LABELS.MEMORY_CREATED.PLURAL,
            value: conversationStats.conversationMemoryCount,
            icon: "clipboard-check",
          },
        ] as SummaryEntry[])
      : []
    ).concat([
      // Memory-related entries - only show if enableMemoryRetrieval flag is enabled
      // Non-memory entries - always show
      {
        singularLabel: BUTTON_LABELS.FILE_CHANGED.SINGULAR,
        pluralLabel: BUTTON_LABELS.FILE_CHANGED.PLURAL,
        value: conversationStats.conversationStrReplaceFiles,
        icon: "file-diff",
        callback: hasEditTab ? () => navigateToChangesTab() : undefined,
      },
      {
        singularLabel: BUTTON_LABELS.FILE_EXAMINED.SINGULAR,
        pluralLabel: BUTTON_LABELS.FILE_EXAMINED.PLURAL,
        value: conversationStats.conversationViewFiles,
        icon: "file-search-2",
      },
      {
        singularLabel: BUTTON_LABELS.TOOL_USED.SINGULAR,
        pluralLabel: BUTTON_LABELS.TOOL_USED.PLURAL,
        value: conversationStats.conversationToolUseCount,
        icon: "pencil-ruler",
      },
    ])
  );
  $effect(() => {
    isTurnSummaryVisible = entries.some((entry) => entry.value > 0);
  });
</script>

{#if isTurnSummaryVisible}
  <TurnSummary title="Turn Summary" {entries} />
{/if}
