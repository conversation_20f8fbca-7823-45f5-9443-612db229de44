<script module lang="ts">
  export type TooltipItem = {
    label: string;
    action: (e: Event) => void;
    id: string;
    disabled: boolean;
    icon: any;
    lucideIconName?: IconName;
    successMessage?: string;
  };
</script>

<script lang="ts">
  import SuccessfulButton from "$common-webviews/src/apps/chat/components/buttons/SuccessfulButton.svelte";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import { type IconName } from "$common-webviews/src/design-system/icons/lucide-icon-registry";

  interface Props {
    items: Array<TooltipItem>;
  }

  let { items }: Props = $props();

  function getIconName(item: TooltipItem): IconName | undefined {
    if (item.lucideIconName) {
      return item.lucideIconName;
    }

    const { icon } = item;
    // Try to get the icon name from the component's name or other properties
    // This is a simplified approach - you might need to adjust based on actual icon component structure
    const iconString = icon?.toString() || "";
    if (iconString.includes("pencil")) return "pencil";
    if (iconString.includes("copy")) return "copy";
    return undefined;
  }
</script>

<div class="c-message-actions">
  {#each items as item}
    <SuccessfulButton
      data-testid={`${item.id}-button`}
      variant="ghost-block"
      size={1}
      defaultColor="neutral"
      disabled={item.disabled}
      icon
      stickyColor={false}
      onClick={(e) => {
        item.action(e);
        return item.successMessage ? "success" : "neutral";
      }}
      tooltip={{
        neutral: item.label,
        success: item.successMessage,
      }}
    >
      {@const iconName = getIconName(item)}
      {#if iconName}
        <GuardedIcon name={iconName}>
          <item.icon />
        </GuardedIcon>
      {:else}
        <item.icon />
      {/if}
    </SuccessfulButton>
  {/each}
</div>

<style>
  .c-message-actions {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
  }

  .c-message-actions :global(svg) {
    width: 16px;
    height: 16px;
  }
</style>
