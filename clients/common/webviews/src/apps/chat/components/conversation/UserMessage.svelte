<script lang="ts">
  import type { JSONContent } from "@tiptap/core";
  import ChatMessage from "../ChatMessage.svelte";
  import { type ChatModel } from "../../models/chat-model";
  import { ExchangeStatus } from "../../types/chat-message";
  import ChatEditMessageInput from "../ChatInput/ChatEditMessageInput.svelte";
  import { type ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import { type IChatMentionable } from "../../types/mention-option";
  import MessageActions from "./MessageActions.svelte";
  import Edit from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pencil.svg?component";
  import { tick } from "svelte";
  import ResizeObserver from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/ResizeObserver/ResizeObserver.svelte";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import CopyIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/copy.svg?component";
  import { type IconName } from "$common-webviews/src/design-system/icons/lucide-icon-registry";

  interface Props {
    chatModel: ChatModel;
    msg: string;
    requestId?: string | undefined;
    richTextJsonRepr?: JSONContent | JSONContent[] | undefined;
    timestamp?: string | undefined;
    // Lifecycle callbacks
    onStartEdit?: () => void;
    onAcceptEdit?: () => void;
    onCancelEdit?: () => void;
  }

  let {
    chatModel,
    msg,
    requestId = undefined,
    richTextJsonRepr = undefined,
    timestamp = undefined,
    onStartEdit = () => {},
    onAcceptEdit = () => {},
    onCancelEdit = () => {},
  }: Props = $props();

  const remoteAgentModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  const COLLAPSED_MAX_HEIGHT = 120;

  // Only collapse if not the last message and message is long enough
  let isCollapsed = $state(false);
  let userExpanded = $state(false);

  async function setIsCollapsed(c: boolean) {
    await tick();
    isCollapsed = c && canCollapse && !userExpanded;
  }

  let isEditing = $state(false);

  const startEdit = (e: Event) => {
    if (!canEdit || !requestId) {
      return;
    }

    // Always show full content when entering edit mode
    setIsCollapsed(false);
    dsEditor?.requestStartEdit();
    onStartEdit();
    e?.stopPropagation();
  };

  const copyRequestId = async (e: Event) => {
    if (!requestId) {
      return;
    }
    await navigator.clipboard.writeText(requestId);
    e.stopPropagation();
  };

  const cancelEdit = () => {
    onCancelEdit();
  };

  function onSubmitEdit(data: ContentData, mentions: IChatMentionable[]) {
    if (!requestId) {
      return;
    }

    chatModel.currentConversationModel.clearHistoryFrom(requestId);

    const structuredRequestNodes =
      $chatModel.flags.enableChatMultimodal && data.richTextJsonRepr
        ? chatModel.currentConversationModel.createStructuredRequestNodes(data.richTextJsonRepr)
        : undefined;

    // Send a new exchange to the real conversation model
    chatModel.currentConversationModel.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: data.rawText,
      rich_text_json_repr: data.richTextJsonRepr,
      status: ExchangeStatus.draft,
      mentioned_items: mentions,
      structured_request_nodes: structuredRequestNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
    onAcceptEdit();
  }

  let dsEditor: ChatEditMessageInput | undefined = $state(undefined);
  let showExpandButton = $state(false);

  function handleEditorHeightChange() {
    if (!dsEditor?.getEditorContainer() || !editorHeight || !canCollapse) return;
    if (isCollapsed && editorHeight < COLLAPSED_MAX_HEIGHT) {
      setIsCollapsed(false);
    } else {
      setIsCollapsed(editorHeight > COLLAPSED_MAX_HEIGHT);
    }
    showExpandButton = editorHeight > COLLAPSED_MAX_HEIGHT;
  }

  let isRemoteAgent = $derived(!!remoteAgentModel?.isActive);
  let isLastMessage = $derived(
    requestId === undefined ||
      requestId === $chatModel?.currentConversationModel?.lastExchange?.request_id
  );
  let canCollapse = $derived(!isLastMessage && !isEditing);
  let editorHeight = $state(0);

  let canEdit = $derived(
    requestId !== undefined &&
      $chatModel.flags.fullFeatured &&
      $chatModel.flags.enableEditableHistory &&
      !isRemoteAgent
  );
  let tooltipItems = $derived([
    ...(canEdit && !isEditing
      ? [
          ...($chatModel.flags.enableDebugFeatures
            ? [
                {
                  label: "Copy request ID",
                  action: copyRequestId,
                  id: "copy-request-id",
                  disabled: false,
                  icon: CopyIcon,
                  lucideIconName: "clipboard" as IconName,
                  successMessage: "Copied!",
                },
              ]
            : []),
          {
            label: "Edit message",
            action: startEdit,
            id: "edit-message",
            disabled: false,
            icon: Edit,
            lucideIconName: "pencil" as IconName,
          },
        ]
      : []),
  ]);
  $effect(() => {
    if (editorHeight && canCollapse) handleEditorHeightChange();
  });
</script>

<ChatMessage bind:isEditing {timestamp}>
  {#snippet content()}
    <ChatEditMessageInput
      bind:this={dsEditor}
      bind:isEditing
      collapsed={isCollapsed}
      {showExpandButton}
      content={richTextJsonRepr ?? msg}
      collapsedMaxHeight={COLLAPSED_MAX_HEIGHT}
      {requestId}
      {onSubmitEdit}
      onCancelEdit={cancelEdit}
      {setIsCollapsed}
      bind:userExpanded
    >
      <ResizeObserver bind:height={editorHeight} />
    </ChatEditMessageInput>
  {/snippet}

  {#snippet userActions()}
    <div class="c-user-msg__actions">
      {#if tooltipItems.length > 0}
        <MessageActions items={tooltipItems} />
      {/if}
    </div>
  {/snippet}
</ChatMessage>

<style>
  .c-user-msg__actions {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }

  /* Also show actions when they're being interacted with */
  :global(.c-chat-message):hover .c-user-msg__actions {
    opacity: 1;
  }

  .c-user-msg__actions:hover,
  .c-user-msg__actions:focus-within {
    opacity: 1;
  }
</style>
