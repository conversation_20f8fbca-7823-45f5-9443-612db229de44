<script lang="ts">
  import { getContext } from "svelte";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import ContextEngineToolComponent from "../ContextEngineToolComponent.svelte";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  export let toolUseInput: Record<string, unknown> = {};

  const chatModel = getContext<ChatModel>("chatModel");
  $: flagsModel = chatModel?.flags;
</script>

<ContextEngineToolComponent {toolUseInput} noRetrieval={!$flagsModel?.enableCommitIndexing}>
  <svelte:fragment slot="retrieval">
    {#if $flagsModel?.enableCommitIndexing}
      <Icon name="code" /> Codebase
    {/if}
  </svelte:fragment>
</ContextEngineToolComponent>
