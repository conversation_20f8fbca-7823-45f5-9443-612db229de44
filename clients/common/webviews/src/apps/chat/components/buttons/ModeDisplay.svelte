<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { type AgentExecutionMode } from "../../models/chat-model";
  import { getModeDisplayInfo } from "./mode-display-helper";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";

  interface Props {
    isConversationAgentic?: boolean;
    agentExecutionMode?: AgentExecutionMode | undefined;
    isBackgroundAgent?: boolean;
    description?: string;
    doShowSecondaryText?: boolean;
  }

  let {
    isConversationAgentic = false,
    agentExecutionMode = undefined,
    isBackgroundAgent = false,
    description = "",
    doShowSecondaryText = true,
  }: Props = $props();

  let modeInfo = $derived(
    getModeDisplayInfo({
      isConversationAgentic,
      agentExecutionMode,
      isBackgroundAgent,
    })
  );
</script>

<div class="c-mode-display">
  <div class="c-mode-display__top">
    <GuardedIcon name={modeInfo.lucideIcon}>
      <modeInfo.icon />
    </GuardedIcon>
    <TextAugment size={1}>{modeInfo.primaryText}</TextAugment>
    {#if modeInfo.secondaryText}
      <TextAugment size={1} class="c-mode-secondary-label">{modeInfo.secondaryText}</TextAugment>
    {/if}
  </div>
  {#if description}
    <div class="c-mode-display__bottom" class:c-mode-display__bottom--hidden={!doShowSecondaryText}>
      <TextAugment size={0} class="c-mode-secondary-label" color="secondary">
        {description}
      </TextAugment>
    </div>
  {/if}
</div>

<style>
  .c-mode-display {
    text-wrap: nowrap;
  }

  .c-mode-display__top {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-mode-display__top :global(svg) {
    flex: none;
    height: var(--ds-icon-size-0);
    width: var(--ds-icon-size-0);
  }

  .c-mode-display__bottom {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .c-mode-display__bottom--hidden {
    height: 0;
    overflow: hidden;
  }

  .c-mode-display :global(.c-mode-right-label) {
    display: flex;
    margin-left: auto;
    flex: 1;
    justify-content: flex-end;
  }

  :global(.c-base-btn--solid:hover .c-mode-right-label__text) {
    opacity: 1;
    transform: translateX(0);
  }
</style>
