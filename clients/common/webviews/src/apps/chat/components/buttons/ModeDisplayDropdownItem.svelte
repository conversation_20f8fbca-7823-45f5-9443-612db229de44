<script lang="ts">
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { type AgentExecutionMode } from "../../models/chat-model";
  import ModeDisplay from "./ModeDisplay.svelte";

  interface Props {
    onSelect: (e: Event) => void;
    description?: string;
    isConversationAgentic?: boolean;
    agentExecutionMode?: AgentExecutionMode | undefined;
    selected?: boolean;
    isBackgroundAgent?: boolean;
    [key: string]: any;
  }

  let {
    onSelect,
    description = "",
    isConversationAgentic = false,
    agentExecutionMode = undefined,
    selected = false,
    isBackgroundAgent = false,
    // SVELTE5_MIGRATION
    // eslint-disable-next-line svelte/valid-compile
    ...rest
  }: Props = $props();

  let { class: restClassName = "", ...restProps } = $derived(rest);
</script>

<div
  class="c-mode-display-item-container {restClassName}"
  class:c-mode-display-item--selected={selected}
>
  <DropdownMenu.Item {onSelect} {...restProps}>
    <div class="c-mode-display-item">
      <ModeDisplay {isConversationAgentic} {agentExecutionMode} {isBackgroundAgent} {description} />
    </div>
  </DropdownMenu.Item>
</div>

<style>
  .c-mode-display-item-container,
  .c-mode-display-item,
  .c-mode-display-item-container :global(.l-tooltip-trigger),
  .c-mode-display-item-container :global(.c-base-btn) {
    width: 100%;
  }
  .c-mode-display-item--selected :global(.c-base-btn--ghost) {
    --base-btn-bg-color: var(--ds-color-a3);
  }
</style>
