<script lang="ts">
  import AnimatedNumberIndicatorAugment from "$common-webviews/src/design-system/components/AnimatedNumberIndicatorAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { SummaryEntry } from "./types";
  import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
  import { getEventTracker } from "../../contexts/event-tracker-context";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";
  import { type IconName } from "$common-webviews/src/design-system/icons/lucide-icon-registry";

  export let entry: SummaryEntry;
  export let index: number;
  export let animationResetKey: number;
  export let shouldStartNumbers: boolean;
  export let fadeInDelay: number;

  const eventTracker = getEventTracker();

  function handleClick() {
    // Track the button click with label and value
    eventTracker?.trackEvent(ANALYTICS_EVENTS.TURN_SUMMARY_BUTTON_CLICKED, {
      buttonName: entry.singularLabel, // Make this singularLabel so that events gets rolled up
      value: entry.value,
    });

    // Execute the original callback if provided
    if (entry.callback) {
      entry.callback();
    }
  }

  const ICONS_MAP: Record<string, IconName> = {
    difference: "file-diff",
  };
</script>

{#snippet entryContent()}
  <Icon name={ICONS_MAP[entry.icon] || entry.icon} class="c-turn-summary__item__icon" />
  {#key `${animationResetKey}-${index}-${entry.value}`}
    <AnimatedNumberIndicatorAugment
      value={entry.value}
      size={1}
      autoStart={shouldStartNumbers}
      delay={index * fadeInDelay * 1.1}
      skipAnimation={entry.skipAnimation}
    />
  {/key}
  <TextAugment size={1} color="secondary">
    {entry.value === 1 ? entry.singularLabel : entry.pluralLabel}
  </TextAugment>
{/snippet}

{#if entry.callback}
  <button class="c-turn-summary-item__link" on:click={handleClick} type="button">
    {@render entryContent()}
  </button>
{:else}
  <span class="c-turn-summary-item__content">
    {@render entryContent()}
  </span>
{/if}

<style>
  .c-turn-summary-item__content,
  .c-turn-summary-item__link {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-turn-summary-item__link {
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    color: inherit;

    &:hover {
      color: var(--ds-color-accent-a11);

      &:global(.c-animated-number-indicator) {
        color: var(--ds-color-accent-a11);
      }
    }

    &:active {
      transform: translateY(-1px);
    }
  }

  .c-turn-summary-item__link:hover :global(.c-text) {
    color: var(--ds-color-accent-a11);
  }
</style>
