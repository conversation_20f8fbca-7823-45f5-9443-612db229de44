import { render, screen, fireEvent } from "@testing-library/svelte";
import { vi, describe, it, expect, beforeEach, afterEach, type MockInstance } from "vitest";
import { tick } from "svelte";

// Mock the visibility observer before importing the component
vi.mock("$common-webviews/src/apps/chat/components/actions/trackOnScreen", () => ({
  visibilityObserverOnce: vi.fn(() => ({ destroy: vi.fn() })),
}));

// Mock the event tracker
const mockTrackEvent = vi.fn();
vi.mock("../../contexts/event-tracker-context", () => ({
  getEventTracker: vi.fn(() => ({
    trackEvent: mockTrackEvent,
  })),
}));

import TurnSummary from "./TurnSummary.svelte";
import type { SummaryEntry } from "./types";

describe("TurnSummary", () => {
  let mockVisibilityObserverOnce: ReturnType<typeof vi.fn>;
  let mockSetTimeout: MockInstance<typeof setTimeout>;

  const mockEntries: SummaryEntry[] = [
    { singularLabel: "File Changed", pluralLabel: "Files Changed", value: 3, icon: "file-diff" },
    { singularLabel: "Line Added", pluralLabel: "Lines Added", value: 15, icon: "square-plus" },
    { singularLabel: "Tool Used", pluralLabel: "Tools Used", value: 2, icon: "pencil-ruler" },
  ];

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get the mocked function
    const trackOnScreen = await import(
      "$common-webviews/src/apps/chat/components/actions/trackOnScreen"
    );
    mockVisibilityObserverOnce = trackOnScreen.visibilityObserverOnce as ReturnType<typeof vi.fn>;

    // Mock setTimeout to control timing
    mockSetTimeout = vi.spyOn(global, "setTimeout");

    // Mock getBoundingClientRect
    Element.prototype.getBoundingClientRect = vi.fn(() => ({
      top: 100,
      bottom: 200,
      left: 0,
      right: 100,
      width: 100,
      height: 100,
      x: 0,
      y: 100,
      toJSON: () => {},
    }));

    // Mock window.innerHeight
    Object.defineProperty(window, "innerHeight", {
      writable: true,
      configurable: true,
      value: 1000,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders with entries", async () => {
    render(TurnSummary, {
      props: { entries: mockEntries, title: "Test Summary" },
    });

    await tick();

    expect(screen.getByText("Test Summary")).toBeTruthy();
    expect(screen.getByText("Files Changed")).toBeTruthy();
    expect(screen.getByText("Lines Added")).toBeTruthy();
    expect(screen.getByText("Tools Used")).toBeTruthy();
  });

  it("filters out entries with zero values", async () => {
    const entriesWithZeros: SummaryEntry[] = [
      { singularLabel: "File Changed", pluralLabel: "Files Changed", value: 3, icon: "file-diff" },
      { singularLabel: "Zero Value", pluralLabel: "Zero Values", value: 0, icon: "square-plus" },
      { singularLabel: "Tool Used", pluralLabel: "Tools Used", value: 2, icon: "pencil-ruler" },
    ];

    render(TurnSummary, {
      props: { entries: entriesWithZeros, title: "Test Summary" },
    });

    await tick();

    expect(screen.getByText("Files Changed")).toBeTruthy();
    expect(screen.getByText("Tools Used")).toBeTruthy();
    expect(screen.queryByText("Zero Value")).toBeNull();
  });

  it("handles null containerElement gracefully in checkInitialVisibility", async () => {
    const { unmount } = render(TurnSummary, {
      props: { entries: mockEntries, title: "Test Summary" },
    });

    await tick();

    // Verify that setTimeout was called for checkInitialVisibility
    expect(mockSetTimeout).toHaveBeenCalled();

    // Find the checkInitialVisibility callback
    const setTimeoutCalls = mockSetTimeout.mock.calls;
    const checkInitialVisibilityCall = setTimeoutCalls.find(
      (call) => call[1] === 100 // The 100ms delay for checkInitialVisibility
    );

    expect(checkInitialVisibilityCall).toBeTruthy();

    // Simulate the scenario where containerElement becomes null
    // by unmounting the component before the timeout fires
    unmount();

    // Execute the checkInitialVisibility callback - should not throw
    expect(() => {
      if (checkInitialVisibilityCall) {
        checkInitialVisibilityCall[0](); // Execute the callback
      }
    }).not.toThrow();
  });

  it("sets up visibility observer on mount", async () => {
    render(TurnSummary, {
      props: { entries: mockEntries, title: "Test Summary" },
    });

    await tick();

    expect(mockVisibilityObserverOnce).toHaveBeenCalledWith(
      expect.any(HTMLElement),
      expect.objectContaining({
        scrollTarget: document.body,
        threshold: 0.25,
        visibilityDuration: 100,
        onVisible: expect.any(Function),
      })
    );
  });

  it("sets up timeouts correctly", async () => {
    const { unmount } = render(TurnSummary, {
      props: { entries: mockEntries, title: "Test Summary" },
    });

    await tick();

    // Verify that setTimeout was called for component initialization
    expect(mockSetTimeout).toHaveBeenCalled();

    // Verify that the component renders without errors
    expect(screen.getByText("Test Summary")).toBeTruthy();

    unmount();
  });

  it("handles empty entries array", async () => {
    render(TurnSummary, {
      props: { entries: [], title: "Empty Summary" },
    });

    await tick();

    expect(screen.getByText("Empty Summary")).toBeTruthy();
    // Should not render any entry components
    expect(screen.queryByText("Files Changed")).toBeNull();
  });

  it("calculates second column start index correctly", async () => {
    const manyEntries: SummaryEntry[] = [
      { singularLabel: "Entry 1", pluralLabel: "Entries 1", value: 1, icon: "file-text" },
      { singularLabel: "Entry 2", pluralLabel: "Entries 2", value: 2, icon: "file-plus" },
      { singularLabel: "Entry 3", pluralLabel: "Entries 3", value: 3, icon: "plus" },
      { singularLabel: "Entry 4", pluralLabel: "Entries 4", value: 4, icon: "x" },
      { singularLabel: "Entry 5", pluralLabel: "Entries 5", value: 5, icon: "file-diff" },
    ];

    render(TurnSummary, {
      props: { entries: manyEntries, title: "Many Entries" },
    });

    await tick();

    // With 5 entries, second column should start at index 3 (Math.ceil(5/2) = 3)
    expect(screen.getByText("Entry 1")).toBeTruthy();
    expect(screen.getByText("Entries 5")).toBeTruthy();
  });

  describe("analytics tracking", () => {
    beforeEach(() => {
      mockTrackEvent.mockClear();
    });

    it("tracks clicks on interactive buttons with singular labels", async () => {
      const mockCallback = vi.fn();
      const interactiveEntries: SummaryEntry[] = [
        {
          singularLabel: "File Changed",
          pluralLabel: "Files Changed",
          value: 1,
          icon: "file-diff",
          callback: mockCallback,
        },
      ];

      render(TurnSummary, {
        props: { entries: interactiveEntries },
      });

      await tick();

      const button = screen.getByRole("button", { name: /File Changed/i });
      await fireEvent.click(button);

      expect(mockTrackEvent).toHaveBeenCalledWith("turn_summary_button_clicked", {
        buttonName: "File Changed",
        value: 1,
      });
      expect(mockCallback).toHaveBeenCalled();
    });

    it("tracks clicks on interactive buttons with plural labels", async () => {
      const mockCallback = vi.fn();
      const interactiveEntries: SummaryEntry[] = [
        {
          singularLabel: "File Changed",
          pluralLabel: "Files Changed",
          value: 3,
          icon: "file-diff",
          callback: mockCallback,
        },
      ];

      render(TurnSummary, {
        props: { entries: interactiveEntries },
      });

      await tick();

      const button = screen.getByRole("button", { name: /Files Changed/i });
      await fireEvent.click(button);

      expect(mockTrackEvent).toHaveBeenCalledWith("turn_summary_button_clicked", {
        buttonName: "File Changed",
        value: 3,
      });
      expect(mockCallback).toHaveBeenCalled();
    });

    it("does not track clicks on non-interactive entries", async () => {
      const nonInteractiveEntries: SummaryEntry[] = [
        {
          singularLabel: "File Examined",
          pluralLabel: "Files Examined",
          value: 2,
          icon: "file-search-2",
        },
      ];

      render(TurnSummary, { props: { entries: nonInteractiveEntries } });

      await tick();

      const span = screen.getByText("Files Examined");
      expect(span.tagName).toBe("SPAN");
      expect(mockTrackEvent).not.toHaveBeenCalled();
    });

    it("correctly displays singular labels for count = 1", async () => {
      const singleEntry: SummaryEntry[] = [
        {
          singularLabel: "Tool Used",
          pluralLabel: "Tools Used",
          value: 1,
          icon: "pencil-ruler",
        },
      ];

      render(TurnSummary, { props: { entries: singleEntry } });
      await tick();

      expect(screen.getByText("Tool Used")).toBeInTheDocument();
      expect(screen.queryByText("Tools Used")).not.toBeInTheDocument();
    });

    it("correctly displays plural labels for count > 1", async () => {
      const pluralEntry: SummaryEntry[] = [
        {
          singularLabel: "Tool Used",
          pluralLabel: "Tools Used",
          value: 3,
          icon: "pencil-ruler",
        },
      ];

      render(TurnSummary, { props: { entries: pluralEntry } });
      await tick();

      expect(screen.getByText("Tools Used")).toBeInTheDocument();
      expect(screen.queryByText("Tool Used")).not.toBeInTheDocument();
    });
  });
});
