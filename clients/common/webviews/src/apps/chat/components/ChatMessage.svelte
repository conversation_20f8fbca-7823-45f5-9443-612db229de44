<script lang="ts">
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import { getContext } from "svelte";
  import { type ChatModel } from "../models/chat-model";
  import TimestampDisplay from "$common-webviews/src/design-system/components/TimestampDisplay.svelte";
  import ConversationNavigation from "./conversation/navigation";
  import { type Snippet } from "svelte";

  interface Props {
    isAugment?: boolean;
    timestamp?: string | undefined;
    isEditing?: boolean;
    content?: Snippet;
    userActions?: Snippet;
    footer?: Snippet;
    edit?: Snippet;
  }

  let {
    isAugment = false,
    timestamp = undefined,
    isEditing = $bindable(false),
    content,
    userActions,
    footer,
    edit,
  }: Props = $props();

  const chatModel = getContext<ChatModel | undefined>("chatModel");
  const flags = chatModel?.flags;
</script>

<div class="c-chat-message" class:c-chat-message--augment={isAugment}>
  {#if isAugment}
    <div class="c-chat-message__content">
      {@render content?.()}
    </div>
  {:else}
    <ConversationNavigation.Item id={String(timestamp)}>
      <TimestampDisplay {timestamp} />
    </ConversationNavigation.Item>
    <div class="c-chat-message--user">
      {#if userActions}
        <div class="c-chat-message__user-actions">
          {@render userActions?.()}
        </div>
      {/if}

      <CalloutAugment
        --callout-flex-direction="column"
        --callout-gap="var(--ds-spacing-4)"
        --callout-body-overflow="hidden"
        --callout-width={isEditing ? `100%` : "unset"}
        --callout-max-width="100%"
        --callout-padding="var(--ds-spacing-1)"
        --text-font-size="0.75rem"
        --text-line-height="1rem"
        --text-letter-spacing="0.0025em"
        color="neutral"
        size={1}
        variant="ghost"
        class={`c-chat-message__content ${flags?.enableDesignSystemRichTextEditor ? "c-chat-message__content--rich-text" : ""}`}
      >
        {@render content?.()}
      </CalloutAugment>
    </div>
  {/if}
</div>

{@render footer?.()}
{@render edit?.()}

<style>
  .c-chat-message {
    display: flex;
    flex-direction: column;
    gap: 0;

    width: 100%;

    align-items: end;
  }

  .c-chat-message__content {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--ds-spacing-4);
  }

  .c-chat-message--user {
    display: flex;
    flex-direction: row;
    gap: 0;
    align-items: flex-start;
    justify-content: flex-end;
    width: 100%;
  }

  .c-chat-message__user-actions {
    display: flex;
    gap: var(--ds-spacing-1);
    padding-right: var(--ds-spacing-1);
    padding-top: var(--ds-spacing-3);
  }

  .c-chat-message--augment {
    align-items: start;
  }

  :not(.c-chat-message--augment) .c-chat-message__content {
    padding: var(--ds-spacing-1);
    border-radius: var(--ds-radius-2);
  }

  .c-chat-message--augment :global(.c-timestamp-display) {
    /* Align to left for augment's timestamp */
    padding-left: 0;
  }
</style>
