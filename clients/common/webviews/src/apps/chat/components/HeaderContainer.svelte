<script lang="ts">
  import type { Snippet } from "svelte";

  interface Props {
    children: Snippet;
  }

  let { children }: Props = $props();
</script>

<div class="l-header-container">
  {@render children()}
</div>

<style>
  .l-header-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    padding-inline: var(--chat-padding);
    padding-block: var(--ds-spacing-1) var(--ds-spacing-2);
    margin-inline: calc(var(--chat-padding) * -1);
    border-bottom: 1px solid var(--ds-color-neutral-a4);
  }

  .l-header-container :global(svg) {
    opacity: 1;
  }
</style>
