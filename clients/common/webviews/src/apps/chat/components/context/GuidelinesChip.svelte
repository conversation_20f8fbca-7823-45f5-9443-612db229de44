<script lang="ts">
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import "@radix-ui/colors/yellow.css";
  import "@radix-ui/colors/yellow-dark.css";
  import type { IChatMentionableItem } from "../../types/mention-option";
  import { getChatModel } from "../../contexts/chat-model-context";
  import { getRulesModel } from "../../contexts/rules-model-context";
  import RegularBookSparklesIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/book-sparkles.svg?component";
  import { type IContextInfo } from "../../models/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { calculateRulesAndGuidelinesCharacterCount } from "../../utils/rules-utils";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";
  interface Props {
    item: IChatMentionableItem<"userGuidelines"> & IContextInfo;
  }

  let { item }: Props = $props();

  const chatModel = getChatModel();
  const rulesModel = getRulesModel();

  // Get workspace guidelines content from the context model and calculate total character count
  // to determine warning text
  let contextModel = $derived($chatModel?.specialContextInputModel);
  let workspaceGuidelines = $derived(
    contextModel?.workspaceGuidelines ? contextModel?.workspaceGuidelines[0] : undefined
  );
  let rulesAndGuidelinesLimit = $derived(workspaceGuidelines?.lengthLimit);
  let userGuidelinesLimit = $derived(item.userGuidelines.lengthLimit);
  let rules = $derived(rulesModel.getCachedRules());
  let contextRules = $derived(contextModel?.activeRules.map((r) => r.rule) || []);
  let rulesAndGuidelinesCalculation = $derived(
    calculateRulesAndGuidelinesCharacterCount({
      rules: $rules,
      workspaceGuidelinesContent: workspaceGuidelines?.contents || "",
      contextRules,
      rulesAndGuidelinesLimit,
    })
  );
  let showRulesAndGuidelinesWarning = $derived(rulesAndGuidelinesCalculation.isOverLimit);
  let rulesAndGuidelinesWarningMessage = $derived(rulesAndGuidelinesCalculation.warningMessage);

  let enableRules = $derived($chatModel?.flags.enableRules ?? false);
  let showUserGuidelinesWarning = $derived(item.userGuidelines.overLimit);
  let isOverLimit = $derived(showUserGuidelinesWarning || showRulesAndGuidelinesWarning);
  let displayText = $derived(enableRules ? "Rules & Guidelines" : "Guidelines");

  function getGuidelinesErrorMessage() {
    if (showRulesAndGuidelinesWarning) {
      return rulesAndGuidelinesWarningMessage;
    } else if (showUserGuidelinesWarning) {
      return `Guidelines exceeded length limit of ${userGuidelinesLimit} characters`;
    }
  }

  function openSettingsPanel() {
    // Open the settings panel to access user guidelines
    if ($chatModel) {
      // Pass 'guidelines' as the section to navigate to
      $chatModel.extensionClient.openSettingsPage("guidelines");
    }
  }
</script>

<RegisterCommand name="openRulesAndGuidelinesSettings" handler={openSettingsPanel} />
<TextTooltipAugment content={isOverLimit ? getGuidelinesErrorMessage() : `${displayText}`}>
  <span slot="content">
    {isOverLimit ? getGuidelinesErrorMessage() : `${displayText}`}
    <ShortcutHint commandName="openRulesAndGuidelinesSettings" keysOnly />
  </span>
  <IconButtonAugment
    size={0.5}
    variant="ghost-block"
    color={isOverLimit ? "warning" : "neutral"}
    onclick={openSettingsPanel}
    onkeyup={onKey("Enter", openSettingsPanel)}
  >
    {#if isOverLimit}
      <GuardedIcon name="circle-alert"><ExclamationTriangle /></GuardedIcon>
    {:else}
      <GuardedIcon name="album"><RegularBookSparklesIcon /></GuardedIcon>
    {/if}
  </IconButtonAugment>
</TextTooltipAugment>
