<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import MemoriesIconRegular from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/box-archive.svg?component";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";

  export let onClick = () => {};
</script>

<RegisterCommand name="openAgentMemories" handler={onClick} />
<TextTooltipAugment content="Agent Memories">
  <span slot="content">Agent Memories<ShortcutHint commandName="openAgentMemories" keysOnly /></span
  >
  <IconButtonAugment size={0.5} variant="ghost-block" color="neutral" on:click={onClick} on:keydown>
    <GuardedIcon name="archive"><MemoriesIconRegular /></GuardedIcon>
  </IconButtonAugment>
</TextTooltipAugment>
