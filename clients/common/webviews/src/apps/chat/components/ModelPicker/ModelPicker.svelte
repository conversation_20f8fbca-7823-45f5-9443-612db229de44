<script lang="ts">
  import { getContext } from "svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { DEFAULT_NAME, type ModelRegistry } from "../../models/model-registry";
  import ModelIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/sliders-simple.svg?component";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CommandContent from "../InputActionBar/CommandContent.svelte";
  import { AgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { get } from "svelte/store";
  import { tick } from "svelte";
  import type { IModel } from "../../models/model-registry";
  import GuardedIcon from "$common-webviews/src/common/components/GuardedIcon.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ModelPricingTooltip from "./ModelPricingTooltip.svelte";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";

  const modelRegistry: ModelRegistry = getContext("modelRegistry");
  const chatModel: ChatModel = getContext("chatModel");
  $: conversationModel = $chatModel.currentConversationModel;

  // Export props for controlling the dropdown state
  export let open: boolean | undefined = undefined;
  export let defaultOpen: boolean | undefined = undefined;
  export let onOpenChange: ((open: boolean) => void) | undefined = undefined;

  let dropdownRoot: DropdownMenuRoot;

  // Scrolling to selected item when menu opens
  let scrollContainer: HTMLDivElement | undefined;
  let selectedItemEl: HTMLElement | undefined;

  function centerSelected() {
    if (!scrollContainer || !selectedItemEl) return;
    const itemTop = selectedItemEl.offsetTop;
    const itemHeight = selectedItemEl.offsetHeight;
    const targetTop = Math.max(0, itemTop - (scrollContainer.clientHeight - itemHeight) / 2);
    scrollContainer.scrollTop = targetTop;
  }

  function openModalPicker() {
    if (open) {
      dropdownRoot?.requestClose();
    } else {
      dropdownRoot?.requestOpen();
    }
  }

  async function handleOpenChange(open: boolean) {
    if (open) {
      // Wait for DOM to mount and layout before measuring
      await tick();
      centerSelected();
      // Focus, so up/down arrows will work after dropdown is open
      scrollContainer?.focus();
    }
    onOpenChange?.(open);
  }

  // Use ModelRegistry's built-in derived stores
  const { formattedModels, selectedModel } = modelRegistry;

  // Function to select a model
  function selectModel(model: IModel) {
    // Don't allow selection of disabled models
    if (model.disabled) {
      return;
    }

    if (conversationModel) {
      // Get the current model before making the change
      const previousModelId = $selectedModel.id ?? null;
      const previousModel = modelRegistry.getModelStore(previousModelId);

      // Only proceed if the model is actually changing
      if (previousModelId !== model.id) {
        // Get the new model information
        const newModel = modelRegistry.getModelStore(model.id);

        // Update the selected model
        conversationModel.setSelectedModelId(model.id);
        const modelSelectionChangeData = {
          previousModelId: get(previousModel).id || "",
          previousModelName: get(previousModel).displayName || "",
          newModelId: get(newModel).id || "",
          newModelName: get(newModel).displayName || "",
        };

        // Report the model selection change event
        conversationModel.extensionClient.reportAgentSessionEvent({
          eventName: AgentSessionEventName.modelSelectionChange,
          conversationId: conversationModel.id,
          eventData: {
            modelSelectionChangeData,
          },
        });
      }
    }
    dropdownRoot?.requestClose();
  }
</script>

{#snippet dropdownItem(model: IModel)}
  {@const selected =
    $selectedModel.id === model.id && $selectedModel.displayName === model.displayName}
  <DropdownMenuAugment.Item
    highlight={selected}
    disabled={model.disabled}
    onSelect={() => {
      selectModel(model);
    }}
  >
    {#if selected}
      <div class="c-model-picker__item-content" bind:this={selectedItemEl}>
        <div>{model.displayName}</div>
        {#if model.description}
          <div class="c-model-picker__item-description">{model.description}</div>
        {/if}
      </div>
    {:else}
      <div class="c-model-picker__item-content">
        <div>{model.displayName}</div>
        {#if model.description}
          <div class="c-model-picker__item-description">{model.description}</div>
        {/if}
      </div>
    {/if}
    {#if model.tooltipName}
      <ModelPricingTooltip name={model.tooltipName} description={model.tooltipDescription} />
    {/if}
  </DropdownMenuAugment.Item>
{/snippet}

{#snippet selectedModelLabel()}
  <TextAugment size={1} truncate={true}>
    {$selectedModel.displayName === DEFAULT_NAME ? "" : $selectedModel.displayName}
  </TextAugment>
{/snippet}

<div class="c-model-picker">
  <RegisterCommand name="toggleModelPicker" handler={openModalPicker} />
  <DropdownMenuAugment.Root
    bind:this={dropdownRoot}
    nested={false}
    {open}
    {defaultOpen}
    onOpenChange={handleOpenChange}
  >
    <DropdownMenuAugment.Trigger>
      <CommandContent>
        <ButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          class="c-model-picker__button"
        >
          <GuardedIcon slot="iconLeft" name="settings-2"><ModelIcon /></GuardedIcon>
          <!-- Hide the name if default -->
          <!-- Red dot indicator for disabled selected model -->
          {#if $selectedModel.disabled}
            <TextTooltipAugment
              content={$selectedModel.disabled_reason}
              triggerOn={[TooltipTriggerOn.Hover]}
              side="right"
              nested={true}
            >
              <span class="c-model-picker__text-wrapper">
                {@render selectedModelLabel()}
                <span class="c-model-picker__disabled-indicator"></span>
              </span>
            </TextTooltipAugment>
          {:else}
            <span class="c-model-picker__text-wrapper">
              {@render selectedModelLabel()}
            </span>
          {/if}
        </ButtonAugment>
      </CommandContent>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content size={1} side="top" align="start">
      <div class="c-model-picker__scroll" bind:this={scrollContainer}>
        {#each $formattedModels as model}
          {#if model.disabled && model.disabled_reason}
            <!-- Wrap disabled items with tooltip if they have a disabled reason -->
            <TextTooltipAugment
              content={model.disabled_reason}
              triggerOn={[TooltipTriggerOn.Hover]}
              side="top"
              nested={true}
            >
              {@render dropdownItem(model)}
            </TextTooltipAugment>
          {:else}
            <!-- Regular items without tooltip -->
            {@render dropdownItem(model)}
          {/if}
        {/each}
      </div>
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
</div>

<style>
  .c-model-picker__item-content {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  .c-model-picker__item-description {
    color: var(--ds-color-neutral-a8);
    line-height: 1.3;
    white-space: pre-line; /* Respect \n as line breaks while still wrapping */
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 200px;
  }

  .c-model-picker {
    display: inline-flex;
    /* Allow the component to shrink when space is limited */
    min-width: 0;
    flex-shrink: 1;
    /* Prevent text wrapping and ensure proper alignment */
    white-space: nowrap;
    align-items: center;
  }

  .c-model-picker :global(.l-tooltip-trigger) {
    max-width: 100%;
  }

  /*
    This is hacky but we need text truncation and this can
    be done by applying overflow through all the children.
    Doing it here because applying these styles to actual component would have to many side-effects.
  */
  .c-model-picker
    :global(
      :is(
        .c-model-picker__button,
        .c-model-picker__button .c-button--text,
        .c-model-picker__button .c-button--content
      )
    ) {
    max-width: 100%;
    overflow: hidden;
  }

  .c-model-picker__text-wrapper {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    overflow: hidden;
  }

  .c-model-picker__text-wrapper :global(.c-text) {
    display: block;
    min-width: 0;
  }

  .c-model-picker__disabled-indicator {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--ds-color-error-a11);
    flex-shrink: 0;
  }

  /* Used in tooltip content - Svelte can't detect dynamic usage */
  :global(.c-model-picker__coin-icon) {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-right: var(--ds-spacing-2);
    cursor: pointer;
    vertical-align: middle;
    stroke-width: 2;
  }

  .c-model-picker__scroll {
    display: flex;
    flex-direction: column;
    max-height: min(60vh, 420px);
    overflow-y: auto;
    overscroll-behavior: contain;
  }
</style>
