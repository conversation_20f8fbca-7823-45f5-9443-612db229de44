/**
 * @file send-mode.test.ts
 * Tests for send mode types and utility functions
 */

import { describe, it, expect } from "vitest";
import { SendMode, SEND_MODE, ADD_TASK_MODE, type SendModeOption } from "./send-mode";

describe("SendMode", () => {
  describe("enum values", () => {
    it("should have correct send mode value", () => {
      expect(SendMode.send).toBe("send");
    });

    it("should have correct addTask mode value", () => {
      expect(SendMode.addTask).toBe("addTask");
    });

    it("should have exactly two modes", () => {
      const modes = Object.values(SendMode);
      expect(modes).toHaveLength(2);
      expect(modes).toContain("send");
      expect(modes).toContain("addTask");
    });
  });

  describe("SEND_MODE constant", () => {
    it("should have correct properties", () => {
      expect(SEND_MODE.id).toBe(SendMode.send);
      expect(SEND_MODE.label).toBe("Send to Agent");
      expect(SEND_MODE.description).toBe("Send message to agent");
      expect(SEND_MODE.icon).toBeDefined();
    });

    it("should match SendModeOption interface", () => {
      const option: SendModeOption = SEND_MODE;
      expect(option.id).toBe(SendMode.send);
      expect(option.label).toBe("Send to Agent");
      expect(option.description).toBe("Send message to agent");
    });
  });

  describe("ADD_TASK_MODE constant", () => {
    it("should have correct properties", () => {
      expect(ADD_TASK_MODE.id).toBe(SendMode.addTask);
      expect(ADD_TASK_MODE.label).toBe("Add Task");
      expect(ADD_TASK_MODE.description).toBe("Add task with the message content");
      expect(ADD_TASK_MODE.icon).toBeDefined();
    });

    it("should match SendModeOption interface", () => {
      const option: SendModeOption = ADD_TASK_MODE;
      expect(option.id).toBe(SendMode.addTask);
      expect(option.label).toBe("Add Task");
      expect(option.description).toBe("Add task with the message content");
    });
  });

  describe("Mode constants array usage", () => {
    it("should be able to create an array of mode options", () => {
      const modeOptions: SendModeOption[] = [SEND_MODE, ADD_TASK_MODE];
      expect(modeOptions).toHaveLength(2);
      expect(modeOptions[0].id).toBe(SendMode.send);
      expect(modeOptions[1].id).toBe(SendMode.addTask);
    });

    it("should be able to find mode by id", () => {
      const modeOptions = [SEND_MODE, ADD_TASK_MODE];
      const sendOption = modeOptions.find((opt) => opt.id === SendMode.send);
      expect(sendOption).toBeDefined();
      expect(sendOption?.label).toBe("Send to Agent");

      const addTaskOption = modeOptions.find((opt) => opt.id === SendMode.addTask);
      expect(addTaskOption).toBeDefined();
      expect(addTaskOption?.label).toBe("Add Task");
    });
  });

  describe("type safety", () => {
    it("should ensure SendModeOption has required properties", () => {
      const option: SendModeOption = {
        id: SendMode.send,
        label: "Test",
        description: "Test description",
        lucideIcon: "file-search-2",
      };

      expect(option.id).toBe(SendMode.send);
      expect(option.label).toBe("Test");
      expect(option.description).toBe("Test description");
    });

    it("should allow optional icon property", () => {
      const optionWithIcon: SendModeOption = {
        id: SendMode.addTask,
        label: "Test",
        description: "Test description",
        icon: "test-icon",
        lucideIcon: "file-search-2",
      };

      expect(optionWithIcon.icon).toBe("test-icon");
    });
  });
});
