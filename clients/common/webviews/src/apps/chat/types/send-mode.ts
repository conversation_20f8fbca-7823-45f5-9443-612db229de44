/**
 * @file send-mode.ts
 * Types and enums for chat send mode functionality
 */

import SendIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/solid/paper-plane-top.svg?component";
import PlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/solid/circle-plus.svg?component";
import { type IconName } from "$common-webviews/src/design-system/icons/lucide-icon-registry";

/**
 * Enum representing different send modes for chat messages
 */
export enum SendMode {
  /** Standard send mode - just sends the message */
  send = "send",
  /** Add task with the message content */
  addTask = "addTask",
}

/**
 * Interface for send mode option data used in UI components
 */
export interface SendModeOption {
  /** The send mode value */
  id: SendMode;
  /** Display label for the mode */
  label: string;
  /** Icon component for the mode */
  icon?: any;
  lucideIcon?: IconName;
  /** Optional description for tooltips */
  description?: string;
}

export const SEND_MODE = {
  id: SendMode.send,
  label: "Send to Agent",
  icon: SendIcon,
  lucideIcon: "send-horizontal",
  description: "Send message to agent",
} as const;

export const ADD_TASK_MODE = {
  id: SendMode.addTask,
  label: "Add Task",
  icon: PlusIcon,
  lucideIcon: "plus",
  description: "Add task with the message content",
} as const;

export const SEND_MODE_OPTIONS = [SEND_MODE, ADD_TASK_MODE];
