import type { IMentionable } from "$common-webviews/src/common/components/inputs/types";
import { normalizeFilePath } from "$common-webviews/src/common/utils/file-paths";
import { type UserGuidelinesState } from "$vscode/src/chat/guidelines-types";
import type { ExternalSource } from "$vscode/src/webview-providers/webview-messages";
import type { FileDetails } from "$vscode/src/webview-providers/webview-messages";
import { pathNameToAbsPath, type ISourceFolderInfo } from "$vscode/src/workspace/types";
import { PersonaType, type Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  AUGMENT_DIRECTORY_ROOT,
  AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import type { HydratedTask } from "@augment-internal/sidecar-libs/src/agent/task/task-types";

export interface IOneOfTypes {
  group?: {
    type: GroupTypes;
    items: IChatMentionable[];
    backlinks?: IChatMentionable[];
    class?: string;
  };
  file?: FileDetails;
  recentFile?: FileDetails;
  folder?: FileDetails;
  sourceFolder?: ISourceFolderInfo;
  sourceFolderGroup?: IChatMentionableItem<"sourceFolder">[];
  selection?: FileDetails;
  externalSource?: ExternalSource;
  userGuidelines?: UserGuidelinesState;
  agentMemories?: object;
  personality?: {
    type: PersonaType;
    description: string;
  };
  rule?: Rule;
  task?: {
    taskUuid: string;
    taskTree: HydratedTask;
    surroundingContext: {
      rootTask: HydratedTask;
      targetTaskPath: string[];
    };
  };

  // Special types
  allDefaultContext?: boolean;
  clearContext?: boolean;
}
export type GroupTypes = Exclude<keyof IOneOfTypes, "group">;

export type IChatMentionable = IMentionable &
  IOneOfTypes & {
    [P in keyof IOneOfTypes]: Exclude<IOneOfTypes[P], undefined>;
  };
export type IChatGroup = IChatMentionableItem<"group">;
export type IChatMentionableItem<T extends keyof IOneOfTypes> = IMentionable & {
  [P in T]: Exclude<IOneOfTypes[P], undefined>;
};

// Extended type for user guidelines that can include rulesAndGuidelinesState
export type IChatUserGuidelinesItem = IChatMentionableItem<"userGuidelines">;

// Given a key, return whether the mentionable is of that type.
// Typescript will be able to infer whether type is a valid key of IOneOfTypes,
// and narrow the type so users can directly reference the fields they care about
export function isItemOfType<T extends keyof IOneOfTypes>(
  mentionable: IChatMentionable,
  type: T
): mentionable is IChatMentionableItem<T> {
  return type in mentionable && mentionable[type] !== undefined;
}

export function isItemFile<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"file"> & T {
  return isItemOfType(mentionable, "file");
}

export function isItemRecentFile<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"recentFile"> & T {
  return isItemOfType(mentionable, "recentFile");
}

export function isItemFolder<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"folder"> & T {
  return isItemOfType(mentionable, "folder");
}

export function isItemSourceFolder<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"sourceFolder"> & T {
  return isItemOfType(mentionable, "sourceFolder");
}

export function isItemSourceFolderGroup<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"sourceFolderGroup"> & T {
  return isItemOfType(mentionable, "sourceFolderGroup");
}

export function isItemSelection<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"selection"> & T {
  return isItemOfType(mentionable, "selection");
}

export function isItemExternalSource<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"externalSource"> & T {
  return isItemOfType(mentionable, "externalSource");
}

export function isItemAllDefaultContext<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"allDefaultContext"> & T {
  return isItemOfType(mentionable, "allDefaultContext");
}

export function isItemClearContext<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"clearContext"> & T {
  return isItemOfType(mentionable, "clearContext");
}

export function isItemGuidelines<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"userGuidelines"> & T {
  return isItemOfType(mentionable, "userGuidelines");
}

export function isItemAgentMemories<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"agentMemories"> & T {
  return isItemOfType(mentionable, "agentMemories");
}

export function isItemPersonality(
  mentionable: IChatMentionable
): mentionable is IChatMentionableItem<"personality"> {
  return isItemOfType(mentionable, "personality");
}

export function isItemRule<T>(
  mentionable: IChatMentionable & T
): mentionable is IChatMentionableItem<"rule"> & T {
  return isItemOfType(mentionable, "rule");
}

export function isItemTask(
  mentionable: IChatMentionable
): mentionable is IChatMentionableItem<"task"> {
  return isItemOfType(mentionable, "task");
}

export const USE_DEFAULT_CONTEXT: IChatMentionableItem<"allDefaultContext"> = {
  allDefaultContext: true,
  label: "Default Context",
  id: "allDefaultContext",
};

export const CLEAR_CONTEXT: IChatMentionableItem<"clearContext"> = {
  clearContext: true,
  label: "Clear Context",
  id: "clearContext",
};

export const USER_GUIDELINES: IChatMentionableItem<"userGuidelines"> = {
  userGuidelines: {
    overLimit: false,
    contents: "",
    lengthLimit: 2000,
  },
  label: "User Guidelines",
  id: "userGuidelines",
};

export const AGENT_MEMORIES: IChatMentionableItem<"agentMemories"> = {
  agentMemories: {},
  label: "Agent Memories",
  id: "agentMemories",
};

export const DEFAULT_PERSONALITY: IChatMentionableItem<"personality"> = {
  personality: {
    type: PersonaType.DEFAULT,
    description: "Expert software engineer - trusted coding agent, at your service!",
  },
  label: "Agent Auggie",
  name: "auggie-personality-agent-default",
  id: "auggie-personality-agent-default",
};

export const PROTOTYPER_PERSONALITY: IChatMentionableItem<"personality"> = {
  personality: {
    type: PersonaType.PROTOTYPER,
    description: "Fast and loose - let's get it done, boss!",
  },
  label: "Prototyper Auggie",
  name: "auggie-personality-prototyper",
  id: "auggie-personality-prototyper",
};

export const BRAINSTORM_PERSONALITY: IChatMentionableItem<"personality"> = {
  personality: {
    type: PersonaType.BRAINSTORM,
    description: "Thoughtful and creative - thinking through all possibilities...",
  },
  label: "Brainstorm Auggie",
  name: "auggie-personality-brainstorm",
  id: "auggie-personality-brainstorm",
};

export const REVIEWER_PERSONALITY: IChatMentionableItem<"personality"> = {
  personality: {
    type: PersonaType.REVIEWER,
    description: "Code detective - finding issues and analyzing implications",
  },
  label: "Reviewer Auggie",
  name: "auggie-personality-reviewer",
  id: "auggie-personality-reviewer",
};

export const AVAILABLE_PERSONALITIES = [
  DEFAULT_PERSONALITY,
  PROTOTYPER_PERSONALITY,
  BRAINSTORM_PERSONALITY,
  REVIEWER_PERSONALITY,
];

/**
 * All grouped types are defined below
 */
export function isItemGroup(
  mentionable: IChatMentionable
): mentionable is IChatMentionableItem<"group"> {
  return isItemOfType(mentionable, "group");
}

interface GroupConfig {
  type: GroupTypes;
  label: string;
}
const groupsConfig: GroupConfig[] = [
  {
    label: "Personalities",
    type: "personality",
  },
  {
    label: "Files",
    type: "file",
  },
  {
    label: "Folders",
    type: "folder",
  },
  {
    label: "Source Folders",
    type: "sourceFolder",
  },
  {
    label: "Recently Opened Files",
    type: "recentFile",
  },
  {
    label: "Documentation",
    type: "externalSource",
  },
  {
    label: "Rules",
    type: "rule",
  },
];

export function getIncludedGroups(mentionables: IChatMentionable[]): IChatGroup[] {
  const configuredGroupTypes = groupsConfig.map((config) => config.type);
  const groupedTypes: Map<GroupTypes, IChatMentionable[]> = new Map();
  mentionables.forEach((mentionable) => {
    const groupType = configuredGroupTypes.find((type) => isItemOfType(mentionable, type));
    if (groupType) {
      groupedTypes.set(groupType, [...(groupedTypes.get(groupType) ?? []), mentionable]);
    }
  });

  return groupsConfig
    .map((groupConfig) => {
      const items = groupedTypes.get(groupConfig.type);
      if (!items || items.length === 0) {
        return undefined;
      }
      return {
        label: groupConfig.label,
        id: `${groupConfig.type}s`,
        group: {
          type: groupConfig.type,
          items,
        },
      };
    })
    .filter((group): group is IChatGroup => Boolean(group));
}

/**
 * Common formatters to get things into an IMentionableFormat format
 */

export function fileDetailsToMentionable(f: FileDetails): IMentionable {
  const absolutePath = pathNameToAbsPath({ rootPath: f.repoRoot, relPath: f.pathName });
  const mentionable: IMentionable = {
    label:
      normalizeFilePath(f.pathName)
        .split("/")
        .filter((x) => x.trim() !== "")
        .pop() || "",
    name: absolutePath,
    id: absolutePath,
  };

  // If there is a range, like in a selection
  if (f.fullRange) {
    const rangeString = `:L${f.fullRange.startLineNumber}-${f.fullRange.endLineNumber}`;
    mentionable.label += rangeString;
    mentionable.name += rangeString;
    mentionable.id += rangeString;
  } else if (f.range) {
    const rangeString = `:L${f.range.start}-${f.range.stop}`;
    mentionable.label += rangeString;
    mentionable.name += rangeString;
    mentionable.id += rangeString;
  }
  return mentionable;
}

export function ruleToMentionable(rule: Rule): IMentionable {
  // Extract the filename from the path for the label
  const pathParts = rule.path.split("/");
  const filename = pathParts[pathParts.length - 1];
  // Remove the .md extension if present
  const label = filename.endsWith(".md") ? filename.slice(0, -3) : filename;
  // Add full path so that agent doesn't get confused with finding the filename when inserting into context
  const fullPath = `${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/${rule.path}`;

  return {
    label: label,
    name: fullPath,
    id: fullPath,
  };
}
