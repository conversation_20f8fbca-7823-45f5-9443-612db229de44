<script lang="ts">
  import { run } from "svelte/legacy";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import ToggleButtonAugment from "$common-webviews/src/design-system/components/ToggleButtonAugment.svelte";
  import type { IEditSuggestion, NextEditResultInfo } from "$vscode/src/next-edit/next-edit-types";
  import { ChangeType } from "$vscode/src/next-edit/next-edit-types";
  import { FeedbackRating } from "$vscode/src/types/feedback-rating";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import { webviewState } from "../models/webview-state";
  import type { FeedbackState } from "../types/feedback-state";
  import type { LocalCompletionRequest } from "../types/local-history-completion";
  import { getThankYouNote } from "../utils/thankyou-note";
  import CompletionCodeBlock from "./CompletionCodeBlock.svelte";
  import HistoryHeader from "./HistoryHeader.svelte";
  import Icon from "$common-webviews/src/design-system/icons/Icon.svelte";

  interface Props {
    // Props for different item types
    completion?: LocalCompletionRequest | undefined;
    nextEdit?: NextEditResultInfo | undefined;
    demo?: boolean;
  }

  let { completion = undefined, nextEdit = undefined, demo = false }: Props = $props();

  // Determine item type and get common properties
  let itemType = $derived(completion ? "completion" : "nextEdit");
  let requestId = $derived(completion?.requestId || nextEdit?.requestId || "");
  let occuredAt = $derived(completion?.occuredAt || nextEdit?.occurredAt || new Date());
  let pathName = $derived(completion?.pathName || "");
  let repoRoot = $derived(completion?.repoRoot || nextEdit?.qualifiedPathName?.rootPath || "");

  // Tab state
  let activeTab = $derived(itemType === "completion" ? "Completion" : "Next Edit");
  const tabOptions = completion ? ["Completion"] : ["Next Edit"];

  // Dynamic title for feedback buttons based on item type
  let feedbackTitle = $derived(
    `Leave feedback about this ${itemType === "completion" ? "completion" : "next edit"}`
  );

  // Feedback state
  let feedbackState: FeedbackState = $derived(webviewState.getFeedback(requestId));
  let primaryIssue: string = "";
  let sendingFeedback: boolean = $state(false);
  let thankYouNote: string = $state("");
  let thankYouTimeoutID: ReturnType<typeof setTimeout> | undefined;
  let previousRating: FeedbackRating;

  // Feedback expansion state
  let showFeedbackArea: boolean = $state(false);
  let feedbackType: FeedbackRating | null = null;
  let feedbackText: string = $state("");

  // Next edit specific state
  let lastClicked: IEditSuggestion | undefined = $state();
  let changedLocations: IEditSuggestion[] = $state([]);
  let unchangedLocations: IEditSuggestion[] = $state([]);

  run(() => {
    if (nextEdit) {
      changedLocations = nextEdit.suggestions.filter((s) => s.changeType !== ChangeType.noop);
      unchangedLocations = nextEdit.suggestions.filter((s) => s.changeType === ChangeType.noop);
    }
  });

  function handleMessage(e: MessageEvent<WebViewMessage>) {
    if (demo) return;

    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.completionRatingDone: {
        const { requestId: msgRequestId } = msg.data;
        if (msgRequestId !== requestId) return;
        sendingFeedback = false;
        if (!msg.data.success) {
          feedbackState.selectedRating = previousRating;
          webviewState.setFeedback(requestId, feedbackState);
        }
        break;
      }
      case WebViewMessageType.nextEditRatingDone: {
        const { requestId: msgRequestId } = msg.data;
        if (msgRequestId !== requestId) return;
        sendingFeedback = false;
        if (!msg.data.success) {
          feedbackState.selectedRating = previousRating;
          webviewState.setFeedback(requestId, feedbackState);
        }
        break;
      }
    }
  }

  function showThankYouNote() {
    thankYouNote = getThankYouNote();
    if (thankYouTimeoutID) clearTimeout(thankYouTimeoutID);
    thankYouTimeoutID = setTimeout(() => {
      thankYouNote = "";
    }, 4000);
  }

  function showFeedback(rating: FeedbackRating) {
    // Set the rating and color the button
    feedbackState.selectedRating = rating;
    feedbackType = rating;
    feedbackText = "";
    showFeedbackArea = true;
  }

  function closeFeedbackArea() {
    showFeedbackArea = false;
    feedbackType = null;
    feedbackText = "";
    // Reset the button color
    feedbackState.selectedRating = FeedbackRating.unset;
  }

  function submitFeedback() {
    if (!feedbackType || feedbackText.trim().length === 0) return;

    handleRating(feedbackType, feedbackText.trim());
    closeFeedbackArea();
  }

  function handleRating(rating: FeedbackRating, customNote?: string) {
    showThankYouNote();

    previousRating = feedbackState.selectedRating;
    if (rating !== FeedbackRating.unset) {
      feedbackState.selectedRating = rating;
    }

    if (demo) return;

    let note = customNote || feedbackState.feedbackNote;
    if (primaryIssue) {
      note = `${note} #${primaryIssue}`;
    }

    webviewState.setFeedback(requestId, feedbackState);

    sendingFeedback = true;
    const messageType =
      itemType === "completion"
        ? WebViewMessageType.completionRating
        : WebViewMessageType.nextEditRating;

    host.postMessage({
      type: messageType,
      data: {
        requestId: requestId,
        rating: rating,
        note: note.trim(),
      },
    });
  }

  function onFileClick(suggestion: IEditSuggestion) {
    host.postMessage({
      type: WebViewMessageType.openFile,
      data: {
        repoRoot: suggestion.qualifiedPathName.rootPath,
        pathName: suggestion.result.path,
        range: suggestion.lineRange,
        differentTab: true,
      },
    });
    lastClicked = suggestion;
  }

  function handleTabSelect(option: string): boolean {
    activeTab = option;
    return true;
  }
</script>

<svelte:window onmessage={handleMessage} />

<CardAugment
  size={2}
  variant="surface"
  class={`c-unified-history-item ${sendingFeedback ? "c-unified-history-item--sending-feedback" : ""}`}
>
  <div class="c-unified-history-item__header">
    <HistoryHeader
      {occuredAt}
      requestID={requestId}
      {pathName}
      {repoRoot}
      others={nextEdit ? [`Request type: ${nextEdit.mode}/${nextEdit.scope}`] : undefined}
    />
  </div>

  <!-- Tab Navigation (if we want to support both types in future) -->
  {#if tabOptions.length > 1}
    <div class="c-unified-history-item__tabs">
      <ToggleButtonAugment
        options={tabOptions}
        onSelectOption={handleTabSelect}
        activeOption={activeTab}
        size={1}
      />
    </div>
  {/if}

  <div class="c-unified-history-item__content">
    {#if itemType === "completion" && completion}
      <!-- Completion Content -->
      {#each completion.completions as item}
        <div class="c-unified-history-item__code-block">
          <CompletionCodeBlock
            completion={item}
            prefix={completion.prefix}
            suffix={completion.suffix}
          />
        </div>
      {/each}
    {:else if itemType === "nextEdit" && nextEdit}
      <!-- Next Edit Content -->
      <section>
        {#each changedLocations as item}
          <div
            class="c-completion-code-block"
            role="button"
            tabindex="0"
            onclick={() => onFileClick(item)}
            onkeydown={onKey("Enter", () => onFileClick(item))}
          >
            <pre data-language="plaintext"><code
                ><span
                  class="c-next-edit-addition"
                  class:c-next-edit-addition-clicked={lastClicked === item}
                  >{item.qualifiedPathName.relPath}: {item.lineRange.start +
                    (item.lineRange.start < item.lineRange.stop ? 1 : 0)}-{item.lineRange
                    .stop}</span
                ></code
              ></pre>
          </div>
          <div>{item.result.changeDescription}</div>
          <section>
            original:
            <SimpleMonaco
              text={item.result.existingCode}
              pathName={item.qualifiedPathName.relPath}
              options={{ lineNumbers: "off" }}
            />
            modified:
            <SimpleMonaco
              text={item.result.suggestedCode}
              pathName={item.qualifiedPathName.relPath}
              options={{ lineNumbers: "off" }}
            />
          </section>
        {/each}
        <div class="c-unified-history-item__no-modifications">Unchanged locations:</div>
        {#each unchangedLocations as item}
          <div
            class="c-completion-code-block"
            role="button"
            tabindex="0"
            onclick={() => onFileClick(item)}
            onkeydown={onKey("Enter", () => onFileClick(item))}
          >
            <pre data-language="plaintext" class="c-next-edit-addition"><code
                ><span
                  class="c-next-edit-addition"
                  class:c-next-edit-addition-clicked={lastClicked === item}
                  >{item.qualifiedPathName.relPath}: {item.lineRange.start +
                    (item.lineRange.start < item.lineRange.stop ? 1 : 0)}-{item.lineRange
                    .stop}</span
                ></code
              ></pre>
          </div>
        {/each}
      </section>
    {/if}
  </div>

  <div class="c-unified-history-item__footer">
    <div class="c-unified-history-item__ratings">
      <div class="c-unified-history-item__rating-buttons">
        <IconButtonAugment
          variant="ghost"
          color={feedbackState.selectedRating === FeedbackRating.positive ? "success" : "neutral"}
          size={2}
          disabled={sendingFeedback}
          title={feedbackTitle}
          onclick={() => showFeedback(FeedbackRating.positive)}
        >
          <Icon name="thumbs-up" />
        </IconButtonAugment>
        <IconButtonAugment
          variant="ghost"
          color={feedbackState.selectedRating === FeedbackRating.negative ? "error" : "neutral"}
          size={2}
          disabled={sendingFeedback}
          title={feedbackTitle}
          onclick={() => showFeedback(FeedbackRating.negative)}
        >
          <Icon name="thumbs-down" />
        </IconButtonAugment>
      </div>
      <div class="c-unified-history-item__thankyou">
        {thankYouNote}
      </div>
    </div>

    <!-- Inline Feedback Area -->
    {#if showFeedbackArea}
      <div class="c-unified-history-item__feedback-area">
        <div class="c-unified-history-item__feedback-content">
          <TextAreaAugment
            rows={4}
            placeholder="Enter your feedback..."
            resize="none"
            bind:value={feedbackText}
          />
        </div>

        <div class="c-unified-history-item__feedback-actions">
          <ButtonAugment variant="ghost" size={2} onclick={closeFeedbackArea}>Cancel</ButtonAugment>
          <ButtonAugment
            variant="solid"
            size={2}
            disabled={feedbackText.trim().length === 0}
            onclick={submitFeedback}
          >
            Share Feedback
          </ButtonAugment>
        </div>
      </div>
    {/if}
  </div>
</CardAugment>

<style>
  :global(.c-unified-history-item) {
    display: flex;
    flex-direction: column;
    color: var(--vscode-descriptionForeground);
    margin: 0;
    transition: all 0.2s ease;
  }

  :global(.c-unified-history-item--sending-feedback) {
    opacity: 0.7;
    pointer-events: none;
  }

  .c-unified-history-item__header {
    margin-bottom: var(--ds-spacing-3);
  }

  .c-unified-history-item__tabs {
    margin-bottom: var(--ds-spacing-3);
    display: flex;
    justify-content: center;
  }

  .c-unified-history-item__content {
    margin-bottom: var(--ds-spacing-3);
  }

  .c-unified-history-item__code-block {
    border-radius: var(--ds-radius-3);
    overflow: hidden;
    border: 1px solid var(--vscode-panel-border);
    background: var(--vscode-editor-background);
  }

  .c-unified-history-item__footer {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-unified-history-item__ratings {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-3);
  }

  .c-unified-history-item__rating-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-unified-history-item__thankyou {
    color: var(--vscode-descriptionForeground);
    font-size: 0.9em;
    font-style: italic;
    opacity: 0.8;
  }

  .c-unified-history-item__feedback-area {
    margin-top: var(--ds-spacing-3);
    padding-top: var(--ds-spacing-3);
    border-top: 1px solid var(--vscode-panel-border);
    animation: slideDown 0.2s ease-out;
  }

  .c-unified-history-item__feedback-content {
    margin-bottom: var(--ds-spacing-3);
  }

  .c-unified-history-item__feedback-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--ds-spacing-2);
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .c-unified-history-item__no-modifications {
    gap: var(--ds-spacing-2);
    margin: var(--ds-spacing-4) 0;
  }

  .c-next-edit-addition {
    margin: 0;
    color: var(--vscode-gitDecoration-addedResourceForeground);
  }

  .c-next-edit-addition-clicked {
    color: var(--vscode-gitDecoration-modifiedResourceForeground);
  }
</style>
