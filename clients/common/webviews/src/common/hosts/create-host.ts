import { getVSCodeHost, isVSCodeHost } from "./vscode/vscode";
import { configureIntelliJHost, isIntelliJHost } from "./intellij";
import type { HostInterface } from "./host-types";
import type * as MonacoEditor from "monaco-editor";

declare global {
  interface Window {
    augment?: {
      host?: HostInterface;
    };
    augmentDeps: {
      monaco?: Promise<typeof MonacoEditor>;
    };
    augmentFlags: {
      enablePerformanceMonitoring?: boolean;
      sentry?: {
        enabled: boolean;
        dsn: string;
        release: string;
        environment: string;
        errorSampleRate?: number;
        tracesSampleRate?: number;
        replaysSessionSampleRate?: number;
        replaysOnErrorSampleRate?: number;
        sendDefaultPii?: boolean;
        tags?: Record<string, string>;
        userId?: string;
      };
    };
    augmentPerformance?: {
      initialized?: boolean;
      getFramerate?: () => number;
      getWorstINP?: () => number;
      getAverageFramerate?: () => number;
    };
  }
}

/**
 * Bootstraps the host interface.
 * Does so by doing some level of platform detection, and based on platform detection,
 * will selectively initialize stuff.
 *
 * @returns the host interface
 * @throws if the host interface cannot be created
 */
function createHostInterface(): HostInterface {
  // Platform detection.
  if (isVSCodeHost()) {
    // VSCode injects everything we need so we can use it immediately.
    return getVSCodeHost();
  } else if (isIntelliJHost()) {
    return configureIntelliJHost();
  }
  if (!window.augment?.host) {
    throw new Error("Augment host not available");
  }

  return window.augment.host;
}

/**
 * Gets the host interface. If it does not exist, it will be created.
 *
 * @returns the host interface
 * @throws if the host interface cannot be created
 */
export function getHost(): HostInterface {
  if (window.augment?.host) {
    return window.augment.host;
  }
  window.augment = window.augment || {};
  window.augment.host = createHostInterface();
  return window.augment.host;
}
