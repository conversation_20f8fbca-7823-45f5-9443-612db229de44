// THIS FILE IS AUTOGENERATED, BUT CAN BE EDITED MANUALLY.
// Next pass will merge existing iconRegistry with new icons pulled from Figma.
// Auto-generated Lucide icon registry
// Generated on 2025-09-03T20:06:47.697Z
// Source: https://www.figma.com/design/EuDFzV0ozR8G3jXllO545S

import {
  Activity,
  AlarmClockCheck,
  AlarmClockOff,
  Album,
  Archive,
  ArrowDown,
  ArrowDownToLine,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  AtSign,
  Ban,
  Bell,
  BellOff,
  Book,
  BookOpen,
  BookText,
  Box,
  Braces,
  Brackets,
  Brain,
  BrushCleaning,
  Bug,
  Check,
  CheckCheck,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronsDown,
  ChevronsDownUp,
  ChevronsLeft,
  ChevronsRight,
  ChevronsUpDown,
  Circle,
  CircleAlert,
  CircleCheck,
  CircleCheckBig,
  CircleDashed,
  CircleDot,
  CircleDotDashed,
  CircleFadingArrowUp,
  CircleFadingPlus,
  CircleMinus,
  CirclePause,
  CirclePlay,
  CirclePlus,
  CircleSmall,
  CircleUserRound,
  CircleX,
  Clipboard,
  ClipboardList,
  Clock,
  Clock8,
  Cloud,
  CloudUpload,
  Code,
  CodeXml,
  Cog,
  Compass,
  Copy,
  CopyCheck,
  CopySlash,
  CornerUpLeft,
  CornerUpRight,
  Cpu,
  Database,
  Download,
  Ellipsis,
  EllipsisVertical,
  Expand,
  ExternalLink,
  Eye,
  EyeOff,
  File,
  FileCheck,
  FileDiff,
  FileDown,
  FileInput,
  FileMinus,
  FilePen,
  FilePlus,
  FileSearch2,
  FileText,
  FileUp,
  FileX,
  Files,
  Flag,
  FlaskConical,
  FlaskConicalOff,
  Folder,
  FolderCheck,
  FolderCog,
  FolderOpen,
  FolderPlus,
  FolderRoot,
  FolderSearch,
  FolderTree,
  Folders,
  Gauge,
  Gem,
  GitBranch,
  GitBranchPlus,
  GitCommitHorizontal,
  GitCommitVertical,
  GitMerge,
  GitPullRequest,
  GitPullRequestArrow,
  Github,
  Glasses,
  Globe,
  GripHorizontal,
  GripVertical,
  History,
  Hourglass,
  ImagePlus,
  Info,
  Key,
  KeyRound,
  Layers,
  Layers2,
  List,
  ListCheck,
  ListChecks,
  ListEnd,
  ListFilter,
  ListPlus,
  ListTodo,
  LogIn,
  LogOut,
  MapPinCheck,
  MapPinCheckInside,
  Maximize2,
  Menu,
  MessageCircle,
  MessageCircleQuestionMark,
  MessageCircleWarning,
  MessageSquare,
  MessageSquarePlus,
  MessageSquareText,
  Milestone,
  Minus,
  MoveDown,
  MoveLeft,
  MoveRight,
  MoveUp,
  Network,
  NotebookText,
  NotepadText,
  Octagon,
  Paperclip,
  Pause,
  Pen,
  PenLine,
  Pencil,
  PencilRuler,
  Pin,
  PinOff,
  Play,
  Plug,
  Plus,
  Redo2,
  RefreshCcw,
  Repeat,
  Rocket,
  RotateCcw,
  RotateCw,
  Ruler,
  Save,
  Scissors,
  Scroll,
  Search,
  SearchCode,
  SendHorizontal,
  Settings,
  Settings2,
  Share,
  Share2,
  SlidersHorizontal,
  SlidersVertical,
  Sparkles,
  Square,
  SquareMinus,
  SquarePen,
  SquarePlus,
  SquareSlash,
  SquareTerminal,
  Star,
  Terminal,
  TextCursor,
  TextCursorInput,
  TextSearch,
  ThumbsDown,
  ThumbsUp,
  Trash,
  Trash2,
  Undo2,
  Unplug,
  Upload,
  User,
  UserRound,
  UsersRound,
  WandSparkles,
  Workflow,
  Wrench,
  X,
  Zap,
  ZoomIn,
  ZoomOut,
} from "@lucide/svelte";

/* eslint-disable @typescript-eslint/naming-convention */
export const iconRegistry = {
  "activity": Activity,
  "alarm-clock-check": AlarmClockCheck,
  "alarm-clock-off": AlarmClockOff,
  "album": Album,
  "archive": Archive,
  "arrow-down": ArrowDown,
  "arrow-down-to-line": ArrowDownToLine,
  "arrow-left": ArrowLeft,
  "arrow-right": ArrowRight,
  "arrow-up": ArrowUp,
  "at-sign": AtSign,
  "ban": Ban,
  "bell": Bell,
  "bell-off": BellOff,
  "book": Book,
  "book-open": BookOpen,
  "book-text": BookText,
  "box": Box,
  "braces": Braces,
  "brackets": Brackets,
  "brain": Brain,
  "brush-cleaning": BrushCleaning,
  "bug": Bug,
  "check": Check,
  "check-check": CheckCheck,
  "chevron-down": ChevronDown,
  "chevron-left": ChevronLeft,
  "chevron-right": ChevronRight,
  "chevron-up": ChevronUp,
  "chevrons-down": ChevronsDown,
  "chevrons-down-up": ChevronsDownUp,
  "chevrons-left": ChevronsLeft,
  "chevrons-right": ChevronsRight,
  "chevrons-up-down": ChevronsUpDown,
  "circle": Circle,
  "circle-alert": CircleAlert,
  "circle-check": CircleCheck,
  "circle-check-big": CircleCheckBig,
  "circle-dashed": CircleDashed,
  "circle-dot": CircleDot,
  "circle-dot-dashed": CircleDotDashed,
  "circle-fading-arrow-up": CircleFadingArrowUp,
  "circle-fading-plus": CircleFadingPlus,
  "circle-minus": CircleMinus,
  "circle-pause": CirclePause,
  "circle-play": CirclePlay,
  "circle-plus": CirclePlus,
  "circle-small": CircleSmall,
  "circle-user-round": CircleUserRound,
  "circle-x": CircleX,
  "clipboard": Clipboard,
  "clipboard-list": ClipboardList,
  "clock": Clock,
  "clock-8": Clock8,
  "cloud": Cloud,
  "cloud-upload": CloudUpload,
  "code": Code,
  "code-xml": CodeXml,
  "cog": Cog,
  "compass": Compass,
  "copy": Copy,
  "copy-check": CopyCheck,
  "copy-slash": CopySlash,
  "corner-up-left": CornerUpLeft,
  "corner-up-right": CornerUpRight,
  "cpu": Cpu,
  "database": Database,
  "download": Download,
  "ellipsis": Ellipsis,
  "ellipsis-vertical": EllipsisVertical,
  "expand": Expand,
  "external-link": ExternalLink,
  "eye": Eye,
  "eye-off": EyeOff,
  "file": File,
  "file-check": FileCheck,
  "file-diff": FileDiff,
  "file-down": FileDown,
  "file-input": FileInput,
  "file-minus": FileMinus,
  "file-pen": FilePen,
  "file-plus": FilePlus,
  "file-search-2": FileSearch2,
  "file-text": FileText,
  "file-up": FileUp,
  "file-x": FileX,
  "files": Files,
  "flag": Flag,
  "flask-conical": FlaskConical,
  "flask-conical-off": FlaskConicalOff,
  "folder": Folder,
  "folder-check": FolderCheck,
  "folder-cog": FolderCog,
  "folder-open": FolderOpen,
  "folder-plus": FolderPlus,
  "folder-search": FolderSearch,
  "folder-tree": FolderTree,
  "folder-root": FolderRoot,
  "folders": Folders,
  "gauge": Gauge,
  "gem": Gem,
  "git-branch": GitBranch,
  "git-branch-plus": GitBranchPlus,
  "git-commit-horizontal": GitCommitHorizontal,
  "git-commit-vertical": GitCommitVertical,
  "git-merge": GitMerge,
  "git-pull-request": GitPullRequest,
  "git-pull-request-arrow": GitPullRequestArrow,
  "github": Github,
  "glasses": Glasses,
  "globe": Globe,
  "grip-horizontal": GripHorizontal,
  "grip-vertical": GripVertical,
  "history": History,
  "hourglass": Hourglass,
  "image-plus": ImagePlus,
  "info": Info,
  "key": Key,
  "key-round": KeyRound,
  "layers": Layers,
  "layers-2": Layers2,
  "list": List,
  "list-check": ListCheck,
  "list-checks": ListChecks,
  "list-end": ListEnd,
  "list-filter": ListFilter,
  "list-plus": ListPlus,
  "list-todo": ListTodo,
  "log-in": LogIn,
  "log-out": LogOut,
  "map-pin-check": MapPinCheck,
  "map-pin-check-inside": MapPinCheckInside,
  "maximize-2": Maximize2,
  "menu": Menu,
  "message-circle": MessageCircle,
  "message-circle-question-mark": MessageCircleQuestionMark,
  "message-circle-warning": MessageCircleWarning,
  "message-square": MessageSquare,
  "message-square-plus": MessageSquarePlus,
  "message-square-text": MessageSquareText,
  "messages-circle": MessageCircle,
  "milestone": Milestone,
  "minus": Minus,
  "move-down": MoveDown,
  "move-left": MoveLeft,
  "move-right": MoveRight,
  "move-up": MoveUp,
  "network": Network,
  "notebook-text": NotebookText,
  "notepad-text": NotepadText,
  "octagon": Octagon,
  "paperclip": Paperclip,
  "pause": Pause,
  "pen": Pen,
  "pen-line": PenLine,
  "pencil": Pencil,
  "pencil-ruler": PencilRuler,
  "pin": Pin,
  "pin-off": PinOff,
  "play": Play,
  "plug": Plug,
  "plus": Plus,
  "redo-2": Redo2,
  "refresh-ccw": RefreshCcw,
  "repeat": Repeat,
  "rocket": Rocket,
  "rotate-ccw": RotateCcw,
  "rotate-cw": RotateCw,
  "ruler": Ruler,
  "save": Save,
  "scissors": Scissors,
  "scroll": Scroll,
  "search": Search,
  "search-code": SearchCode,
  "send-horizontal": SendHorizontal,
  "settings": Settings,
  "settings-2": Settings2,
  "share": Share,
  "share-2": Share2,
  "sliders-horizontal": SlidersHorizontal,
  "sliders-vertical": SlidersVertical,
  "sparkles": Sparkles,
  "square": Square,
  "square-plus": SquarePlus,
  "square-minus": SquareMinus,
  "square-pen": SquarePen,
  "square-slash": SquareSlash,
  "square-terminal": SquareTerminal,
  "star": Star,
  "terminal": Terminal,
  "text-cursor": TextCursor,
  "text-cursor-input": TextCursorInput,
  "text-search": TextSearch,
  "thumbs-down": ThumbsDown,
  "thumbs-up": ThumbsUp,
  "trash": Trash,
  "trash-2": Trash2,
  "undo-2": Undo2,
  "unplug": Unplug,
  "upload": Upload,
  "user": User,
  "user-round": UserRound,
  "users-round": UsersRound,
  "wand-sparkles": WandSparkles,
  "workflow": Workflow,
  "wrench": Wrench,
  "x": X,
  "zap": Zap,
  "zoom-in": ZoomIn,
  "zoom-out": ZoomOut,
  "layers-3": Layers,
} as const;
/* eslint-enable @typescript-eslint/naming-convention */

export type IconName = keyof typeof iconRegistry;

// Helper function to get icon component by name
export function getIcon(name: IconName) {
  return iconRegistry[name];
}
