<script lang="ts">
  import { run } from "svelte/legacy";

  import { onMount } from "svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import Icon from "../../icons/Icon.svelte";

  interface Props {
    scrollableElement?: HTMLElement | null;
  }

  let { scrollableElement = null }: Props = $props();

  let visible: boolean = $state(false);

  // Function to check if the scrollable element has overflow
  function checkOverflow() {
    if (!scrollableElement) {
      visible = false;
      return;
    }

    // Check if content overflows horizontally
    visible = scrollableElement.scrollWidth > scrollableElement.clientWidth;
  }

  // Reactive statement to check overflow when scrollableElement changes
  run(() => {
    if (scrollableElement) {
      checkOverflow();
    }
  });

  // Scroll navigation functions
  function scrollToPrevious() {
    if (!scrollableElement) return;

    const itemWidth = scrollableElement.children[0]?.getBoundingClientRect().width || 0;
    const gap = parseInt(getComputedStyle(scrollableElement).gap) || 0;
    const scrollDistance = itemWidth + gap;

    scrollableElement.scrollBy({
      left: -scrollDistance,
      behavior: "smooth",
    });
  }

  function scrollToNext() {
    if (!scrollableElement) return;

    const itemWidth = scrollableElement.children[0]?.getBoundingClientRect().width || 0;
    const gap = parseInt(getComputedStyle(scrollableElement).gap) || 0;
    const scrollDistance = itemWidth + gap;

    scrollableElement.scrollBy({
      left: scrollDistance,
      behavior: "smooth",
    });
  }

  onMount(() => {
    // Set up a ResizeObserver to watch for changes in the scrollable element
    if (typeof ResizeObserver !== "undefined" && scrollableElement) {
      const resizeObserver = new ResizeObserver(() => {
        checkOverflow();
      });

      resizeObserver.observe(scrollableElement);

      // Also observe the parent container in case it changes size
      if (scrollableElement.parentElement) {
        resizeObserver.observe(scrollableElement.parentElement);
      }

      return () => {
        resizeObserver.disconnect();
      };
    }
  });
</script>

{#if visible}
  <div class="c-scroll-navigation-buttons">
    <IconButtonAugment size={1} variant="ghost" color="neutral" onclick={scrollToPrevious}>
      <Icon name="chevron-left" />
    </IconButtonAugment>
    <IconButtonAugment size={1} variant="ghost" color="neutral" onclick={scrollToNext}>
      <Icon name="chevron-right" />
    </IconButtonAugment>
  </div>
{/if}

<style>
  .c-scroll-navigation-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
