/**
 * Git initialization utilities for VSCode extension
 *
 * Provides helper functions for initializing git operations service
 * with workspace discovery and repository tracking.
 */
import * as path from "path";
import * as vscode from "vscode";
import { GitOperationsService } from "@augment-internal/sidecar-libs/src/git/git-operations-service";
import { findUniqueGitRoots } from "@augment-internal/sidecar-libs/src/vcs/git-utils";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import type { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import type { WorkspaceManager } from "../workspace/workspace-manager";

const logger = getLogger("GitInitialization");

export interface GitInitializationOptions {
  workspaceManager: WorkspaceManager;
  featureFlagManager: FeatureFlagManager;
  gitOperationsService: GitOperationsService;
}

/**
 * Initialize git service with current workspace repositories
 *
 * This function:
 * 1. Checks if git tracking is enabled via feature flags
 * 2. Discovers all workspace folders (source folders + VSCode workspace folders)
 * 3. Finds unique git repository roots from workspace paths
 * 4. Tracks both existing git repos and non-git workspace folders (which will be initialized)
 * 5. Sets up workspace manager file change listener for batched git notifications
 *
 * @param options Configuration object with required services
 * @returns Promise that resolves to a disposable for cleanup, or void if initialization failed
 */
export async function initializeGitService(
  options: GitInitializationOptions
): Promise<vscode.Disposable | void> {
  const { workspaceManager, featureFlagManager, gitOperationsService } = options;

  if (!workspaceManager) {
    logger.warn("WorkspaceManager not available for git initialization");
    return;
  }

  // Check if git tracking is enabled via feature flag
  if (!featureFlagManager.currentFlags.enableAgentGitTracker) {
    logger.info("Git tracking disabled by enableAgentGitTracker feature flag");
    return;
  }

  const sourceFolders = workspaceManager.listSourceFolders();

  // Get all workspace folder paths (both source folders and VSCode workspace folders)
  const allWorkspacePaths =
    sourceFolders.length > 0
      ? sourceFolders.map((folder) => folder.folderRoot)
      : vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath) || [];

  if (allWorkspacePaths.length === 0) {
    logger.warn("No workspace folders available for GitOperationsService initialization");
    return;
  }

  // Find unique git repository roots from all workspace paths
  // This crawls up the directory tree to find the nearest ancestor git root
  const existingGitRoots = await findUniqueGitRoots(allWorkspacePaths);

  // Collect all unique roots to track: existing git roots + non-git workspace folders
  const allRootsToTrack = new Set<string>();

  // Add existing git roots
  existingGitRoots.forEach((gitRoot) => allRootsToTrack.add(gitRoot));

  // Add workspace folders that are not covered by existing git roots
  for (const workspacePath of allWorkspacePaths) {
    // Check if this workspace path is already covered by an existing git root
    const isCoveredByExistingGit = existingGitRoots.some((gitRoot) => {
      const relativePath = path.relative(gitRoot, workspacePath);
      return !relativePath.startsWith("..") && !path.isAbsolute(relativePath);
    });

    if (!isCoveredByExistingGit) {
      // This workspace folder is not in a git repo, so track it for initialization
      allRootsToTrack.add(workspacePath);
    }
  }

  const rootsToTrackArray = Array.from(allRootsToTrack);

  if (rootsToTrackArray.length === 0) {
    logger.debug("No workspace folders available for git tracking");
    return;
  }

  logger.debug(
    `Found ${rootsToTrackArray.length} unique roots to track (${existingGitRoots.length} existing git repos, ${rootsToTrackArray.length - existingGitRoots.length} non-git folders): ${rootsToTrackArray.join(", ")}`
  );

  // Track all roots: existing git repositories and non-git workspace folders (which will be initialized)
  for (const rootPath of rootsToTrackArray) {
    const isExistingGitRepo = existingGitRoots.includes(rootPath);
    logger.debug(
      `Initializing GitOperationsService with ${isExistingGitRepo ? "existing git repository" : "workspace folder (will initialize git repo)"}: ${rootPath}`
    );

    try {
      // Track the repository for git operations (will initialize git repo if none exists)
      const repoInfo = await gitOperationsService.trackRepository({
        repoRoot: rootPath,
      });
      if (repoInfo.isValid) {
        logger.debug(`GitOperationsService successfully tracking repository: ${rootPath}`);
      } else {
        logger.warn(`Failed to track repository ${rootPath}: ${repoInfo.lastError}`);
      }
    } catch (error) {
      logger.warn(`Failed to initialize GitOperationsService for ${rootPath}:`, error);
    }
  }

  // Set up workspace manager file change listener to queue file changes for git notifications
  const fileChangeDisposable = workspaceManager.onDidChangeFile((event) => {
    // Get the repo root directly from the folderId using the path map
    // This is the proper way to resolve folderId to repo root
    const repoRoot = workspaceManager.getRepoRootFromFolderId(event.folderId);
    if (repoRoot) {
      const qualifiedPathName: IQualifiedPathName = {
        rootPath: repoRoot,
        relPath: event.relPath,
      };

      // Queue the file change for batched notification
      gitOperationsService.queueFileChanges([qualifiedPathName]);
    }
  });

  logger.info("Git service initialized with workspace manager file change listener");

  // Return the disposable so the caller can clean it up
  return fileChangeDisposable;
}
