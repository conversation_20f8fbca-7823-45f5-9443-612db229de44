/**
 * @fileoverview Git Operations Service - Multi-repository git operations using isomorphic-git
 *
 * Provides a unified interface for git operations across multiple repositories with:
 * - Idempotent repository initialization
 * - Real repository management via IsoGitConfigManager (no shadow repos)
 * - File staging, status queries, and commit operations
 * - Comprehensive diff statistics and file state tracking
 */

import * as git from "isomorphic-git";
import * as path from "path";
import { diffLines } from "diff";
import { getLogger } from "../logging";
import { DisposableService } from "../lifecycle/disposable-service";
import throttle from "lodash/throttle";
import type { ISidecarDisposable } from "../lifecycle/disposable-types";
import { statusMatrixForWorkspace } from "../vcs/iso-git/status-matrix/enhanced-status-matrix";
import {
  stageFiles as stageFilesUtil,
  unstageFiles as unstageFilesUtil,
  resetFilesToIndex as resetFilesToIndexUtil,
} from "../vcs/iso-git/file-operations";
import type {
  GitRepositoryState,
  GitStageFilesRequest,
  GitUnstageFilesRequest,
  GitCheckoutFilesRequest,
  GitResetToIndexFilesRequest,
  GitCommitRequest,
  GitFileState,
  GitRepositoryInfo,
  GitFileStatusOptions,
  GitReference,
  GitMetadata,
} from "./git-types";
import { createGitFileState, GitFileStatus, GitSpecialRef } from "./git-types";
import type { ChatAgentFileChangeSummary } from "../webview-messages/message-types/agent-messages";
import { IsoGitConfigManager } from "../vcs/iso-git/git-config-manager";
import type { GitConfig } from "../vcs/iso-git/types";
import { DEFAULT_AUTHOR } from "../vcs/iso-git/types";
import { worthWalking } from "../vcs/iso-git/status-matrix";
import type { IQualifiedPathName } from "../workspace/workspace-types";

/**
 * Event type for git updates with optional file paths for targeted refreshes
 */
export interface GitUpdateEvent {
  /** Optional array of file paths that changed, for targeted webview updates */
  changedFiles?: IQualifiedPathName[];
}

/**
 * Calculates line-level diff statistics between two text contents
 * @param oldContent - Original content (undefined for new files)
 * @param newContent - Modified content (undefined for deleted files)
 * @returns Object with added and removed line counts
 */
function calculateDiffLines(
  oldContent: string | undefined,
  newContent: string | undefined
): { addedLines: number; removedLines: number } {
  if (!oldContent && !newContent) return { addedLines: 0, removedLines: 0 };

  if (!oldContent) {
    return { addedLines: newContent?.split("\n").length || 0, removedLines: 0 };
  }

  if (!newContent) {
    return { addedLines: 0, removedLines: oldContent.split("\n").length };
  }

  const diff = diffLines(oldContent, newContent);
  let addedLines = 0;
  let removedLines = 0;

  for (const part of diff) {
    if (part.added) addedLines += part.count || 0;
    else if (part.removed) removedLines += part.count || 0;
  }

  return { addedLines, removedLines };
}

/**
 * Calculates comprehensive git diff statistics across working/staging/HEAD states
 * @param headContent - Content at HEAD commit
 * @param stagedContent - Content in staging area
 * @param workingContent - Content in working directory
 * @returns Complete change summary with staged/unstaged/total statistics
 */
function calculateGitDiffStatistics(
  headContent: string | undefined,
  stagedContent: string | undefined,
  workingContent: string | undefined
): ChatAgentFileChangeSummary {
  const unstagedChanges = calculateDiffLines(stagedContent, workingContent);
  const stagedChanges = calculateDiffLines(headContent, stagedContent);
  const totalChanges = calculateDiffLines(headContent, workingContent);

  return {
    totalAddedLines: totalChanges.addedLines,
    totalRemovedLines: totalChanges.removedLines,
    unstagedChanges,
    stagedChanges,
  };
}

/**
 * Multi-repository git operations service using isomorphic-git
 *
 * Provides a unified interface for git operations across multiple repositories with:
 * - Idempotent repository initialization via `ensureRepo`
 * - File staging, unstaging, and checkout operations
 * - Status matrix queries with optional diff statistics
 * - Commit creation with comprehensive metadata
 * - Repository tracking and discovery
 *
 * @example
 * ```typescript
 * const service = new GitOperationsService();
 * await service.trackRepository({ repoRoot: '/path/to/repo' });
 * const status = await service.getStatusMatrix({ repoRoot: '/path/to/repo' });
 * ```
 */
export class GitOperationsService extends DisposableService {
  private readonly _logger = getLogger("GitOperationsService");
  private readonly _ensuredRepos = new Set<string>();

  // Event system for git updates
  private readonly _updateCallbacks = new Set<(event: GitUpdateEvent) => void>();
  public readonly notifyUpdate: ReturnType<typeof throttle>;

  // Batching system for file changes
  private readonly _pendingFileChanges = new Map<string, IQualifiedPathName>();

  // Throttle update notifications to once per second
  private static readonly UPDATE_THROTTLE_MS = 1000;

  /**
   * Creates a new GitOperationsService instance
   */
  constructor() {
    super();

    // Create throttled update notification function
    this.notifyUpdate = throttle(
      this._notifyUpdateImmediate.bind(this),
      GitOperationsService.UPDATE_THROTTLE_MS,
      { leading: true, trailing: true }
    );
  }

  /**
   * Create a unique key for IQualifiedPathName for deduplication
   * @param pathName - The qualified path name
   * @returns A unique string key
   */
  private _createPathKey(pathName: IQualifiedPathName): string {
    return `${pathName.rootPath}/${pathName.relPath}`;
  }

  /**
   * Queue file changes for batched notification to webview
   * @param qualifiedPathNames - Array of file paths that changed
   */
  public queueFileChanges(qualifiedPathNames: IQualifiedPathName[]): void {
    for (const pathName of qualifiedPathNames) {
      // Use path key for deduplication, store the full IQualifiedPathName object
      const pathKey = this._createPathKey(pathName);
      this._pendingFileChanges.set(pathKey, pathName);
    }
    // Trigger throttled notification
    this.notifyUpdate();
  }

  /**
   * Register a callback to be called when git operations result in updates
   * @param callback - Function to call when updates occur
   * @returns Disposable that can be used to unregister the callback
   */
  public onHasUpdate(callback: (event: GitUpdateEvent) => void): ISidecarDisposable {
    this._updateCallbacks.add(callback);
    return {
      dispose: () => {
        this._updateCallbacks.delete(callback);
      },
    };
  }

  /**
   * Immediate implementation of notifying callbacks
   * This is wrapped by the throttled version
   */
  private _notifyUpdateImmediate(): void {
    // Collect and clear pending file changes - use the IQualifiedPathName objects directly
    const changedFiles: IQualifiedPathName[] = [];
    for (const [, qualifiedPathName] of this._pendingFileChanges) {
      changedFiles.push(qualifiedPathName);
    }
    this._pendingFileChanges.clear();

    const event: GitUpdateEvent = {
      changedFiles: changedFiles.length > 0 ? changedFiles : undefined,
    };

    this._updateCallbacks.forEach((callback) => {
      try {
        callback(event);
      } catch (error) {
        this._logger.error("Error in git update callback:", error);
      }
    });
  }

  /**
   * Ensures repository is initialized and ready for operations (idempotent)
   * @param repoRoot - Repository root path to initialize
   * @private
   */
  private async _ensureRepo(repoRoot: string): Promise<void> {
    if (this._ensuredRepos.has(repoRoot)) return;

    this._logger.debug(`Ensuring repository: ${repoRoot}`);

    try {
      await IsoGitConfigManager.getInstance().addWorkspace(repoRoot);
      this._ensuredRepos.add(repoRoot);
      this._logger.debug(`Repository ensured: ${repoRoot}`);
    } catch (error) {
      const errMessage = error instanceof Error ? error.message : "Unknown error";
      const fullMessage = `Failed to ensure repository ${repoRoot}: ${errMessage}`;
      this._logger.error(fullMessage);
      throw new Error(fullMessage);
    }
  }

  /**
   * Gets repository information without tracking it
   * @param options - Configuration object with repoRoot path
   * @returns Repository information including validity and current branch
   */
  async getRepositoryInfo(options: { repoRoot: string }): Promise<GitRepositoryInfo> {
    const { repoRoot } = options;
    return this._getRepositoryInfo(repoRoot);
  }

  /**
   * Tracks a repository for git operations and returns its current state
   * @param options - Configuration object with repoRoot path
   * @returns Repository information including validity and current branch
   */
  async trackRepository(options: { repoRoot: string }): Promise<GitRepositoryInfo> {
    const { repoRoot } = options;

    try {
      await this._ensureRepo(repoRoot);
      this._logger.debug(`Successfully tracked repository: ${repoRoot}`);

      const metadata = await this.computeGitMetadata(repoRoot);

      return {
        repoRoot,
        hasRealRepo: true,
        metadata,
        isValid: true,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      this._logger.warn(`Failed to track repository ${repoRoot}:`, errorMessage);

      return {
        repoRoot,
        hasRealRepo: false,
        isValid: false,
        lastError: errorMessage,
      };
    }
  }

  /**
   * Lists all currently tracked repositories with their status information
   * @returns Array of repository information objects
   */
  async listRepositories(): Promise<GitRepositoryInfo[]> {
    const repositories: GitRepositoryInfo[] = [];

    for (const repoRoot of this._ensuredRepos) {
      try {
        const metadata = await this.computeGitMetadata(repoRoot);

        repositories.push({
          repoRoot,
          hasRealRepo: true,
          metadata,
          isValid: true,
        });
      } catch (error) {
        repositories.push({
          repoRoot,
          hasRealRepo: false,
          isValid: false,
          lastError: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    this._logger.info(
      `Listed ${repositories.length} repositories: ${repositories
        .map((repo) => repo.repoRoot)
        .join(", ")}`
    );

    return repositories;
  }

  /**
   * Gets repository information for a given path without tracking it
   * @param repoRoot - Repository root path
   * @returns Repository information
   * @private
   */
  private async _getRepositoryInfo(repoRoot: string): Promise<GitRepositoryInfo> {
    try {
      await this._ensureRepo(repoRoot);
      const metadata = await this.computeGitMetadata(repoRoot);

      return {
        repoRoot,
        hasRealRepo: true,
        metadata,
        isValid: true,
      };
    } catch (error) {
      return {
        repoRoot,
        hasRealRepo: false,
        isValid: false,
        lastError: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Gets the working git configuration for a repository
   * @param repoRoot - Repository root path
   * @returns Git configuration object for operations
   * @private
   */
  private async _getWorkingConfig(repoRoot: string): Promise<GitConfig> {
    await this._ensureRepo(repoRoot);

    const workspace = IsoGitConfigManager.getInstance().getWorkspace(repoRoot);
    if (!workspace) {
      throw new Error(`Workspace not found: ${repoRoot}`);
    }

    const config = IsoGitConfigManager.getInstance().getConfigs(workspace.root);
    if (!config) {
      throw new Error(`Git config not found for workspace: ${workspace.root}`);
    }

    return config.real;
  }

  /**
   * Computes lightweight git metadata that is cheap to compute
   * @param repoRoot - Repository root path
   * @returns Git metadata with current branch, head OID, and short commit
   */
  async computeGitMetadata(repoRoot: string): Promise<GitMetadata> {
    try {
      const config = await this._getWorkingConfig(repoRoot);

      const currentBranch = await git.currentBranch(config);
      const headOid = await git.resolveRef({ ...config, ref: "HEAD" });
      const shortCommit = headOid ? headOid.substring(0, 7) : undefined;

      return {
        currentBranch: currentBranch || undefined,
        headOid,
        shortCommit,
      };
    } catch (error) {
      this._logger.debug(`Failed to compute git metadata for ${repoRoot}:`, error);
      return {};
    }
  }

  /**
   * Gets the current repository state including branch, files, and change statistics
   * @param options - Configuration object with repoRoot path
   * @returns Complete repository state with file changes and metadata
   */
  async getRepositoryState(options: { repoRoot: string }): Promise<GitRepositoryState> {
    try {
      const { repoRoot } = options;
      const config = await this._getWorkingConfig(repoRoot);

      const metadata = await this.computeGitMetadata(repoRoot);

      this._logger.debug(
        `Repository state - Branch: ${metadata.currentBranch ?? "detached"}, HEAD: ${metadata.headOid}`
      );

      const files = await this.getStatusMatrix({
        repoRoot,
        includeStatistics: true,
        filterChangedOnly: true,
      });

      return {
        metadata,
        workdir: config.dir,
        gitdir: config.gitdir || ".git",
        files,
        isOperationInProgress: false,
        lastUpdated: Date.now(),
      };
    } catch (error) {
      this._logger.error("Failed to get repository state:", error);
      throw error;
    }
  }

  /**
   * Gets file status matrix with optional diff statistics and filtering
   * @param options - Status query options including repository path and filters
   * @returns Array of file states with optional change statistics
   */
  async getStatusMatrix(options: GitFileStatusOptions): Promise<GitFileState[]> {
    try {
      const { repoRoot, ...statusOptions } = options;
      await this._ensureRepo(repoRoot);

      const statusMatrixResult = await statusMatrixForWorkspace(repoRoot, {
        filepaths: statusOptions.filepaths,
      });

      const filteredStatusMatrix = statusOptions.filterChangedOnly
        ? statusMatrixResult.filter((statusRow) => GitFileStatus.hasChangedSinceHead(statusRow))
        : statusMatrixResult;

      if (!statusOptions.includeStatistics) {
        return filteredStatusMatrix.map((statusRow) => {
          const qualifiedPathName = {
            rootPath: repoRoot,
            relPath: statusRow[0],
          };
          return createGitFileState(statusRow, qualifiedPathName);
        });
      }

      const filePaths = filteredStatusMatrix.map((statusRow) => statusRow[0]);
      const fileContents =
        filePaths.length > 0 ? await this.getFileContentForDiffBulk(repoRoot, filePaths) : {};

      return filteredStatusMatrix.map((statusRow) => {
        const qualifiedPathName = {
          rootPath: repoRoot,
          relPath: statusRow[0],
        };

        const normalizedPath = path.normalize(statusRow[0]);
        const content = fileContents[normalizedPath];
        const changesSummary = content
          ? calculateGitDiffStatistics(
              content.headContent,
              content.stagedContent,
              content.workingContent
            )
          : undefined;

        return createGitFileState(statusRow, qualifiedPathName, changesSummary);
      });
    } catch (error) {
      this._logger.error("Failed to get status matrix:", error);
      throw error;
    }
  }

  /**
   * Stages multiple files for commit (batch operation)
   * @param options - Repository path and file staging request
   */
  async stageFiles(options: { repoRoot: string } & GitStageFilesRequest): Promise<void> {
    const { repoRoot, ...request } = options;
    const config = await this._getWorkingConfig(repoRoot);

    const results = await stageFilesUtil(config, request.filePaths);

    // Log results
    for (const result of results) {
      if (result.success) {
        this._logger.debug(`Staged file (${result.action}): ${result.filepath}`);
      } else {
        this._logger.warn(`Could not stage file ${result.filepath}: ${result.error}`);
      }
    }

    const successCount = results.filter((r) => r.success).length;
    this._logger.info(`Successfully staged ${successCount}/${request.filePaths.length} files`);
  }

  /**
   * Unstages multiple files from the staging area (batch operation)
   * @param options - Repository path and file unstaging request
   */
  async unstageFiles(options: { repoRoot: string } & GitUnstageFilesRequest): Promise<void> {
    const { repoRoot, ...request } = options;
    const config = await this._getWorkingConfig(repoRoot);

    const results = await unstageFilesUtil(config, request.filePaths);

    // Log results
    for (const result of results) {
      if (result.success) {
        this._logger.debug(`Unstaged file: ${result.filepath}`);
      } else {
        this._logger.warn(`Could not unstage file ${result.filepath}: ${result.error}`);
      }
    }

    const successCount = results.filter((r) => r.success).length;
    this._logger.info(`Successfully unstaged ${successCount}/${request.filePaths.length} files`);
  }

  /**
   * Checks out multiple files to a specific git reference
   * @param options - Repository path and file checkout request
   */
  async checkoutFiles(options: { repoRoot: string } & GitCheckoutFilesRequest): Promise<void> {
    try {
      const { repoRoot, ...request } = options;
      const config = await this._getWorkingConfig(repoRoot);
      const reference = request.reference || GitSpecialRef.HEAD;

      for (const filepath of request.filePaths) {
        try {
          await this._checkoutFileToReference(config, filepath, reference);
          this._logger.debug(`Checked out file: ${filepath} to ${reference}`);
        } catch (error) {
          if (error instanceof Error && error.message.includes("not found")) {
            const fullPath = path.join(config.dir, filepath);
            try {
              await config.fs.unlink(fullPath);
              this._logger.debug(`Deleted untracked file: ${filepath}`);
            } catch {
              this._logger.debug(`File already deleted: ${filepath}`);
            }
          } else {
            throw error;
          }
        }
      }

      this._logger.info(
        `Successfully checked out ${request.filePaths.length} files to ${reference}`
      );
    } catch (error) {
      this._logger.error("Failed to checkout files:", error);
      throw error;
    }
  }

  /**
   * Helper method to checkout a single file to a specific reference
   * @private
   */
  private async _checkoutFileToReference(
    config: GitConfig,
    filepath: string,
    reference: GitReference
  ): Promise<void> {
    const fullPath = path.join(config.dir, filepath);

    // WORKTREE is a no-op (file is already in working directory)
    if (reference === GitSpecialRef.WORKTREE) {
      return;
    }

    try {
      let blob: Uint8Array | undefined;

      if (reference === GitSpecialRef.INDEX) {
        // Read from staging area using git.walk
        await git.walk({
          ...config,
          trees: [git.STAGE()],
          map: async (walkFilepath, [entry]) => {
            // If our current walk is not a prefix of the filepath we're looking for, skip the tree
            if (!worthWalking(filepath, walkFilepath)) {
              return null;
            }

            if (walkFilepath === filepath && entry) {
              const oid = typeof entry.oid === "function" ? await entry.oid() : entry.oid;
              const result = await git.readBlob({ ...config, oid });
              blob = result.blob;
            }
          },
        });

        if (!blob) {
          throw new Error(`File ${filepath} not found in staging area`);
        }
      } else {
        // Read from HEAD or specific reference
        const ref = reference === GitSpecialRef.HEAD ? "HEAD" : reference;
        const oid = await git.resolveRef({ ...config, ref });
        const result = await git.readBlob({ ...config, oid, filepath });
        blob = result.blob;
      }

      await config.fs.writeFile(fullPath, blob);
    } catch {
      // If file doesn't exist in the reference, remove it from working directory
      try {
        await config.fs.unlink(fullPath);
      } catch {
        // File might not exist, which is fine
      }
    }
  }

  /**
   * Resets multiple files to their index state with smart logic:
   * - If file is in index: reset to index state
   * - If file is not in index but in HEAD: reset to HEAD state
   * - If file is untracked (not in index or HEAD): remove entirely
   * @param options - Repository path and file reset request
   */
  async resetToIndex(options: { repoRoot: string } & GitResetToIndexFilesRequest): Promise<void> {
    const { repoRoot, ...request } = options;
    const config = await this._getWorkingConfig(repoRoot);

    const results = await resetFilesToIndexUtil(config, request.filePaths);

    // Log results
    for (const result of results) {
      if (result.success) {
        this._logger.debug(`Reset file (${result.action}): ${result.filepath}`);
      } else {
        this._logger.warn(`Could not reset file ${result.filepath}: ${result.error}`);
      }
    }

    const successCount = results.filter((r) => r.success).length;
    this._logger.info(
      `Successfully reset ${successCount}/${request.filePaths.length} files to index`
    );
  }

  /**
   * Validates that the specified path is a valid git repository
   * @param options - Repository path to validate
   * @returns True if repository is valid, false otherwise
   */
  async validateRepository(options: { repoRoot: string }): Promise<boolean> {
    try {
      const { repoRoot } = options;
      const config = await this._getWorkingConfig(repoRoot);
      await git.resolveRef({ ...config, ref: "HEAD" });
      return true;
    } catch (error) {
      this._logger.debug("Repository validation failed:", error);
      return false;
    }
  }

  /**
   * Refreshes repository state to reflect external changes
   * @param options - Repository path to refresh
   */
  async refreshRepository(options: { repoRoot: string }): Promise<void> {
    const { repoRoot } = options;
    try {
      await IsoGitConfigManager.getInstance().addWorkspace(repoRoot);
      this._logger.info("Repository state refreshed");
    } catch (error) {
      this._logger.error("Failed to refresh repository:", error);
      throw error;
    }
  }

  /**
   * Creates a commit with staged changes and specified message
   * @param options - Repository path and commit details
   * @returns The OID of the created commit
   */
  async commitChanges(options: { repoRoot: string } & GitCommitRequest): Promise<string> {
    try {
      const { repoRoot, ...request } = options;
      const config = await this._getWorkingConfig(repoRoot);
      const author = request.author || DEFAULT_AUTHOR;

      const commitOid = await git.commit({
        ...config,
        message: request.message,
        author,
      });

      this._logger.info(`Successfully created commit: ${commitOid}`);
      return commitOid;
    } catch (error) {
      this._logger.error("Failed to commit changes:", error);
      throw error;
    }
  }

  /**
   * Gets file content from HEAD, staging area, and working directory for diff operations
   * @param repoRoot - Repository root path
   * @param filePath - Path to the file relative to repository root
   * @returns Object with content from all three git states
   */
  async getFileContentForDiff(
    repoRoot: string,
    filePath: string
  ): Promise<{
    headContent: string | undefined;
    stagedContent: string | undefined;
    workingContent: string | undefined;
  }> {
    const results = await this.getFileContentForDiffBulk(repoRoot, [filePath]);
    return (
      results[filePath] || {
        headContent: undefined,
        stagedContent: undefined,
        workingContent: undefined,
      }
    );
  }

  /**
   * Gets file content from HEAD, staging, and working directory for multiple files (bulk operation)
   * @param repoRoot - Repository root path
   * @param filePaths - Array of file paths relative to repository root
   * @returns Record mapping file paths to their content in all three git states
   * @private
   */
  private async getFileContentForDiffBulk(
    repoRoot: string,
    filePaths: string[]
  ): Promise<
    Record<
      string,
      {
        headContent: string | undefined;
        stagedContent: string | undefined;
        workingContent: string | undefined;
      }
    >
  > {
    try {
      const config = await this._getWorkingConfig(repoRoot);
      const normalizedPaths = filePaths.map((fp) => path.normalize(fp));
      const normalizedPathsSet = new Set(normalizedPaths);
      const results: Record<
        string,
        {
          headContent: string | undefined;
          stagedContent: string | undefined;
          workingContent: string | undefined;
        }
      > = {};

      // Initialize results for all requested files
      for (const filePath of normalizedPaths) {
        results[filePath] = {
          headContent: undefined,
          stagedContent: undefined,
          workingContent: undefined,
        };
      }

      await git.walk({
        ...config,
        trees: [git.TREE({ ref: "HEAD" }), git.STAGE(), git.WORKDIR()],
        map: async (filepath, [headEntry, stageEntry, workdirEntry]) => {
          const normalizedFilepath = path.normalize(filepath);

          // Only process trees we're interested in
          if (
            normalizedPaths.length > 0 &&
            !normalizedPaths.some((p) => worthWalking(p, normalizedFilepath))
          ) {
            return null; // don't traverse this tree
          }

          // Only process paths we're interested in
          if (normalizedPaths.length > 0 && !normalizedPathsSet.has(normalizedFilepath)) {
            return undefined; // don't process this path
          }

          // Get HEAD content
          if (headEntry && (await headEntry.type()) === "blob") {
            const content = await headEntry.content();
            if (content) {
              results[normalizedFilepath].headContent = Buffer.from(content).toString("utf8");
            }
          }

          // Get staged content
          if (stageEntry && (await stageEntry.type()) === "blob") {
            const oid = await stageEntry.oid();
            if (oid) {
              const { blob } = await git.readBlob({ ...config, oid });
              results[normalizedFilepath].stagedContent = Buffer.from(blob).toString("utf8");
            }
          }

          // Get working directory content
          if (workdirEntry && (await workdirEntry.type()) === "blob") {
            const content = await workdirEntry.content();
            if (content) {
              results[normalizedFilepath].workingContent = Buffer.from(content).toString("utf8");
            }
          }
        },
      });

      return results;
    } catch (error) {
      this._logger.error(`Failed to get file content for diff: ${filePaths.join(", ")}`, error);
      throw error;
    }
  }
}
