import { queryFetch } from "app/utils/query-fetch";
import { atomWithQuery } from "jotai-tanstack-query";
import { planChangePendingBaseAtom } from "../pending";
import { getFeatureFlag } from "../../../feature-flags/feature-flags.client";
import { PendingChangeSchema } from "../../../schemas/plan-change";
import { queryOptions } from "../../queryOptions";
import { atom } from "jotai";

export const pendingChangeQueryKeyAtom = atom(["pending-change"]);

export const pendingChangeQueryAtom = atomWithQuery((get) => {
  const shouldPoll = get(planChangePendingBaseAtom);
  const pendingChangeQueryOptions = queryOptions({
    queryKey: get(pendingChangeQueryKeyAtom),
    queryFn: queryFetch("/api/pending-change", PendingChangeSchema),
    enabled: () => {
      // Only enable if the feature flag is enabled
      const dryRunCheckoutEnabled = !!getFeatureFlag(
        "team_management_dry_run_checkout_flows_enabled"
      );
      return dryRunCheckoutEnabled;
    },
    refetchInterval: shouldPoll ? 1_000 : false,
  });
  return pendingChangeQueryOptions;
});
