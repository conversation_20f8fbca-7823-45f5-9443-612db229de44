import { isTenantTierEnterprise, PlansApiGETResponseSchema } from "app/schemas/plan";
import { queryFetch } from "app/utils/query-fetch";
import { queryOptions } from "../../queryOptions";
import { atomWithQuery } from "jotai-tanstack-query";
import { atom } from "jotai";
import { userQueryAtom } from "./user";

export const plansQueryKeyAtom = atom(["plans"]);

export const plansQueryAtom = atomWithQuery((get) => {
  const user = get(userQueryAtom);
  const isEnterprise = user.data?.tenantTier ? isTenantTierEnterprise(user.data.tenantTier) : false;
  const plansQueryOptions = queryOptions({
    queryKey: get(plansQueryKeyAtom),
    queryFn: queryFetch("/api/plans", PlansApiGETResponseSchema),
    enabled: !isEnterprise && !user.isLoading,
  });
  return plansQueryOptions;
});
