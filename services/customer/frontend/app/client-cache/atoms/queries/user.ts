import { atomWithQuery } from "jotai-tanstack-query";
import { queryFetch } from "app/utils/query-fetch";
import { UserApiGETResponseSchema } from "app/schemas/user";
import { queryOptions } from "../../queryOptions";
import { subscriptionCreationPendingBaseAtom, userPlanChangePendingBase<PERSON>tom } from "../pending";
import { atom } from "jotai";

export const userQueryKeyAtom = atom(["user"]);

export const userQueryAtom = atomWithQuery((get) => {
  const isPlanChangeInProgress = get(userPlanChangePendingBaseAtom);
  const isSubscriptionCreationInProgress = get(subscriptionCreationPendingBaseAtom);

  // Poll when:
  // - Base atoms are true (actively pending)
  // - Base atoms are undefined (unknown state - need to check)
  const shouldPoll =
    isPlanChangeInProgress === true ||
    isSubscriptionCreationInProgress === true ||
    isPlanChangeInProgress === undefined ||
    isSubscriptionCreationInProgress === undefined;

  const userQueryOptions = queryOptions({
    queryKey: get(userQueryKeyAtom),
    queryFn: queryFetch("/api/user", UserApiGETResponseSchema),
    refetchInterval: shouldPoll ? 1_000 : false,
  });

  return userQueryOptions;
});
