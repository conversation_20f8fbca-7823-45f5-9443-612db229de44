import { atomWithQuery } from "jotai-tanstack-query";
import { getFeatureFlag } from "../../../feature-flags/feature-flags.client";
import { shouldBlockQueriesAtom, subscriptionCreationPendingAtom } from "../effects";
import { userQuery<PERSON>tom } from "./user";
import { queryOptions } from "../../queryOptions";
import { GetAllPlanChangeDetailsResponseSchema } from "../../../schemas/plan-change";
import { queryFetch } from "../../../utils/query-fetch";
import { atom } from "jotai";

export const allPlanChangeDetailsKeyAtom = atom(["all-plan-change-details"]);

export const getAllPlanChangeDetailsQueryAtom = atomWithQuery((get) => {
  const shouldBlockPlanChangeQueries = get(shouldBlockQueriesAtom);
  const isSubscriptionCreationInProgress = get(subscriptionCreationPendingAtom);
  const userQuery = get(userQueryAtom);

  // Check if the feature flag is enabled first
  const dryRunCheckoutEnabled = !!getFeatureFlag("team_management_dry_run_checkout_flows_enabled");

  // Check if user is admin - only admins can access this query
  const isAdmin = userQuery.data?.isAdmin ?? false;

  // Block if plan change queries should be blocked or subscription creation is pending/undefined
  const shouldBlock = shouldBlockPlanChangeQueries || isSubscriptionCreationInProgress !== false;

  const getAllPlanChangeDetailsQueryOptions = queryOptions({
    queryKey: get(allPlanChangeDetailsKeyAtom),
    queryFn: queryFetch("/api/get-all-plan-change-details", GetAllPlanChangeDetailsResponseSchema),
    enabled: dryRunCheckoutEnabled && isAdmin && !shouldBlock,
  });

  return getAllPlanChangeDetailsQueryOptions;
});
