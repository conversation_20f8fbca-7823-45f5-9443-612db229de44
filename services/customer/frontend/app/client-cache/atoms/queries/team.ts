import { queryFetch } from "app/utils/query-fetch";
import { atomWithQuery } from "jotai-tanstack-query";
import { teamPlanChangePendingBaseAtom } from "../pending";
import type { UserApiGETResponseSchema } from "app/schemas/user";
import {
  TeamPlanChangePendingSchema,
  type TeamStatusMockSchema,
  type TeamStatusActiveSchema,
  type TeamStatusPendingSchema,
  TeamStatusSchema,
} from "app/schemas/team";
import { formatISO } from "date-fns";
import { queryOptions } from "../../queryOptions";
import { userQueryAtom } from "./user";
import { atom } from "jotai";

function makeStubTeamActive(userData: UserApiGETResponseSchema): TeamStatusMockSchema {
  return {
    status: "active",
    isMock: true,
    team: {
      id: `mock-team-${userData.email}`,
      users: [
        {
          id: `mock-user-${userData.email}`,
          email: userData.email,
          role: "ADMIN",
          joinedAt: formatISO(new Date()),
        },
      ],
      seats: 1,
      invitations: [],
    },
  };
}

export const teamQueryKeyAtom = atom(["team"]);

export const teamQueryAtom = atomWithQuery((get) => {
  const userQuery = get(userQueryAtom);

  const teamQueryOptions = queryOptions({
    queryKey: get(teamQueryKeyAtom),
    queryFn: async () => {
      const teamStatus = await queryFetch("/api/team", TeamStatusSchema)();
      return teamStatus;
    },
    select: (teamStatus): TeamStatusActiveSchema | TeamStatusPendingSchema => {
      // If status is 'none', return a mocked team instead of showing create button
      if (teamStatus.status === "none" && userQuery.data) {
        return makeStubTeamActive(userQuery.data);
      }
      return teamStatus as TeamStatusActiveSchema | TeamStatusPendingSchema;
    },
  });
  return teamQueryOptions;
});

export const teamPlanChangePendingQueryKeyAtom = atom(["team-plan-change-pending"]);

export const teamPlanChangePendingQueryAtom = atomWithQuery((get) => {
  const isTeamPlanChangeInProgress = get(teamPlanChangePendingBaseAtom);
  const teamPlanChangePendingQueryOptions = queryOptions({
    queryKey: get(teamPlanChangePendingQueryKeyAtom),
    queryFn: queryFetch("/api/team/plan-change-pending", TeamPlanChangePendingSchema),
    refetchInterval: isTeamPlanChangeInProgress ? 1_000 : false,
  });
  return teamPlanChangePendingQueryOptions;
});
