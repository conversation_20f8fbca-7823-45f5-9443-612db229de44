import { atomWithQuery } from "jotai-tanstack-query";
import { isTenantTierEnterprise } from "../../../schemas/plan";
import { shouldBlockQueriesAtom, subscriptionCreationPendingAtom } from "../effects";
import { userQueryAtom } from "./user";
import { queryOptions } from "../../queryOptions";
import { queryFetch } from "app/utils/query-fetch";
import { OrbCustomerInfoSchema } from "../../../schemas/orb";
import { atom } from "jotai";

export const subscriptionQueryKeyAtom = atom(["subscription"]);

export const subscriptionQueryAtom = atomWithQuery((get) => {
  const shouldBlockPlanChangeQueries = get(shouldBlockQueriesAtom);
  const subscriptionCreationPending = get(subscriptionCreationPendingAtom);
  const userQuery = get(userQueryAtom);

  // Check if user is on enterprise tier - enterprise users should not access this query
  const isEnterprise = userQuery.data?.tenantTier
    ? isTenantTierEnterprise(userQuery.data.tenantTier)
    : false;

  // Block if plan change queries should be blocked or subscription creation is pending/undefined
  const shouldBlock = shouldBlockPlanChangeQueries || subscriptionCreationPending !== false;

  const subscriptionQueryOptions = queryOptions({
    queryKey: get(subscriptionQueryKeyAtom),
    queryFn: queryFetch("/api/subscription", OrbCustomerInfoSchema),
    enabled: !isEnterprise && !shouldBlock,
  });
  return subscriptionQueryOptions;
});
