// K8S deployment file for the embedder.
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

function(env,
         namespace,
         namespace_config,
         cloud,
         name,
         modelInstanceConfigMapName,
         modelConfig,
         gpu,
         replicas,
         surgeReplicas,
         transformationKey,
         embedder_host_handler,
         gpuCount=1,
         global=false)
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, cloud=cloud, namespace=namespace, appName=name);
  local services = lib.flatten([
    grpcLib.grpcService(name, namespace=namespace),
    if global && env != 'DEV' then grpcLib.globalGrpcService(cloud=cloud, appName=name, namespace=namespace) else [],
  ]);
  local dnsNames = lib.flatten([
    grpcLib.grpcServiceNames(name, namespace=namespace),
    if global && env != 'DEV' then grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=name, namespace=namespace) else [],
  ]);
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  // create a client TLS certificate
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % name,
    namespace=namespace,
    env=env,
    dnsNames=dnsNames,
    appName=name,
    volumeName='client-certs',
  );
  // create a server certificate for MTLS
  local serverCert = if env != 'DEV' then
    certLib.createCentralServerCert(name='%s-server-cert' % name,
                                    namespace=namespace,
                                    env=env,
                                    appName=name,
                                    dnsNames=dnsNames,
                                    volumeName='certs')
  else
    certLib.createServerCert(name='%s-server-cert' % name,
                             namespace=namespace,
                             appName=name,
                             dnsNames=grpcLib.grpcServiceNames(name),
                             volumeName='certs');
  local configMap = configMapLib.createConfigMap(appName=name, namespace=namespace, config={
    port: 50051,
    mtls: mtls,
    server_cert_path: '/certs/tls.crt',
    server_key_path: '/certs/tls.key',
    root_cert_path: '/certs/ca.crt',
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    model_arch: modelConfig.model_arch,
    max_seq_length: modelConfig.max_seq_length,
    model_name: modelConfig.name,
    weights_path: modelConfig.efs_deepspeed_checkpoint_path,
    weights_sha256: modelConfig.checkpoint_sha256,
    max_rpc_threads: 64,  // up to several seconds of work can queue up.
    non_indexing_threads_min: 10,
    model_parallelism: gpuCount,
    round_sizes: modelConfig.round_sizes,
    use_cuda_graphs: true,
    num_attention_caches: 32,
    indexing_queue_size: 1000,  // 1000 chunks per tenant; maybe up to 5 seconds of work on an H100.
    query_queue_size: 20,  // up to 20 queries per tenant; maybe 0.2 seconds of work on an H100.
    embedder_host_handler: embedder_host_handler,
    prioritization_over_fairness: std.get(modelConfig, 'prioritization_over_fairness', true),
    // timeouts for query requests
    query_timeout_s: 1.0,
    // timeouts for index requests
    index_timeout_s: 30.0,
    // Kernel selection flags
    pre_attention_kernel_fusion: true,
    use_register_tokens_kernel: true,
  });
  assert std.any(std.map(function(x) x >= modelConfig.max_seq_length, modelConfig.round_sizes)) : 'max_seq_length must be at most max(round_sizes)';
  local container =
    {
      name: 'embedder-py',
      target: {
        name: '//services/embedder_host/server:image',
        dst: 'embedder_py',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv(name, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env + [
        // this is the location GKE injects the matching NVIDIA CUDA driver utils
        {
          name: 'TRITON_CUDA_DIRS',
          value: '/usr/local/nvidia/lib64/',
        },
      ],
      volumeMounts: [
        {
          name: 'persistent-storage',
          mountPath: '/mnt/efs/augment',
          readOnly: true,
        },
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      startupProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        failureThreshold: 6,
        initialDelaySeconds: 60,
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1 * gpuCount + 1,
          'nvidia.com/gpu': gpuCount,
          memory: '%sGi' % (4 * gpuCount),
        },
      },
    };
  local pod =
    {
      containers: [
        container,
      ],
      volumes: [
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        {
          name: 'persistent-storage',
          persistentVolumeClaim: {
            claimName: 'filestore-checkpoint-claim',
          },
        },
        configMap.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    };
  local tolerations = nodeLib.tolerations(resource=gpu, env=env, count=gpuCount, cloud=cloud);

  // If replicas is >= 24, then we always turn antiAffinity off.
  // If replicas < 24, then we make it preferred in GSC and EU, and required otherwise.
  local affinity = nodeLib.affinity(resource=gpu, env=env, cloud=cloud, appName=name, antiAffinity=replicas < 24);
  local schedulingClass = nodeLib.schedulingClass(env=env, resource=gpu, cloud=cloud, count=gpuCount);
  local rollingUpdate = {
    // If we have many replicas, then we can afford not to surge and use unavailable only as our rolling strategy
    maxSurge: if replicas < 16 then 1 else 0,
    // 15% gives us enough slack to make progress quickly while limiting the capacity dip.
    // Important that this returns 0 for 1-replica deployments (dev and staging) so we don't create outages there.
    maxUnavailable: std.floor(replicas * 0.15),
  };
  local approxBatches = std.ceil(replicas / (rollingUpdate.maxSurge + rollingUpdate.maxUnavailable));
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: name,
      namespace: namespace,
      labels: {
        app: name,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // Adhoc auto-scaling and pulling large weights can take longer than the default 10 minutes.
      progressDeadlineSeconds: (10 * 60) + (2 * 60 * approxBatches),
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: replicas,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: rollingUpdate,
      },
      selector: {
        matchLabels: {
          app: name,
        },
      },
      template: {
        metadata: {
          labels: {
            app: name,
          } + if schedulingClass != null then {
            schedulingClass: schedulingClass,
          } else {},
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
          priorityClassName: cloudInfo.envToPriorityClass(env=env, resource=gpu),
        },
      },
    },
  };
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: '%s-scaledobject' % name,
      namespace: namespace,
      labels: {
        app: name,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: name,
      },
      minReplicaCount: replicas,
      maxReplicaCount: replicas + surgeReplicas,
      advanced: {
        // Keda passes these to the HPA.
        // - https://keda.sh/docs/2.14/concepts/scaling-deployments/
        // - https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/#configurable-scaling-behavior
        horizontalPodAutoscalerConfig: {
          behavior: {
            scaleDown: {
              // This is a rolling maximum over the last 20 minutes to avoid churn.
              stabilizationWindowSeconds: 1500,
              policies: [
                {
                  // Reduce the number of embedders by 25% every 5 minutes.
                  type: 'Percent',
                  value: 25,
                  periodSeconds: 300,
                },
              ],
            },
            scaleUp: {
              stabilizationWindowSeconds: 0,  // default
              policies: [
                {
                  // Increase the number of embedders by 25% every 5 minutes.
                  type: 'Percent',
                  value: 25,
                  periodSeconds: 300,
                },
              ],
              selectPolicy: 'Max',  // default; select the policy that affects the most pods
            },
          },
        },
      },
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            threshold: '1',  // Don't scale up for more than 1 pod worth of requirement
            query:
              // Limit to subscriptions we serve. TODO: nicer way of handling this
              local servedNamespaces = std.filter(function(ns) ns.cloud == (if cloud == 'GCP_US_CENTRAL1_GSC_PROD' then 'GCP_US_CENTRAL1_PROD' else cloud) && ns.env == env, tenantNamespaces.namespaces);
              local servedNamespacesRegex = std.join('|', std.map(function(ns) ns.namespace, servedNamespaces));
              // Query the amount we're currently scaled up by.
              local currentSurgeReplicas = 'clamp_min(sum(kube_deployment_spec_replicas{deployment="%s", namespace="%s", cluster="%s"}) - %s, 1)' % [name, namespace, cloudInfo[cloud].clusterName, replicas];
              // Estimate the number of messages to handle by adding 15 minutes worth of incoming load to the backlog count. This leans toward weighing backlog relatively heavily.
              local loadQuery(filter) = |||
                (sum(increase(pubsub_googleapis_com:subscription_sent_message_count{monitored_resource="pubsub_subscription",subscription_id=~"%s"}[15m]))
                + sum(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription", subscription_id=~"%s"}))
              ||| % [filter, filter];
              local estimatedDeploymentLoad = loadQuery('(%s)-content-manager-%s.*' % [servedNamespacesRegex, transformationKey]);
              local estimatedGlobalLoad = loadQuery('(%s)-content-manager.*' % [servedNamespacesRegex]);
              // Then we use the utilization to define the TARGET VALUE for the HPA. The documentation elaborates that the target value for the resource is:
              //     desiredReplicas = ceil[currentReplicas * ( currentMetricValue / desiredMetricValue )]
              // The unit of this query is "estimated additional pods needed": a minimum of (estimated share of total load * surge pods), (conservative estimate of sufficient hardware for my load) minus the current allocation
              // The current estimate is: one pod per 5k messages over 15 minutes or roughly 5 messages/sec
              '%s * clamp_max(%d / %s, 0.0002) - %s' % [estimatedDeploymentLoad, surgeReplicas, estimatedGlobalLoad, currentSurgeReplicas],
          },
        },
      ],
    },
  };

  lib.flatten([
    configMap.objects,
    serverCert.objects,
    clientCert.objects,
    deployment,
    services,
    dynamicFeatureFlags.k8s_objects,
  ] + if surgeReplicas > 0 then [scaledObject] else [])
