// K8S deployment file for the continuous batching inference host.
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
function(
  name,
  env,
  namespace,
  namespace_config,
  cloud,
  gpu,
  gpuCount,
  modelConfig,
  mtls,
  replicas,
  attentionType,
  allReduceImplementation,
  global=false,
)
  local services = lib.flatten([
    grpcLib.grpcService(name, namespace=namespace),
    if global && env != 'DEV' then grpcLib.globalGrpcService(cloud=cloud, appName=name, namespace=namespace) else [],
  ]);
  local dnsNames = lib.flatten([
    grpcLib.grpcServiceNames(name, namespace=namespace),
    if global && env != 'DEV' then grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=name, namespace=namespace) else [],
  ]);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(cloud=cloud, env=env, namespace=namespace, appName=name);
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % name,
    namespace=namespace,
    env=env,
    appName=name,
    volumeName='client-certs',
    dnsNames=dnsNames,
  );
  // create a server certificate for MTLS
  local serverCert =
    certLib.createCentralServerCert(name='%s-server-cert' % name,
                                    namespace=namespace,
                                    env=env,
                                    appName=name,
                                    dnsNames=dnsNames,
                                    volumeName='certs');
  local configMap = configMapLib.createConfigMap(appName=name, namespace=namespace, config={
    port: 50051,
    max_seq_length: modelConfig.inference.max_context_length,
    round_sizes: std.get(modelConfig, 'round_sizes', [32, 64, 128, 256, 512]),
    cache_size: if 'cache_pool_size' in modelConfig then modelConfig.cache_pool_size else 16,
    model_name: modelConfig.name,
    model_arch: modelConfig.model_arch,
    weights_path: modelConfig.efs_deepspeed_checkpoint_path,
    weights_sha256: modelConfig.checkpoint_sha256,
    max_rpc_threads: 32,
    model_parallelism: gpuCount,
    attention_implementation: attentionType,
    speculation_models: if 'speculationModels' in modelConfig then modelConfig.speculationModels else [],
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    auth_config: {
      // This also lives in central, so should not need any further namespace
      // qualification
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    },
    use_sorted_batching: true,
    all_reduce_implementation: allReduceImplementation,
    use_sequence_parallel: std.get(modelConfig, 'use_sequence_parallel', false),
    use_dynamic_sequence_parallel: std.get(modelConfig, 'use_dynamic_sequence_parallel', false),
    use_register_tokens_kernel: true,
    max_requests_in_round: std.get(modelConfig, 'max_requests_in_round', null),
    small_request_max_seqlen: std.get(modelConfig, 'small_request_max_seqlen', null),
  } + if !mtls then {} else {
    client_mtls: clientCert.config,
    server_mtls: serverCert.config,
  });
  local container =
    {
      name: 'infer',
      target: {
        name: '//services/inference_host/server/continuous_batching:image',
        dst: 'inference_host_continuous_batching_image',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv(name, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env + [
        // this is the location GKE injects the matching NVIDIA CUDA driver utils
        {
          name: 'TRITON_CUDA_DIRS',
          value: '/usr/local/nvidia/lib64/',
        },
      ],
      volumeMounts: [
        {
          name: 'persistent-storage',
          mountPath: '/mnt/efs/augment',
          readOnly: true,
        },
        configMap.volumeMountDef,
        // increase shared memory for multiprocessing in multi-GPU models
        {
          mountPath: '/dev/shm',
          name: 'shmem',
        },
        clientCert.volumeMountDef,
        serverCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      startupProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        failureThreshold: 60,
        initialDelaySeconds: 60,
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: gpuCount,
          'nvidia.com/gpu': gpuCount,
          memory: '%sGi' % (24 * gpuCount),
        },
      },
    };

  local appName = name;
  local shortAppName = 'infer-%s' % std.substr(std.md5(appName), 0, 7);
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName,
  );

  local pod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        {
          name: 'shmem',
          emptyDir: {
            sizeLimit: '1Gi',
            medium: 'Memory',
          },
        },
        {
          name: 'persistent-storage',
          persistentVolumeClaim: {
            claimName: 'filestore-checkpoint-claim',
          },
        },
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        serverCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    };

  local tolerations = nodeLib.tolerations(resource=gpu, env=env, count=gpuCount, cloud=cloud);
  local affinity = nodeLib.affinity(resource=gpu,
                                    env=env,
                                    count=gpuCount,
                                    cloud=cloud,
                                    appName=name,
                                    antiAffinity=replicas < 24);
  local schedulingClass = nodeLib.schedulingClass(env=env, resource=gpu, cloud=cloud, count=gpuCount);
  local rollingUpdate = if cloud == 'GCP_EU_WEST4_PROD' then {
    // EU does not have a lot of nodes, and tends to get in a state where
    // it is not possible to schedule a new inference host without kicking
    // out any old pods, particularly one that uses 4 gpus. Workaround
    // this for now by allowing 1 replica to be unavailable during
    // upgrades, which is what we would have to do anyway to get out of
    // this situation.
    maxSurge: 0,
    maxUnavailable: 1,
  } else {
    // If we have many replicas, then we can afford not to surge and use unavailable only as our rolling strategy
    maxSurge: if replicas < 16 then 1 else 0,
    // 15% gives us enough slack to make progress quickly while limiting the capacity dip.
    // Important that this returns 0 for 1-replica deployments (dev and staging) so we don't create outages there.
    maxUnavailable: std.floor(replicas * 0.15),
  };
  local approxBatches = std.ceil(replicas / (rollingUpdate.maxSurge + rollingUpdate.maxUnavailable));

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: name,
      namespace: namespace,
      labels: {
        app: name,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // Adhoc auto-scaling and pulling large weights can take longer than the default 40 minutes.
      progressDeadlineSeconds: (10 + approxBatches * 30) * 60,
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: replicas,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: rollingUpdate,
      },
      selector: {
        matchLabels: {
          app: name,
        },
      },
      template: {
        metadata: {
          labels: {
            app: name,
          } + if schedulingClass != null then {
            schedulingClass: schedulingClass,
          } else {},
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
          priorityClassName: cloudInfo.envToPriorityClass(env=env, resource=gpu),
        },
      },
    },
  };
  // Give access to the CloudStorage bucket.
  local bucketAccess = gcpLib.grantAccess(
    name='%s-bucket-policy' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'StorageBucket',
      external: if env == 'PROD' then 'augment-data' else 'augment-data-dev',
    },
    bindings=[
      {
        role: 'roles/storage.objectViewer',
        members: [
          { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
        ],
      },
    ],
    abandon=true,
  );
  lib.flatten([
    configMap.objects,
    serverCert.objects,
    clientCert.objects,
    deployment,
    services,
    dynamicFeatureFlags.k8s_objects,
    serviceAccount.objects,
    bucketAccess,
  ])
