package service

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"reflect"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"cloud.google.com/go/bigtable"
	"github.com/augmentcode/augment/base/logging/audit"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/option"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	btpb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	bigtableproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	tenantcrypto "github.com/augmentcode/augment/services/lib/encryption"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"google.golang.org/protobuf/types/known/wrapperspb"
	"rsc.io/binaryregexp"
)

type BigtableProxyServer struct {
	bigtableproto.UnimplementedBigtableProxyServer
	config           *Config
	client           *bigtable.Client
	centralClient    *bigtable.Client
	tenantCache      tenantwatcherclient.TenantCacheSync
	tenantCrypto     tenantcrypto.TenantCrypto
	featureFlags     featureflags.FeatureFlagHandle
	ioSequenceNumber *atomic.Int64
	timeout          time.Duration
	auditLogger      *audit.AuditLogger
	tableManager     *TableManager
}

// Increase the default max grpc message decode size to 256MB when
// reading from BigTable. The default is 4MB, which is too small
// for some of the larger lists of blob names that we store in BigTable
// for blob checkpoints.
// see https://code-review.googlesource.com/c/gocloud/+/36171/2/bigtable/bigtable.go
var MAX_DECODE_SIZE = 1024 * 1024 * 256

var (
	ENCRYPTION_FLAG                   = "#encrypted"
	ESCAPED_ENCRYPTION_FLAG_FOR_REGEX string
)

func init() {
	// Initialize the escaped version of the encryption flag
	ESCAPED_ENCRYPTION_FLAG_FOR_REGEX = binaryregexp.QuoteMeta(ENCRYPTION_FLAG)
}

func NewBigtableProxyServer(
	ctx context.Context,
	config *Config,
	featureFlags featureflags.FeatureFlagHandle,
	tenantCache tenantwatcherclient.TenantCacheSync,
	auditLogger *audit.AuditLogger,
) (*BigtableProxyServer, error) {
	var clientOpts []option.ClientOption

	timeout := time.Duration(float32(time.Second) * config.BigtableTimeoutSecs)

	// Configure custom message sizes
	clientOpts = append(clientOpts,
		option.WithGRPCDialOption(
			grpc.WithDefaultCallOptions(
				grpc.MaxCallRecvMsgSize(MAX_DECODE_SIZE),
				grpc.MaxCallSendMsgSize(MAX_DECODE_SIZE),
			),
		),
	)

	// Create the client with all options
	client, err := bigtable.NewClient(
		ctx,
		config.BigtableProjectID,
		config.BigtableInstanceName,
		clientOpts...,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create bigtable client: %w", err)
	}
	centralClient, err := bigtable.NewClient(
		ctx,
		config.BigtableProjectID,
		config.CentralBigtableInstanceName,
		clientOpts...,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create central bigtable client: %w", err)
	}

	// Initialize with random sequence number
	seqNum := &atomic.Int64{}
	seqNum.Store(rand.Int63())

	var kmsClient tenantcrypto.KMSClient
	if config.UseMockKMS {
		log.Info().Ctx(ctx).Msg("Using mock KMS client")
		MockKMSUsage.Inc()
		kmsClient = tenantcrypto.NewMockKMSClient()
	} else {
		log.Info().Ctx(ctx).Msg("Using Google KMS client")
		kmsServiceAccountEmail := config.KMSServiceAccountEmail
		if kmsServiceAccountEmail == "" {
			return nil, fmt.Errorf("no KMS service account specified for KMS operations")
		}
		log.Info().Ctx(ctx).Msgf("Using KMS service account %s", kmsServiceAccountEmail)
		kmsClient, err = tenantcrypto.NewGoogleKMSClientWithServiceAccount(ctx, kmsServiceAccountEmail)
		if err != nil {
			return nil, fmt.Errorf("failed to initialize Google KMS client: %w", err)
		}
	}

	tenantCrypto, err := tenantcrypto.New(ctx, kmsClient)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize tenant data encryption: %w", err)
	}
	// Preload tenant keys for encrypted tenants
	tenants, err := tenantCache.GetAllTenants(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed get tenants for key preload: %w", err)
	}
	err = tenantCrypto.LoadTenantKeys(ctx, tenants)
	if err != nil {
		log.Warn().Ctx(ctx).Err(err).Msg("Failure preloading tenant keys")
	}

	tableManager := NewTableManager(config)

	return &BigtableProxyServer{
		tableManager:  tableManager,
		client:        client,
		centralClient: centralClient,
		config:        config,
		// Create cache of tenants in this namespace
		tenantCache:      tenantCache,
		tenantCrypto:     tenantCrypto,
		featureFlags:     featureFlags,
		ioSequenceNumber: seqNum,
		timeout:          timeout,
		auditLogger:      auditLogger,
	}, nil
}

func (s *BigtableProxyServer) Close() error {
	if s.tenantCrypto != nil {
		s.tenantCrypto.Close()
	}
	if s.client != nil {
		return s.client.Close()
	}
	return nil
}

func (s *BigtableProxyServer) openTable(tableConfig *TableConfig) *bigtable.Table {
	if tableConfig.shouldPassthrough {
		return s.client.Open(tableConfig.GetRealTableName())
	} else {
		return s.centralClient.Open(tableConfig.GetRealTableName())
	}
}

func (s *BigtableProxyServer) checkReadAccess(ctx context.Context, tableName bigtableproto.TableName) error {
	// Create a read request with limit 1, just to check access
	tableConfig, err := s.tableManager.GetConfig(tableName)
	if err != nil {
		return err
	}

	table := s.openTable(tableConfig)

	// Try to read one row with timeout
	ctx, cancel := context.WithTimeout(ctx, s.timeout)
	defer cancel()

	rowRange := bigtable.InfiniteRange("")
	var readError error
	err = table.ReadRows(ctx, rowRange, func(row bigtable.Row) bool {
		return false // Stop after first row
	}, bigtable.LimitRows(1)) // Explicitly limit to 1 row
	if err != nil {
		readError = fmt.Errorf("failed to read from table %s: %w", tableConfig.GetRealTableName(), err)
		log.Error().Ctx(ctx).Err(err).Str("table", tableConfig.GetRealTableName()).Msg("checkReadAccess read failed")
		return readError
	}

	return nil
}

func (s *BigtableProxyServer) CheckAccess(ctx context.Context) error {
	// Check access to ContentManager table
	if err := s.checkReadAccess(ctx, bigtableproto.TableName_CONTENT_MANAGER); err != nil {
		return fmt.Errorf("failed to check ContentManager table access: %w", err)
	}

	return nil
}

func filterDependsOnValue(filter *btpb.RowFilter) bool {
	// Sense if the filter depends on cell values.
	// If data is encrypted, the encrypted data will need to be re-processed locally to determine if the data passes the filter.

	// If no filter, that's valid
	if filter == nil {
		return false
	}

	switch {
	case filter.GetValueRegexFilter() != nil:
		return true
	case filter.GetCondition() != nil:
		condition := filter.GetCondition()
		if filterDependsOnValue(condition.GetPredicateFilter()) {
			return true
		}
		if filterDependsOnValue(condition.GetTrueFilter()) {
			return true
		}
		if filterDependsOnValue(condition.GetFalseFilter()) {
			return true
		}
		return false
	case filter.GetChain() != nil:
		chain := filter.GetChain()
		for _, subFilter := range chain.GetFilters() {
			if filterDependsOnValue(subFilter) {
				return true
			}
		}
		return false
	default:
		// Non-supported filters will be sensed in convertProtoFilter()
		return false
	}
}

func convertProtoFilter(filter *btpb.RowFilter) (bigtable.Filter, error) {
	// Convert the protobuf RowFilter to a bigtable Filter.
	// This doesn't actually make any changes to the meaning of filters today, but if we introduce a filter mode that could
	// be affected by transforming row keys, we have to make sure to handle it here.

	// If no filter, that's valid
	if filter == nil {
		return nil, nil
	}

	// Handle empty filter struct (all fields zero)
	if reflect.ValueOf(filter).Elem().IsZero() {
		return nil, nil
	}

	switch {
	case filter.GetCellsPerColumnLimitFilter() != 0:
		return bigtable.LatestNFilter(int(filter.GetCellsPerColumnLimitFilter())), nil
	case filter.GetCellsPerRowLimitFilter() != 0:
		return bigtable.CellsPerRowLimitFilter(int(filter.GetCellsPerRowLimitFilter())), nil
	case filter.GetStripValueTransformer():
		return bigtable.StripValueFilter(), nil
	case filter.GetFamilyNameRegexFilter() != "":
		return bigtable.FamilyFilter(filter.GetFamilyNameRegexFilter()), nil
	case filter.GetPassAllFilter():
		return bigtable.PassAllFilter(), nil
	case filter.GetBlockAllFilter():
		return bigtable.BlockAllFilter(), nil
	case filter.GetTimestampRangeFilter() != nil:
		tr := filter.GetTimestampRangeFilter()
		startTime := time.Unix(0, tr.GetStartTimestampMicros()*1000) // convert micros to nanos
		endTime := time.Unix(0, tr.GetEndTimestampMicros()*1000)     // convert micros to nanos
		return bigtable.TimestampRangeFilter(startTime, endTime), nil
	case filter.GetColumnQualifierRegexFilter() != nil:
		// modify qualifier regex for encryption flag
		originalPattern := string(filter.GetColumnQualifierRegexFilter())
		modifiedPattern := addEncryptedFlagToQualifierRegex(originalPattern)
		return bigtable.ColumnFilter(modifiedPattern), nil
	case filter.GetValueRegexFilter() != nil:
		// Any encrypted data must be fetched and filter evaluated locally.
		return bigtable.ConditionFilter(
			bigtable.ColumnFilter("\\C*"+ESCAPED_ENCRYPTION_FLAG_FOR_REGEX),
			bigtable.PassAllFilter(),
			bigtable.ValueFilter(string(filter.GetValueRegexFilter())),
		), nil

	case filter.GetCondition() != nil:
		condition := filter.GetCondition()
		predFilter, err := convertProtoFilter(condition.GetPredicateFilter())
		if err != nil {
			return nil, err
		}
		trueFilter, err := convertProtoFilter(condition.GetTrueFilter())
		if err != nil {
			return nil, err
		}
		falseFilter, err := convertProtoFilter(condition.GetFalseFilter())
		if err != nil {
			return nil, err
		}
		return bigtable.ConditionFilter(predFilter, trueFilter, falseFilter), nil

	case filter.GetChain() != nil:
		chain := filter.GetChain()
		filters := make([]bigtable.Filter, 0, len(chain.GetFilters()))
		for _, subFilter := range chain.GetFilters() {
			f, err := convertProtoFilter(subFilter)
			if err != nil {
				return nil, err
			}
			filters = append(filters, f)
		}
		return bigtable.ChainFilters(filters...), nil

	default:
		log.Error().
			Interface("filter", filter).
			Interface("filter_type", fmt.Sprintf("%T", filter)).
			Msg("unsupported filter type")
		return nil, status.Error(codes.Internal, "unsupported filter")
	}
}

func evaluateProtoFilterLocally(filter *btpb.RowFilter, chunk *btpb.ReadRowsResponse_CellChunk) (bool, error) {
	// Evaluate the filter locally on the chunk data.
	// This is necessary when the filter depends on cell values, since the encrypted data will need to be decrypted
	// before the filter can be evaluated.

	switch {
	case filter.GetPassAllFilter():
		return true, nil

	case filter.GetBlockAllFilter():
		return false, nil

	// These are transformers, so rely on bigtble to have applied them appropriately.
	case filter.GetCellsPerColumnLimitFilter() != 0:
		return true, nil
	case filter.GetCellsPerRowLimitFilter() != 0:
		return true, nil
	case filter.GetStripValueTransformer():
		return true, nil

	case filter.GetFamilyNameRegexFilter() != "":
		re, err := newRegexp([]byte(filter.GetFamilyNameRegexFilter()))
		if err != nil {
			return false, err
		}
		familyName := chunk.FamilyName.GetValue()
		match := re.MatchString(familyName)
		return match, nil

	case filter.GetColumnQualifierRegexFilter() != nil:
		// Evaluate column qualifier regex
		re, err := newRegexp(filter.GetColumnQualifierRegexFilter())
		if err != nil {
			return false, err
		}
		qualifier := string(chunk.Qualifier.GetValue())
		match := re.MatchString(qualifier)
		return match, nil

	case filter.GetTimestampRangeFilter() != nil:
		// Evaluate timestamp range filter
		tr := filter.GetTimestampRangeFilter()
		startMicros := tr.GetStartTimestampMicros()
		endMicros := tr.GetEndTimestampMicros()

		// Convert chunk timestamp (nanos) to micros for comparison
		chunkTimestampMicros := chunk.GetTimestampMicros()

		// Check if timestamp is within range
		// Start is inclusive, end is exclusive
		inRange := chunkTimestampMicros >= startMicros &&
			(endMicros == 0 || chunkTimestampMicros < endMicros)
		return inRange, nil

	case filter.GetValueRegexFilter() != nil:
		// This filter must be evaluated locally.
		pattern := filter.GetValueRegexFilter()
		re, err := newRegexp(pattern)
		if err != nil {
			return false, err
		}
		matched := re.Match(chunk.Value)
		return matched, nil

	case filter.GetCondition() != nil:
		condition := filter.GetCondition()
		predPass, err := evaluateProtoFilterLocally(condition.GetPredicateFilter(), chunk)
		if err != nil {
			return false, err
		}
		if predPass {
			return evaluateProtoFilterLocally(condition.GetTrueFilter(), chunk)
		}
		return evaluateProtoFilterLocally(condition.GetFalseFilter(), chunk)

	case filter.GetChain() != nil:
		chain := filter.GetChain()
		for _, subFilter := range chain.GetFilters() {
			f, err := evaluateProtoFilterLocally(subFilter, chunk)
			if err != nil {
				return false, err
			}
			if !f {
				return false, nil
			}
		}
		return true, nil

	default:
		log.Error().Interface("filter", filter).Msg("unsupported filter")
		return false, status.Error(codes.Internal, "unsupported filter")
	}
}

func addEncryptedFlagToQualifier(qualifier string) string {
	return qualifier + ENCRYPTION_FLAG
}

func removeEncryptedFlagFromQualifier(qualifier string) string {
	return strings.TrimSuffix(qualifier, ENCRYPTION_FLAG)
}

func addEncryptedFlagToQualifierRegex(originalPattern string) string {
	// Modify patterns specifying end-of-string to optionally have encryption flag at the end.
	// Note: This isn't resilient to really complex regex patterns.
	if strings.HasSuffix(originalPattern, "$") && !strings.HasSuffix(originalPattern, "\\$") {
		patternWithoutEndMarker := strings.TrimSuffix(originalPattern, "$")
		return fmt.Sprintf("(%s(%s)?$)", patternWithoutEndMarker, ESCAPED_ENCRYPTION_FLAG_FOR_REGEX)
	}
	return fmt.Sprintf("(%s(%s)?)", originalPattern, ESCAPED_ENCRYPTION_FLAG_FOR_REGEX)
}

func valueIsEncrypted(qualifier string) bool {
	return strings.HasSuffix(qualifier, ENCRYPTION_FLAG)
}

func (s *BigtableProxyServer) convertProtoMutations(
	ctx context.Context,
	tableConfig *TableConfig,
	protoMutations []*btpb.Mutation,
	tenantDetails *tenantproto.Tenant,
) (*bigtable.Mutation, error) {
	if len(protoMutations) == 0 {
		log.Debug().Ctx(ctx).Msg("no mutations to convert")
		return nil, nil
	}

	mutation := bigtable.NewMutation()

	for _, m := range protoMutations {
		switch m := m.Mutation.(type) {
		case *btpb.Mutation_SetCell_:
			encrypt := tenantcrypto.HasSealKey(tenantDetails)
			encryptionStatus := strconv.FormatBool(encrypt)
			ChunkSizeBytes.WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName(), "write", encryptionStatus).Observe(float64(len(m.SetCell.Value)))
			if encrypt {
				encryptedQualifier := addEncryptedFlagToQualifier(string(m.SetCell.ColumnQualifier))
				startTime := time.Now()
				encryptedValue, err := s.tenantCrypto.Seal(ctx, tenantDetails, m.SetCell.Value)
				CryptoLatency.WithLabelValues(
					tenantwatcherclient.MetricsTenantName(tenantDetails),
					tableConfig.GetRealTableName(),
					"seal",
					tenantcrypto.GetStatusString(err),
				).Observe(time.Since(startTime).Seconds())

				if err != nil {
					return nil, err
				}
				mutation.Set(m.SetCell.FamilyName,
					encryptedQualifier,
					bigtable.Timestamp(m.SetCell.TimestampMicros),
					encryptedValue)
			} else {
				mutation.Set(m.SetCell.FamilyName,
					string(m.SetCell.ColumnQualifier),
					bigtable.Timestamp(m.SetCell.TimestampMicros),
					m.SetCell.Value)
			}
		case *btpb.Mutation_DeleteFromColumn_:
			mutation.DeleteCellsInColumn(m.DeleteFromColumn.FamilyName,
				string(m.DeleteFromColumn.ColumnQualifier))
			// Also delete equivalent encrypted data column family.
			mutation.DeleteCellsInColumn(m.DeleteFromColumn.FamilyName,
				addEncryptedFlagToQualifier(string(m.DeleteFromColumn.ColumnQualifier)))
		case *btpb.Mutation_DeleteFromFamily_:
			mutation.DeleteCellsInFamily(m.DeleteFromFamily.FamilyName)
		case *btpb.Mutation_DeleteFromRow_:
			mutation.DeleteRow()
		default:
			return nil, status.Errorf(codes.Internal, "unsupported mutation type: %T", m)
		}
	}

	return mutation, nil
}

// ChunkFilterFunc is a function that determines whether a chunk should be included in the results
type ChunkFilterFunc func(chunk *btpb.ReadRowsResponse_CellChunk) bool

func (s *BigtableProxyServer) convertRowToChunks(
	ctx context.Context,
	row bigtable.Row,
	tenantDetails *tenantproto.Tenant,
	userID *string,
	tableConfig *TableConfig,
	filterFunc ChunkFilterFunc,
) ([]*btpb.ReadRowsResponse_CellChunk, int) {
	chunks := make([]*btpb.ReadRowsResponse_CellChunk, 0)
	decryptErrorCount := 0
	rowKey := []byte(row.Key())
	newRowKey, err := tableConfig.RemovePrefixFromRowKey(rowKey, tenantDetails.Id, userID)
	if err != nil {
		return nil, 0
	}
	rowKey = newRowKey

	for family, cols := range row {
		for _, col := range cols {

			familyName := &wrapperspb.StringValue{Value: family}
			qualifier := strings.TrimPrefix(col.Column, family+":")
			value := col.Value
			isEncrypted := valueIsEncrypted(qualifier)
			var encryptionStatus string
			if len(value) == 0 {
				encryptionStatus = "empty"
			} else {
				encryptionStatus = strconv.FormatBool(isEncrypted)
			}
			ChunkSizeBytes.WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName(), "read", encryptionStatus).Observe(float64(len(value)))
			if isEncrypted {
				// Remove the encrypted flag from the qualifier and decrypt the data
				qualifier = removeEncryptedFlagFromQualifier(string(qualifier))
				if len(value) > 0 { // Strip transformer may remove the value
					startTime := time.Now()
					decrypted, err := s.tenantCrypto.Open(ctx, tenantDetails, value)
					duration := time.Since(startTime).Seconds()
					CryptoLatency.WithLabelValues(
						tenantwatcherclient.MetricsTenantName(tenantDetails),
						tableConfig.GetRealTableName(),
						"open",
						tenantcrypto.GetStatusString(err),
					).Observe(duration)

					if err != nil {
						log.Warn().Ctx(ctx).Err(err).Msg("failed to decrypt value")
						decryptErrorCount++
						continue
					}
					value = decrypted
				}
			}
			chunk := &btpb.ReadRowsResponse_CellChunk{
				RowKey:          rowKey,
				FamilyName:      familyName,
				Qualifier:       &wrapperspb.BytesValue{Value: []byte(qualifier)},
				TimestampMicros: int64(col.Timestamp),
				Value:           value,
				Labels:          col.Labels,
			}

			// Drop encrypted chunks that did not pass the filter.
			// Cleartext chunks have already passed the filter at bigtable.
			if isEncrypted && !filterFunc(chunk) {
				continue
			}

			chunks = append(chunks, chunk)
		}
	}

	// Mark the last chunk as committed
	if len(chunks) > 0 {
		chunks[len(chunks)-1].RowStatus = &btpb.ReadRowsResponse_CellChunk_CommitRow{
			CommitRow: true,
		}
	}

	return chunks, decryptErrorCount
}

// logResponse logs RPC responses as either debug or error depending on the status
func logResponse(err error, callsite string, ioSequenceNumber string) error {
	if err != nil {
		log.Error().Msgf("[%s] response at %s: err %v", ioSequenceNumber, callsite, err)
	} else {
		log.Debug().Msgf("[%s] response at %s: success", ioSequenceNumber, callsite)
	}
	return err
}

func getTenantID(claims *auth.AugmentClaims, reqTenantID string) (string, error) {
	claimTenantID, _ := claims.GetTenantID()
	switch {
	case claims.AllowsAllTenants() && reqTenantID == "":
		return "", status.Error(codes.Internal, "tenant_id must be set in either token or request")
	case claims.AllowsAllTenants():
		return reqTenantID, nil
	case reqTenantID == "":
		return claimTenantID, nil
	case claimTenantID != reqTenantID:
		log.Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", claimTenantID, reqTenantID)
		return "", status.Error(codes.PermissionDenied, "different tenant ID in token and request")
	default:
		return claimTenantID, nil
	}
}

func GetUserID(claims *auth.AugmentClaims, reqUserID string, userKeyedTable bool) (*string, error) {
	if reqUserID != "" && !userKeyedTable {
		return nil, status.Error(codes.InvalidArgument, "user_id is not allowed in this table")
	}
	if !userKeyedTable {
		return nil, nil
	}
	if claims.ServiceName == "" && claims.OpaqueUserIDType != authentitiesproto.UserId_INTERNAL_IAP.String() {
		if reqUserID == "" {
			return &claims.OpaqueUserID, nil
		}
		// a user can specify themselves
		if reqUserID == claims.OpaqueUserID {
			return &reqUserID, nil
		}
		// only IAP tokens and service tokens are allowed to specify a user ID
		return nil, status.Error(codes.PermissionDenied, "Not allowed to specify user_id")
	}
	// background or IAP
	if reqUserID == "" {
		// needs to be set
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}
	return &reqUserID, nil
}

type BigTableOp int

const (
	READ BigTableOp = iota
	WRITE
	DELETE
)

// validateAndGetRequestDetails validates the tenant ID and user ID in the request and returns the tenant details and user ID
func (s *BigtableProxyServer) validateAndGetRequestDetails(ctx context.Context, reqTenantID string, reqUserID string, reqTableName bigtableproto.TableName, op BigTableOp) (*tenantproto.Tenant, *string, error) {
	claims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, nil, status.Error(codes.Internal, "failed to get auth claims from context")
	}

	// Validate and get tenant ID
	tenantID, err := getTenantID(claims, reqTenantID)
	if err != nil {
		return nil, nil, err
	}

	// Get table config and validate claims scope
	tableConfig, err := s.tableManager.GetConfig(reqTableName)
	if err != nil {
		return nil, nil, err
	}

	userId, err := GetUserID(claims, reqUserID, tableConfig.userKeyedTable)
	if err != nil {
		return nil, nil, err
	}

	// Get tenantDetails info from cache. Only tenants in the service's namespace should be in the cache.
	tenantDetails, err := s.tenantCache.GetTenant(ctx, tenantID)
	if err != nil {
		log.Error().Ctx(ctx).Err(err).Msgf("Failed to get tenant id %s info from cache", tenantID)
		return nil, nil, status.Errorf(codes.Internal, "failed to validate tenant")
	}

	// For namespace-only claims, verify namespace access permission
	if claims.AllowsAllTenants() {
		if claims.ShardNamespace == "" {
			return nil, nil, status.Error(codes.PermissionDenied, "claim not specific to tenant or namespace")
		}

		if claims.ShardNamespace != tenantDetails.ShardNamespace &&
			claims.ShardNamespace != tenantDetails.OtherNamespace {
			log.Error().Ctx(ctx).Msgf("Auth claims give permission for namespace %s, but tenant %s is in namespace %s",
				claims.ShardNamespace, tenantID, tenantDetails.ShardNamespace)
			return nil, nil, status.Error(codes.PermissionDenied, "tenant not in authorized namespace")
		}
	}

	requiredScope, err := tableConfig.GetRequiredScope(op)
	if err != nil {
		return nil, nil, err
	}

	if !claims.HasScope(requiredScope) {
		return nil, nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	return tenantDetails, userId, nil
}

// wrapWithLogging wraps an RPC handler with logging and cancellation handling
func (s *BigtableProxyServer) wrapWithLogging(
	ctx context.Context,
	callsite string,
	ioSequenceNumber string,
	f func() error,
) error {
	// Handle context cancellation
	err := f()
	if ctx.Err() == context.Canceled {
		log.Error().Ctx(ctx).Msgf("[%s] cancelled on request of client", ioSequenceNumber)
		return status.Error(codes.Canceled, "client cancelled request")
	}
	return logResponse(err, callsite, ioSequenceNumber)
}

func (s *BigtableProxyServer) ReadRows(
	req *bigtableproto.ReadRowsRequest,
	stream bigtableproto.BigtableProxy_ReadRowsServer,
) error {
	ctx := stream.Context()

	readID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLogging(ctx, "read_rows", readID, func() error {
		tenantDetails, reqUserId, err := s.validateAndGetRequestDetails(ctx, req.GetTenantId(), req.GetUserId(), req.GetTableName(), READ)
		if err != nil {
			return err
		}

		tableConfig, err := s.tableManager.GetConfig(req.GetTableName())
		if err != nil {
			return err
		}

		keyCount := len(req.GetRows().GetRowKeys())
		rangeCount := len(req.GetRows().GetRowRanges())

		rowSet, err := tableConfig.ConvertProtoRowSet(req.GetRows(), tenantDetails.Id, reqUserId)
		if err != nil {
			return err
		}

		filter, err := convertProtoFilter(req.GetFilter())
		if err != nil {
			return err
		}

		// If filter depends on values, must process filter after data fetch based on other filters.
		var chunkFilter ChunkFilterFunc
		if filterDependsOnValue(req.GetFilter()) {
			// Create a filter function that applies the filter locally
			chunkFilter = func(chunk *btpb.ReadRowsResponse_CellChunk) bool {
				pass, err := evaluateProtoFilterLocally(req.GetFilter(), chunk)
				if err != nil {
					log.Warn().Ctx(ctx).Err(err).Msg("failed to evaluate filter locally")
					return false
				}
				return pass
			}
		} else {
			// No local filtering needed, accept all chunks
			chunkFilter = func(chunk *btpb.ReadRowsResponse_CellChunk) bool {
				return true
			}
		}

		ReadRequestRowHistogram.
			WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName()).
			Observe(float64(keyCount))

		claims, ok := auth.GetAugmentClaims(ctx)
		if !ok {
			return status.Error(codes.Internal, "failed to get auth claims from context")
		}
		_, ok = claims.GetIapEmail()
		if ok {
			requestContext, _ := requestcontext.FromGrpcContext(ctx)
			s.auditLogger.WriteAuditLog(
				claims,
				fmt.Sprintf("Read rows %v from %s in tenant %s", req.Rows, tableConfig.GetRealTableName(), tenantDetails.Name),
				audit.NewTenantName(tenantDetails.Name),
				requestContext,
				audit.NewProtoRequest(req),
				audit.NewTenantID(tenantDetails.Id),
			)
		}

		// Start streaming in a goroutine
		errChan := make(chan error, 1)
		go func() {
			defer close(errChan)

			// Protect against bigtable stalling
			timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
			defer cancel()

			table := s.openTable(tableConfig)

			var bigtableLatency time.Duration
			var sendLatency time.Duration
			var syncLatency time.Duration
			streamingResponseCount := 0
			chunkCount := 0
			totalLen := 0
			var streamErr error

			opts := []bigtable.ReadOption{}
			if req.GetRowsLimit() > 0 {
				opts = append(opts, bigtable.LimitRows(int64(req.GetRowsLimit())))
			}
			if filter != nil {
				opts = append(opts, bigtable.RowFilter(filter))
			}

			prevTime := time.Now()
			err := table.ReadRows(timeoutCtx, rowSet, func(row bigtable.Row) bool {
				bigtableLatency += time.Since(prevTime)
				prevTime = time.Now()

				chunks, decryptErrorCount := s.convertRowToChunks(ctx,
					row, tenantDetails, reqUserId, tableConfig, chunkFilter)
				if decryptErrorCount > 0 {
					log.Warn().Ctx(ctx).Msgf("[%s] failed to decrypt %d chunks in row %s", readID, decryptErrorCount, string(row.Key()))
					ReadDecryptionErrors.WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName()).Add(float64(decryptErrorCount))
				}

				syncLatency += time.Since(prevTime)
				prevTime = time.Now()

				if len(chunks) == 0 {
					return true // Continue to next row
				}

				resp := &bigtableproto.ReadRowsResponse{
					Chunks: chunks,
				}

				totalLen += len(chunks)
				chunkCount += len(chunks)
				streamingResponseCount++

				if err := stream.Send(resp); err != nil {
					streamErr = err
					return false // Stop iteration
				}

				sendLatency += time.Since(prevTime)
				prevTime = time.Now()

				return true // Continue iteration
			}, opts...)

			latencyThreshold, _ := s.featureFlags.GetFloat("bigtable_proxy_log_latency_threshold_s", 0.1)

			// We can have many bigtable reads per request. Log only slow (>50ms) reads and errors
			if streamErr != nil || bigtableLatency.Seconds()+sendLatency.Seconds()+syncLatency.Seconds() > latencyThreshold {
				log.Info().Ctx(ctx).Msgf("[%s] finished reading %d keys and %d ranges in %s, error %v, timings: bigtable=%.3f send=%.3f sync=%.3f streaming_response_count=%d chunk_count=%d len=%d",
					readID,
					keyCount,
					rangeCount,
					tableConfig.GetRealTableName(),
					streamErr,
					bigtableLatency.Seconds(),
					sendLatency.Seconds(),
					syncLatency.Seconds(),
					streamingResponseCount,
					chunkCount,
					totalLen,
				)
			}

			ReadRequestLatencyCollector.
				WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName(), tenantDetails.Id).
				Observe(bigtableLatency.Seconds())
			if keyCount > 0 && rangeCount == 0 {
				ReadRequestLatencyPerRowCollector.
					WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName(), tenantDetails.Id).
					Observe(bigtableLatency.Seconds() / float64(keyCount))
			}

			ReadResponseChunksHistogram.
				WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName()).
				Observe(float64(chunkCount))

			var finalErr error
			if streamErr != nil {
				finalErr = status.Errorf(codes.Internal, "failed to stream response: %v", streamErr)
			} else {
				finalErr = err
			}
			errChan <- finalErr
		}()

		// Wait for completion
		return <-errChan
	})
}

func (s *BigtableProxyServer) MutateRows(
	req *bigtableproto.MutateRowsRequest,
	stream bigtableproto.BigtableProxy_MutateRowsServer,
) error {
	ctx := stream.Context()

	mutateID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLogging(ctx, "mutate_rows", mutateID, func() error {
		tenantDetails, reqUserId, err := s.validateAndGetRequestDetails(ctx, req.GetTenantId(), req.GetUserId(), req.GetTableName(), WRITE)
		if err != nil {
			return err
		}

		tableConfig, err := s.tableManager.GetConfig(req.GetTableName())
		if err != nil {
			return err
		}

		for _, entry := range req.GetEntries() {
			entry.RowKey, err = tableConfig.AddPrefixToRowKey(entry.RowKey, tenantDetails.Id, reqUserId, true)
			if err != nil {
				return err
			}
		}

		MutateRequestEntriesHistogram.
			WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName()).
			Observe(float64(len(req.GetEntries())))

		// Start streaming in a goroutine
		errChan := make(chan error, 1)
		go func() {
			defer close(errChan)
			prevTime := time.Now()

			table := s.openTable(tableConfig)
			var streamErr error

			// Convert entries to bigtable.Mutation
			rowKeys := make([]string, len(req.GetEntries()))
			muts := make([]*bigtable.Mutation, len(req.GetEntries()))

			for i, entry := range req.GetEntries() {
				rowKeys[i] = string(entry.RowKey)
				converted, err := s.convertProtoMutations(ctx, tableConfig, entry.Mutations, tenantDetails)
				if err != nil {
					streamErr = err
					errChan <- streamErr
					return
				}
				muts[i] = converted
			}

			claims, ok := auth.GetAugmentClaims(ctx)
			if !ok {
				streamErr = status.Error(codes.Internal, "failed to get auth claims from context")
				errChan <- streamErr
				return
			}
			_, ok = claims.GetIapEmail()
			if ok {
				requestContext, _ := requestcontext.FromGrpcContext(ctx)
				s.auditLogger.WriteAuditLog(
					claims,
					fmt.Sprintf("Mutate rows %v from %s in tenant %s", rowKeys, tableConfig.GetRealTableName(), tenantDetails.Name),
					audit.NewTenantName(tenantDetails.Name),
					requestContext,
					audit.NewProtoRequest(req),
					audit.NewTenantID(tenantDetails.Id),
				)
			}

			// Protect against bigtable stalling
			timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
			defer cancel()

			errs, err := table.ApplyBulk(timeoutCtx, rowKeys, muts)
			if err != nil {
				streamErr = err
			}

			// Convert errors to response
			resp := &bigtableproto.MutateRowsResponse{
				Entries: make([]*btpb.MutateRowsResponse_Entry, len(rowKeys)),
			}

			for i := range rowKeys {
				entry := &btpb.MutateRowsResponse_Entry{
					Index: int64(i),
				}
				if i < len(errs) && errs[i] != nil {
					entry.Status = status.Convert(errs[i]).Proto()
				} else {
					entry.Status = status.New(codes.OK, "").Proto()
				}
				resp.Entries[i] = entry
			}

			latency := time.Since(prevTime)
			if err := stream.Send(resp); err != nil {
				streamErr = status.Errorf(codes.Internal, "failed to stream response: %v", err)
			}

			latencyThreshold, _ := s.featureFlags.GetFloat("bigtable_proxy_log_latency_threshold_s", 0.1)

			if streamErr != nil || latency.Seconds() > latencyThreshold {
				// Bigtable mutations are a large contributor to our logging. Log only slow (>100ms) operations and errors
				log.Info().Ctx(ctx).Msgf("[%s] finished mutating %d entries in %s, error %v, latency: %.3f",
					mutateID,
					len(req.GetEntries()),
					tableConfig.GetRealTableName(),
					streamErr,
					latency.Seconds(),
				)
			}
			errChan <- streamErr
		}()

		// Wait for completion
		return <-errChan
	})
}

func (s *BigtableProxyServer) DeleteRows(
	req *bigtableproto.DeleteRowsRequest,
	stream bigtableproto.BigtableProxy_DeleteRowsServer,
) error {
	ctx := stream.Context()

	mutateID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLogging(ctx, "delete_rows", mutateID, func() error {
		tenantDetails, reqUserId, err := s.validateAndGetRequestDetails(ctx, req.GetTenantId(), req.GetUserId(), req.GetTableName(), DELETE)
		if err != nil {
			return err
		}

		tableConfig, err := s.tableManager.GetConfig(req.GetTableName())
		if err != nil {
			return err
		}

		rowKeys := make([]string, len(req.GetRowKeys()))
		for i, entry := range req.GetRowKeys() {
			newKey, err := tableConfig.AddPrefixToRowKey(entry, tenantDetails.Id, reqUserId, true)
			if err != nil {
				return err
			}
			rowKeys[i] = string(newKey)
		}

		log.Info().Ctx(ctx).Msgf("[%s] ", mutateID, len(rowKeys), tableConfig.GetRealTableName())

		MutateRequestEntriesHistogram.
			WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName()).
			Observe(float64(len(rowKeys)))

		claims, ok := auth.GetAugmentClaims(ctx)
		if !ok {
			return status.Error(codes.Internal, "failed to get auth claims from context")
		}
		_, ok = claims.GetIapEmail()
		if ok {
			s.auditLogger.WriteAuditLog(
				claims,
				fmt.Sprintf("Remove rows %v from %s in tenant %s", rowKeys, tableConfig.GetRealTableName(), tenantDetails.Name),
				audit.NewTenantName(tenantDetails.Name),
				audit.NewProtoRequest(req),
				audit.NewTenantID(tenantDetails.Id),
			)
		}

		// Start streaming in a goroutine
		errChan := make(chan error, 1)
		go func() {
			defer close(errChan)

			table := s.openTable(tableConfig)
			var streamErr error

			// Convert entries to bigtable.Mutation
			muts := make([]*bigtable.Mutation, len(rowKeys))

			for i := range rowKeys {
				muts[i] = bigtable.NewMutation()
				muts[i].DeleteRow()
			}

			// Protect against bigtable stalling
			timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
			defer cancel()

			errs, err := table.ApplyBulk(timeoutCtx, rowKeys, muts)
			if err != nil {
				streamErr = err
			}

			// Convert errors to response
			resp := &bigtableproto.DeleteRowsResponse{
				Entries: make([]*btpb.MutateRowsResponse_Entry, len(rowKeys)),
			}

			for i := range rowKeys {
				entry := &btpb.MutateRowsResponse_Entry{
					Index: int64(i),
				}
				if i < len(errs) && errs[i] != nil {
					entry.Status = status.Convert(errs[i]).Proto()
				} else {
					entry.Status = status.New(codes.OK, "").Proto()
				}
				resp.Entries[i] = entry
			}

			if err := stream.Send(resp); err != nil {
				streamErr = status.Errorf(codes.Internal, "failed to stream response: %v", err)
			}

			log.Info().Ctx(ctx).Msgf("[%s] finished deleting %d entries in %s, error %v",
				mutateID,
				len(rowKeys),
				tableConfig.GetRealTableName(),
				streamErr,
			)
			errChan <- streamErr
		}()

		// Wait for completion
		return <-errChan
	})
}

func (s *BigtableProxyServer) DeleteNonEncryptedRows(
	ctx context.Context,
	req *bigtableproto.DeleteNonEncryptedRowsRequest,
) (*bigtableproto.DeleteNonEncryptedRowsResponse, error) {
	mutateID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	err := s.wrapWithLogging(ctx, "delete_non_encrypted_rows", mutateID, func() error {
		tenantDetails, reqUserId, err := s.validateAndGetRequestDetails(ctx, req.GetTenantId(), req.GetUserId(), req.GetTableName(), DELETE)
		if err != nil {
			return err
		}
		if tenantDetails.EncryptionKeyName == "" {
			return status.Error(codes.InvalidArgument, "Tenant does not have encryption enabled")
		}

		tableConfig, err := s.tableManager.GetConfig(req.GetTableName())
		if err != nil {
			return err
		}

		table := s.openTable(tableConfig)

		claims, ok := auth.GetAugmentClaims(ctx)
		if !ok {
			return status.Error(codes.Internal, "failed to get auth claims from context")
		}
		_, ok = claims.GetIapEmail()
		if ok {
			s.auditLogger.WriteAuditLog(
				claims,
				fmt.Sprintf("Remove non-encrypted data from %s in tenant %s", tableConfig.GetRealTableName(), tenantDetails.Name),
				audit.NewTenantName(tenantDetails.Name),
				audit.NewProtoRequest(req),
				audit.NewTenantID(tenantDetails.Id),
			)
		}

		batchSize := req.GetBatchSize()
		if batchSize <= 0 {
			batchSize = 10000
		}
		log.Ctx(ctx).Info().Msgf("[%s] DeleteNonEncryptedRows: Starting non-encrypted data deletion in %s with batch size %d", mutateID, req.GetTableName(), batchSize)

		// Build a RowSet that targets only this tenant/user's rows
		rowSet, err := tableConfig.ConvertProtoRowSet(&btpb.RowSet{}, tenantDetails.Id, reqUserId)
		if err != nil {
			return err
		}

		deletionFunc := func(ctx context.Context) error {
			var affectedRows, allRows int64

			var nextStartKey string

			for {
				if nextStartKey != "" {
					key, err := tableConfig.RemovePrefixFromRowKey([]byte(nextStartKey), tenantDetails.Id, reqUserId)
					if err != nil {
						log.Error().Ctx(ctx).Err(err).Msgf("[%s] DeleteNonEncryptedRows: Failed to remove prefix from row key %s", mutateID, nextStartKey)
						return err
					}
					rowSet, err = tableConfig.ConvertProtoRowSet(&btpb.RowSet{
						RowRanges: []*btpb.RowRange{
							{
								StartKey: &btpb.RowRange_StartKeyOpen{StartKeyOpen: key},
							},
						},
					}, tenantDetails.Id, reqUserId)
					if err != nil {
						log.Error().Ctx(ctx).Err(err).Msgf("[%s] DeleteNonEncryptedRows: Failed to convert row set for non-encrypted data deletion in %s", mutateID, req.GetTableName())
						return err
					}
				}
				// It would be nice to use a column qualifier filter to only read
				// non-encrypted columns here, but I couldn't find a supported regex
				// that could do that since lookbehinds are not supported.
				rowsInBatch := 0
				var keysToMutate []string
				var mutations []*bigtable.Mutation
				opts := []bigtable.ReadOption{
					// Read one batch at a time
					bigtable.LimitRows(int64(batchSize)),
					// Strip values, we only need column names here
					bigtable.RowFilter(bigtable.StripValueFilter()),
				}
				err = table.ReadRows(ctx, rowSet, func(row bigtable.Row) bool {
					mutation := bigtable.NewMutation()
					hasNonEncryptedData := false

					// Check each column and delete non-encrypted ones
					for family, values := range row {
						for _, val := range values {
							column := strings.TrimPrefix(val.Column, family+":")
							if !valueIsEncrypted(column) {
								mutation.DeleteCellsInColumn(family, column)
								hasNonEncryptedData = true
							}
						}
					}

					if hasNonEncryptedData {
						keysToMutate = append(keysToMutate, row.Key())
						mutations = append(mutations, mutation)
					}
					rowsInBatch++
					nextStartKey = row.Key()
					return true
				}, opts...)
				if err != nil {
					log.Error().Ctx(ctx).Err(err).Msgf("[%s] DeleteNonEncryptedRows: Failed to read rows for non-encrypted data deletion in %s, affected %d rows of %d", mutateID, req.GetTableName(), affectedRows, allRows)
					return err
				}
				allRows += int64(rowsInBatch)
				if len(keysToMutate) > 0 {
					errs, err := table.ApplyBulk(ctx, keysToMutate, mutations)
					allErrs := errors.Join(append(errs, err)...)
					if allErrs != nil {
						log.Error().Ctx(ctx).Err(err).Msgf("[%s] DeleteNonEncryptedRows: Failed to apply mutations for non-encrypted data deletion in %s", mutateID, req.GetTableName())
						return allErrs
					}
					affectedRows += int64(len(keysToMutate))
				}

				log.Info().Ctx(ctx).Msgf("[%s] DeleteNonEncryptedRows progress: deleted cells in %d rows of non-encrypted data from %d rows in %s", mutateID, affectedRows, allRows, req.GetTableName())

				// Exit the loop when we run out of rows to read
				if rowsInBatch < int(batchSize) {
					break
				}
			}

			log.Info().Ctx(ctx).Msgf("[%s] DeleteNonEncryptedRows finished: deleted cells in %d rows of non-encrypted data from %d rows in %s, error %v",
				mutateID, affectedRows, allRows, req.GetTableName(), err)
			return nil
		}

		if req.GetAsync() {
			go deletionFunc(context.Background())
			return nil
		}

		return deletionFunc(ctx)
	})

	return &bigtableproto.DeleteNonEncryptedRowsResponse{}, err
}

func (s *BigtableProxyServer) wrapWithLoggingCheckAndMutate(
	ctx context.Context,
	callsite string,
	id string,
	f func() (*bigtableproto.CheckAndMutateRowResponse, error),
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	// Handle context cancellation
	resp, err := f()
	if ctx.Err() == context.Canceled {
		log.Error().Ctx(ctx).Msgf("[%s] cancelled on request of client", id)
		return nil, status.Error(codes.Canceled, "client cancelled request")
	}
	if err != nil {
		log.Error().Ctx(ctx).Msgf("[%s] response at %s: err %v", id, callsite, err)
	} else {
		log.Debug().Ctx(ctx).Msgf("[%s] response at %s: success", id, callsite)
	}
	return resp, err
}

func (s *BigtableProxyServer) CheckAndMutateRow(
	ctx context.Context,
	req *bigtableproto.CheckAndMutateRowRequest,
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	operationID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLoggingCheckAndMutate(ctx, "check_and_mutate_row", operationID, func() (*bigtableproto.CheckAndMutateRowResponse, error) {
		tenantDetails, reqUserId, err := s.validateAndGetRequestDetails(ctx, req.GetTenantId(), req.GetUserId(), req.GetTableName(), WRITE)
		if err != nil {
			return nil, err
		}

		// Protect against bigtable stalling
		timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
		defer cancel()

		if filterDependsOnValue(req.GetPredicateFilter()) {
			return s.handleReadCheckModifyRow(timeoutCtx, operationID, tenantDetails, reqUserId, req)
		}
		return s.handleConditionalMutateRow(timeoutCtx, operationID, tenantDetails, reqUserId, req)
	})
}

func (s *BigtableProxyServer) handleReadCheckModifyRow(
	ctx context.Context,
	operationID string,
	tenantDetails *tenantproto.Tenant,
	reqUserId *string,
	req *bigtableproto.CheckAndMutateRowRequest,
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	tableConfig, err := s.tableManager.GetConfig(req.GetTableName())
	if err != nil {
		return nil, err
	}

	table := s.openTable(tableConfig)
	rowKey, err := tableConfig.AddPrefixToRowKey(req.GetRowKey(), tenantDetails.Id, reqUserId, true)
	if err != nil {
		return nil, err
	}

	// Convert the predicate filter
	filter, err := convertProtoFilter(req.GetPredicateFilter())
	if err != nil {
		return nil, err
	}

	chunkFilter := func(chunk *btpb.ReadRowsResponse_CellChunk) bool {
		pass, err := evaluateProtoFilterLocally(req.GetPredicateFilter(), chunk)
		if err != nil {
			log.Warn().Ctx(ctx).Err(err).Msg("failed to evaluate filter locally")
			return false
		}
		return pass
	}

	// Read and evaluate predicate
	var matched bool = false
	var originalRow bigtable.Row

	rowSet := bigtable.SingleRow(string(rowKey))
	decryptErrorCount := 0
	err = table.ReadRows(ctx, rowSet, func(row bigtable.Row) bool {
		chunks, decryptErrorCount := s.convertRowToChunks(ctx, row, tenantDetails, reqUserId, tableConfig, chunkFilter)
		if decryptErrorCount > 0 {
			log.Warn().Ctx(ctx).Msgf("[%s] failed to decrypt %d chunks in row %s", operationID, decryptErrorCount, string(row.Key()))
			ReadDecryptionErrors.WithLabelValues(tenantwatcherclient.MetricsTenantName(tenantDetails), tableConfig.GetRealTableName()).Add(float64(decryptErrorCount))
		}
		if len(chunks) != 0 {
			matched = true
		}
		originalRow = row
		return true
	}, bigtable.RowFilter(filter))
	if err != nil {
		return nil, err
	}
	if decryptErrorCount > 0 {
		return nil, status.Errorf(codes.Internal, "failed to decrypt %d chunks in row %s", decryptErrorCount, string(rowKey))
	}

	// Create value check filter
	var valueCheckFilters []bigtable.Filter
	for family, cols := range originalRow {
		for _, col := range cols {
			colName := strings.TrimPrefix(col.Column, family+":")
			value := col.Value
			cellFilter := bigtable.ChainFilters(
				bigtable.FamilyFilter(binaryregexp.QuoteMeta(family)),
				bigtable.ColumnFilter(binaryregexp.QuoteMeta(colName)),
				bigtable.ValueFilter(binaryregexp.QuoteMeta(string(value))),
			)
			valueCheckFilters = append(valueCheckFilters, cellFilter)
		}
	}

	if len(valueCheckFilters) == 0 {
		// For value-based predicates, having no values to check is unexpected
		// Apply false mutations directly since there's nothing to match
		falseMut, err := s.convertProtoMutations(ctx, tableConfig, req.GetFalseMutations(), tenantDetails)
		if err != nil {
			return nil, err
		}
		if falseMut == nil {
			return &bigtableproto.CheckAndMutateRowResponse{
				PredicateMatched: false,
			}, nil
		}
		if err := table.Apply(ctx, string(rowKey), falseMut); err != nil {
			return nil, err
		}
		return &bigtableproto.CheckAndMutateRowResponse{
			PredicateMatched: false,
		}, nil
	}

	var valueCheckFilter bigtable.Filter
	if len(valueCheckFilters) == 1 {
		valueCheckFilter = valueCheckFilters[0]
	} else {
		valueCheckFilter = bigtable.InterleaveFilters(valueCheckFilters...)
	}
	valueCheckFilter = bigtable.ChainFilters(valueCheckFilter, bigtable.LatestNFilter(1))

	// Convert appropriate mutation based on predicate match
	var mutToApply *bigtable.Mutation
	if matched {
		mutToApply, err = s.convertProtoMutations(ctx, tableConfig, req.GetTrueMutations(), tenantDetails)
	} else {
		mutToApply, err = s.convertProtoMutations(ctx, tableConfig, req.GetFalseMutations(), tenantDetails)
	}
	if err != nil {
		return nil, err
	}

	if mutToApply == nil {
		return &bigtableproto.CheckAndMutateRowResponse{
			PredicateMatched: matched,
		}, nil
	}

	// Apply conditional mutation with value check
	condMut := bigtable.NewCondMutation(valueCheckFilter, mutToApply, nil)
	var finalMatched bool
	matchResult := bigtable.GetCondMutationResult(&finalMatched)
	if err = table.Apply(ctx, string(rowKey), condMut, matchResult); err != nil {
		return nil, err
	}

	if !finalMatched {
		return nil, status.Error(codes.Aborted, "row was modified between read and mutation")
	}

	return &bigtableproto.CheckAndMutateRowResponse{
		PredicateMatched: matched,
	}, nil
}

func (s *BigtableProxyServer) handleConditionalMutateRow(
	ctx context.Context,
	operationID string,
	tenantDetails *tenantproto.Tenant,
	reqUserId *string,
	req *bigtableproto.CheckAndMutateRowRequest,
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	tableConfig, err := s.tableManager.GetConfig(req.GetTableName())
	if err != nil {
		return nil, err
	}

	rowKey, err := tableConfig.AddPrefixToRowKey(req.GetRowKey(), tenantDetails.Id, reqUserId, true)
	if err != nil {
		return nil, err
	}

	table := s.openTable(tableConfig)

	// Convert the predicate filter
	filter, err := convertProtoFilter(req.GetPredicateFilter())
	if err != nil {
		return nil, err
	}
	// Lack of filter should apply true mutations. Matches Bigtable behavior.
	if filter == nil {
		log.Warn().Ctx(ctx).Msg("No predicate filter provided, applying true mutations only. Just use MutateRows instead.")
		filter = bigtable.PassAllFilter()
	}

	// Convert the mutations
	trueMut, err := s.convertProtoMutations(ctx, tableConfig, req.GetTrueMutations(), tenantDetails)
	if err != nil {
		return nil, err
	}
	falseMut, err := s.convertProtoMutations(ctx, tableConfig, req.GetFalseMutations(), tenantDetails)
	if err != nil {
		return nil, err
	}

	condMut := bigtable.NewCondMutation(filter, trueMut, falseMut)

	var matched bool
	matchResult := bigtable.GetCondMutationResult(&matched)
	err = table.Apply(ctx, string(rowKey), condMut, matchResult)
	if err != nil {
		return nil, err
	}

	return &bigtableproto.CheckAndMutateRowResponse{
		PredicateMatched: matched,
	}, nil
}

func (s *BigtableProxyServer) CheckTenantDataAccess(
	ctx context.Context,
	req *bigtableproto.CheckTenantDataAccessRequest,
) (*bigtableproto.CheckTenantDataAccessResponse, error) {
	tenantDetails, err := s.tenantCache.GetTenant(ctx, req.GetTenantId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get tenant details: %v", err)
	}

	// Check if key is accessible if the tenant is encrypted
	if tenantcrypto.HasSealKey(tenantDetails) {
		err := s.tenantCrypto.CheckKeyAccess(ctx, tenantDetails)
		if err != nil {
			// Record the key access check failure
			KeyAccessCheckCounter.WithLabelValues(
				tenantwatcherclient.MetricsTenantName(tenantDetails),
				tenantcrypto.GetStatusString(err),
			).Inc()

			return &bigtableproto.CheckTenantDataAccessResponse{
				HasAccess: false,
			}, err
		}

		// Record successful key access check
		KeyAccessCheckCounter.WithLabelValues(
			tenantwatcherclient.MetricsTenantName(tenantDetails),
			"success",
		).Inc()
	}

	return &bigtableproto.CheckTenantDataAccessResponse{
		HasAccess: true,
	}, nil
}
