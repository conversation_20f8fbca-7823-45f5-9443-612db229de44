local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

local alertNames = [
  'api-proxy-health-check-chat',
  'api-proxy-health-check-completion',
  'api-proxy-health-check-list-remote-tools',
  'api-proxy-health-check-model',
  'api-proxy-health-check-next-edit',
  'api-proxy-health-check-overall',
  'api-proxy-health-check-remote-agents-list',
  'api-proxy-health-check-smart-paste',
];
{
  deployment: [
    {
      name: 'api-proxy-health-check-monitoring-tombstone',
      kubecfg_tombstone: {
        object: [
          {
            apiVersion: 'monitoring.cnrm.cloud.google.com/v1beta1',
            kind: 'MonitoringAlertPolicy',
            name: name,
          }
          for name in alertNames
        ],
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
    },
    {
      name: 'api-proxy-health-check-tombstone',
      kubecfg_tombstone: {
        object: [{
          kind: 'app',
          name: 'api-proxy-health-check',
        }],
        task: tenantNamespaces.namespaces,
      },
    },
  ],
}
