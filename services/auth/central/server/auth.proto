syntax = "proto3";
package auth;

import "base/logging/audit_ocsf/audit_log.proto";
import "google/protobuf/timestamp.proto";
import "services/auth/central/server/auth_entities.proto";
import "services/token_exchange/auth_options.proto";

// Auth service is responsible for providing read access to authentication
// and authorization related objects such as tokens, users, etc.
service AuthService {
  // 1. Adds user if one does not exist (based on email)
  // 2. Associate user with tenant if needed.
  //
  // Does not fail if user already exists and/or is already associated with the tenant.
  // Fails if tenant does not exist. (Note: some tenant information comes from configuration)
  rpc AddUserToTenant(AddUserToTenantRequest) returns (AddUserToTenantResponse);

  // Get information on user/tenant association.
  rpc GetUserOnTenant(GetUserOnTenantRequest) returns (GetUserOnTenantResponse);

  // Update information on user/tenant association.
  rpc UpdateUserOnTenant(UpdateUserOnTenantRequest) returns (UpdateUserOnTenantResponse);

  // Remove association between user and tenant and all related information.
  rpc RemoveUserFromTenant(RemoveUserFromTenantRequest) returns (RemoveUserFromTenantResponse);

  // Update the user's email.
  rpc UpdateUserEmail(UpdateUserEmailRequest) returns (UpdateUserEmailResponse);

  // List all users in a tenant.
  rpc ListTenantUsers(ListTenantUsersRequest) returns (ListTenantUsersResponse);

  // get information about the user
  rpc GetUser(GetUserRequest) returns (GetUserResponse);

  // get repeated full user information given a list of user IDs
  rpc BatchGetUsers(BatchGetUsersRequest) returns (BatchGetUsersResponse);

  // Get Users with possible search filter
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse);

  // RPC for auth_query GetTokenInfo (in the process of migrating to use the global bigtable)
  // Note that auth_query will still be responsible for asking token exchange for a signed JWT.
  rpc GetTokenInfo(GetTokenInfoRequest) returns (GetTokenInfoResponse);

  // Revoke any user cookies used for authentication
  rpc RevokeUserCookies(RevokeUserCookiesRequest) returns (RevokeUserCookiesResponse);

  // Revoke user tokens and cookies used for authentication
  rpc RevokeUser(RevokeUserRequest) returns (RevokeUserResponse);

  // Revoke user tokens and cookies used for authentication by user ID
  rpc RevokeUserById(RevokeUserByIdRequest) returns (RevokeUserByIdResponse);

  // Add a suspension to a user.
  rpc CreateUserSuspension(CreateUserSuspensionRequest) returns (CreateUserSuspensionResponse);

  // Update suspension exemption for a user.
  rpc UpdateSuspensionExemption(UpdateSuspensionExemptionRequest) returns (UpdateSuspensionExemptionResponse);

  // Lift one or more suspensions from one user.
  rpc DeleteUserSuspensions(DeleteUserSuspensionsRequest) returns (DeleteUserSuspensionsResponse);

  // Get the information of a user related to billing.
  rpc GetUserBillingInfo(GetUserBillingInfoRequest) returns (GetUserBillingInfoResponse);

  // Update the information of a user related to billing.
  // TODO (Bin): remove this API once Stripe to Orb migration is done
  rpc UpdateUserBillingInfo(UpdateUserBillingInfoRequest) returns (UpdateUserBillingInfoResponse);

  // Parse through users and delete self-serve accounts for users that are on team / enterprise plans already
  rpc RemoveSelfServeAccountsForTeam(RemoveSelfServeAccountsForTeamRequest) returns (RemoveSelfServeAccountsForTeamResponse);

  // Remove all suspensions of the indicated types. Triggers background processing.
  rpc SuspensionCleanup(SuspensionCleanupRequest) returns (SuspensionCleanupResponse);

  // Parse through all users and remove extra self-serve accounts for users that are only in self-serve tenants.
  rpc RemoveExtraSelfServeTenantsFromUsers(RemoveExtraSelfServeTenantsFromUsersRequest) returns (RemoveExtraSelfServeTenantsFromUsersResponse);

  // Parse through all users and remove deleted tenants
  rpc RemoveDeletedTenantsFromUsers(RemoveDeletedTenantsFromUsersRequest) returns (RemoveDeletedTenantsFromUsersResponse);

  // Delete a single TenantSubscriptionMapping by tenant ID
  rpc DeleteTenantSubscriptionMapping(DeleteTenantSubscriptionMappingRequest) returns (DeleteTenantSubscriptionMappingResponse);

  // Update subscription owner from user to team
  rpc UpdateSubscriptionOwnerToTeam(UpdateSubscriptionOwnerToTeamRequest) returns (UpdateSubscriptionOwnerToTeamResponse);

  // Merge duplicate accounts by providing a list of user IDs to merge - consolidates duplicate users into a single account
  rpc MergeDuplicateAccounts(MergeDuplicateAccountsRequest) returns (MergeDuplicateAccountsResponse);

  // De-duplicate the user's tenant list - does NOT update any mappings
  rpc DeduplicateUserTenantList(DeduplicateUserTenantListRequest) returns (DeduplicateUserTenantListResponse);

  // Migrate populate IDP user mappings
  rpc MigratePopulateIDPUserMappings(MigratePopulateIDPUserMappingsRequest) returns (MigratePopulateIDPUserMappingsResponse);

  // Backfill cancellations based on failed invoices
  rpc BackfillCancellations(BackfillCancellationsRequest) returns (BackfillCancellationsResponse);

  // "Forget" this user from our system by deleting PII where possible and preventing export where
  // not possible.
  rpc ForgetUser(ForgetUserRequest) returns (ForgetUserResponse);

  // Get the subscription info for a user
  // This includes suspensions that apply to the user in the context of the tenant and payment status
  rpc GetUserSubscriptionInfo(GetUserSubscriptionInfoRequest) returns (GetUserSubscriptionInfoResponse);

  // This is used to clear pending tier changes, pending seat changes, and pending subscription changes.
  // Should only be called by support, after ensuring the user is otherwise in a good state (subscription + tenants are all good).
  // Can only be called by IAP users.
  rpc RemovePendingStatus(RemovePendingStatusRequest) returns (RemovePendingStatusResponse);

  // Add tags to a user
  rpc AddUserTags(AddUserTagsRequest) returns (AddUserTagsResponse);

  // Remove tags from a user
  rpc RemoveUserTags(RemoveUserTagsRequest) returns (RemoveUserTagsResponse);

  // Allow new trial for a list of users by clearing their subscription data and removing them from their tenant
  rpc AllowNewTrialForUsers(AllowNewTrialForUsersRequest) returns (AllowNewTrialForUsersResponse);

  // Backfill Netsuite IDs
  rpc BackfillNetsuiteInfo(BackfillNetsuiteInfoRequest) returns (BackfillNetsuiteInfoResponse);

  // Backfill Addresses
  rpc BackfillAddresses(BackfillAddressesRequest) returns (BackfillAddressesResponse);

  // Trigger Orb subscription event manually to fetching latest data from Orb and updating our records
  // This can be used to trigger the same subscription processing logic that normally happens via webhooks
  // Note: This is only accessible to IAP users. Not a user-facing API.
  rpc TriggerOrbSubscriptionEvent(TriggerOrbSubscriptionEventRequest) returns (TriggerOrbSubscriptionEventResponse);

  // Remove messages from the async ops dead letter queue
  rpc RemoveMessagesFromAsyncOpsDeadLetterQueue(RemoveMessagesFromAsyncOpsDeadLetterQueueRequest) returns (RemoveMessagesFromAsyncOpsDeadLetterQueueResponse);

  // Rebuild auth table indexes
  rpc RebuildIndexes(RebuildIndexesRequest) returns (RebuildIndexesResponse);

  // Create a long-lived API token for a user (for trigger executions)
  rpc CreateTokenForUser(CreateTokenForUserRequest) returns (CreateTokenForUserResponse) {
    option (auth_options.required_token_scopes) = AUTH_MINT;
  }

  // Set expiration on early user tokens
  rpc SetEarlyTokenExpiration(SetEarlyTokenExpirationRequest) returns (SetEarlyTokenExpirationResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }
}

// rebuild table indexes request
message RebuildIndexesRequest {}

// rebuild table indexes response
message RebuildIndexesResponse {}

message CreateTokenForUserRequest {
  string augment_user_id = 1;
  string tenant_id = 2;
  int64 expiration_time_seconds = 3; // must be > 0 and <= 90 days (in seconds)
  string trigger_id = 4; // optional, for audit purposes
  auth_entities.AgentType agent_type = 5; // type of agent requesting the token
}

message CreateTokenForUserResponse {
  string access_token = 1;
  int64 expires_in = 2; // seconds until expiration
}

message GetUserRequest {
  reserved 1;
  string user_id = 2;
  // This is an optional field because we also support
  // verification of the user's token via the user id in the token
  optional string tenant_id = 3;

  // If true, do not filter unenforced suspensions
  bool unfiltered_suspensions = 4;
}

message GetUserResponse {
  auth_entities.User user = 1;
}

message BatchGetUsersRequest {
  repeated GetUserRequest requests = 1;
}

message BatchGetUsersResponse {
  // Map from user_id to User object for efficient lookups (excludes GDPR deleted users)
  map<string, GetUserResponse> users = 1;
}

message GetUserBillingInfoRequest {
  string user_id = 1;
  string tenant_id = 2;
}

message GetUserBillingInfoResponse {
  string user_id = 1;
  string email = 2;

  // Whether this user is on a self-serve team
  bool is_self_serve_team = 3;

  // If the user is on a self-serve team, this is the Orb customer/subscription id of the team
  // Otherwise, this is the Orb customer id of the user
  string orb_customer_id = 4;
  string orb_subscription_id = 5;
  string stripe_customer_id = 6;

  // The user ids of the admins for the user's tenant if the user is in a self-serve team.
  // Otherwise empty list.
  repeated string team_admin_user_ids = 7;
}

message UpdateUserBillingInfoRequest {
  string user_id = 1;
  string tenant_id = 2;

  // The fields below will be updated only if they are sepecified.
  // optional auth_entities.BillingMethod billing_method = 3 [deprecated = true];
  reserved 3;

  optional string orb_customer_id = 4;
  optional string orb_subscription_id = 5;
  optional string stripe_customer_id = 6;
}

message UpdateUserBillingInfoResponse {}

message AddUserToTenantRequest {
  reserved 1;
  string email = 2;
  string tenant_id = 3;
}

message AddUserToTenantResponse {
  auth_entities.User user = 1;
}

// Updates information regarding user/tenant association.
message UpdateUserOnTenantRequest {
  // The tenant ID and user ID mapping (as compound key) to update.
  string tenant_id = 1;
  string user_id = 2;

  // List of rules to set (replace) on the user.
  repeated auth_entities.CustomerUiRole customer_ui_roles = 3;

  // Extra data for audit logging
  audit_log.ExtraAuditData extra_audit_data = 4;
}

// Returns the updated information on user-tenant association.
message UpdateUserOnTenantResponse {
  // The customer-ui roles on the user post update.
  repeated auth_entities.CustomerUiRole customer_ui_roles = 1;
}

// Use this message to get information
// on user/tenant association.
message GetUserOnTenantRequest {
  // The tenant ID and user ID mapping (as compound key) to retrieve.
  string tenant_id = 1;
  string user_id = 2;
}

// Returns information on user/tenant association.
message GetUserOnTenantResponse {
  // The customer-ui roles on the user.
  repeated auth_entities.CustomerUiRole customer_ui_roles = 1;
}

message RemoveUserFromTenantRequest {
  reserved 1;
  string user_id = 2;
  string tenant_id = 3;
}

// Dedicated empty type is gRPC best practice
message RemoveUserFromTenantResponse {}

message UpdateUserEmailRequest {
  string user_id = 1;
  // Current email, used for verification
  string current_email = 2;
  // New email to set
  string new_email = 3;
}

message UpdateUserEmailResponse {}

message ListTenantUsersRequest {
  reserved 1;
  string tenant_id = 2;
  // If true, filter out unenforced suspensions
  bool filter_suspensions = 3;
}

message UserTenantCustomerUiRoles {
  repeated auth_entities.CustomerUiRole customer_ui_roles = 1;
}

message ListTenantUsersResponse {
  repeated auth_entities.User users = 1;
  map<string, UserTenantCustomerUiRoles> user_id_to_customer_ui_roles = 2;
}

message GetTokenInfoRequest {
  // The token to return information
  string token = 1 [debug_redact = true];

  // A string identifying who is requesting the information.
  // Number of requestors should be low.
  string requestor = 2;
}
message GetTokenInfoResponse {
  // The user ID associated with the token.
  // NB: This is the deprecated notion of a "user id", which is either an email or an API token id.
  // We are migrating to using `augment_user_id` and `user_email` instead.
  string user_id = 1;

  // The tenant ID associated with the token.
  string tenant_id = 2;

  // The tenant name associated with the token.
  //
  // In single tenant namespaces, the tenant name will be the same as the
  // namespace.
  string tenant_name = 3;

  // The UUID from the auth database.
  string augment_user_id = 4;

  // The email of the user.
  string user_email = 5 [debug_redact = true];

  // Type of agent/token this represents. When CODE_REVIEW_BOT, the token is tenant-scoped; when USER, it is user-scoped.
  auth_entities.AgentType agent_type = 12;

  // Subscription information
  // Keep all of these in sync with subscription in services/auth/query/auth_query.proto

  oneof subscription {
    EnterpriseSubscription enterprise = 6;
    ActiveSubscription active_subscription = 7;
    Trial trial = 8;
    InactiveSubscription inactive_subscription = 9;
  }

  // Current active suspensions
  repeated auth_entities.UserSuspension suspensions = 10;

  // Feature gating information for backend-driven feature control
  auth_entities.FeatureGatingInfo feature_gating_info = 11;
}

// Enterprise subscription type
message EnterpriseSubscription {}

// Active subscription type - this is used for all self-serve plans
message ActiveSubscription {
  // The end date of the subscription - could be a trial end date when using Orb or a paid subscription end date
  google.protobuf.Timestamp end_date = 1;

  // Whether the user is out of credits
  bool usage_balance_depleted = 2;

  // The user's billing method
  // auth_entities.BillingMethod billing_method = 3 [deprecated = true];
  reserved 3;

  // Whether the subscription is a community subscription
  bool is_community = 4;

  // Whether the subscription is a trial subscription
  bool is_trial = 5;
}

// Trial subscription type
// This needs to be deprecated - no longer in use
message Trial {
  // The trial end date
  google.protobuf.Timestamp trial_end = 1;

  // The user's billing method
  // auth_entities.BillingMethod billing_method = 2 [deprecated = true];
  reserved 2;
}

// Inactive subscription type
message InactiveSubscription {
  // Add any inactive subscription-specific fields here

  // The user's billing method
  // auth_entities.BillingMethod billing_method = 1 [deprecated = true];
  reserved 1;
}

message RevokeUserCookiesRequest {
  string user_id = 1;
  string tenant_id = 2;
}

// Dedicated empty type is gRPC best practice
message RevokeUserCookiesResponse {}

message RevokeUserRequest {
  string email = 1 [debug_redact = true];
  string tenant_id = 2;
}

message RevokeUserResponse {
  int32 tokens_deleted = 1;
}

message RevokeUserByIdRequest {
  string user_id = 1;
  string tenant_id = 2;
}

message RevokeUserByIdResponse {
  int32 tokens_deleted = 1;
}

message RemoveSelfServeAccountsForTeamRequest {
  bool dry_run = 1;

  // If specified, only remove self-serve accounts for users in this tenant
  // If not specified, remove self-serve accounts for all users
  optional string tenant_id = 2;

  // If specified, don't process any users that are in any of these tenants
  repeated string tenant_ids_to_ignore = 3;
}

message RemoveSelfServeAccountsForTeamResponse {
  message TenantInfo {
    string tenant_id = 1;
    string tenant_name = 2;
  }
  message UserRemovals {
    string user_id = 1;
    repeated TenantInfo primary_tenants = 2;
    repeated TenantInfo removed_tenants = 3;
  }

  repeated UserRemovals removed_users = 1;
  repeated string failed_users = 2;
}

message RemoveExtraSelfServeTenantsFromUsersRequest {
  bool make_changes = 1;

  // If specified, only remove self-serve accounts for users in these namespaces
  // If not specified, remove self-serve accounts for all users
  repeated string namespace_ids = 2;
}

message RemoveExtraSelfServeTenantsFromUsersResponse {
  message TenantInfo {
    string tenant_id = 1;
    string tenant_name = 2;
  }
  message UserRemovals {
    string user_id = 1;
    TenantInfo primary_tenant = 2;
    repeated TenantInfo removed_tenants = 3;
  }

  repeated UserRemovals removed_users = 1;
  repeated string failed_users = 2;
}

message RemoveDeletedTenantsFromUsersRequest {
  bool make_changes = 1;
  // map from tenant ID to tenant name
  map<string, string> tenant_mappings_for_deleted_tenants = 2;
}

message RemoveDeletedTenantsFromUsersResponse {
  message TenantInfo {
    string tenant_id = 1;
    string tenant_name = 2;
  }
  message UserRemovals {
    string user_id = 1;
    repeated TenantInfo removed_tenants = 2;
  }

  repeated UserRemovals removed_users = 1;
  repeated string failed_users = 2;
}

message UserMerge {
  // The email that had duplicates (for email-based merges) or the primary user's email (for IDP-based merges)
  string email = 1 [debug_redact = true];
  // The user id to use going forward for this user - this should be the lowest user ID
  string primary_user_id = 2;
  // The user IDs that were merged and should no longer be used
  repeated string merged_user_ids = 3;
  // The user ID whose subscription information was copied to the primary user
  string subscription_source_user_id = 4;

  // Tenant information - maps of tenant_id -> tenant_name
  // The tenant that the user will be in going forward - this may have been copied from subscription_source_user_id
  map<string, string> primary_user_tenant = 5;
  // The tenants that were merged and should no longer be used
  map<string, string> merged_user_tenants = 6;

  // All emails that were merged (useful for IDP-based merges where users had different emails)
  repeated string merged_emails = 15 [debug_redact = true];

  // Subscription information
  // The customer ID that was preserved and associated with the "most active" subscription
  string preserved_orb_customer_id = 7;
  // The orb subscription ID that was preserved and is the "most active"
  string preserved_orb_subscription_id = 8;
  // The stripe customer ID that was preserved and associated with the "most active" subscription
  string preserved_stripe_customer_id = 9;
  // The orb subscription IDs that will no longer be used
  repeated string merged_user_subscription_ids = 10;
  // The orb subscription IDs that were previously active but were canceled
  repeated string canceled_subscription_ids = 11;

  // Merged IDP user IDs that should now all be associated with the primary user
  repeated string idp_user_ids = 12;
}

message MergeDuplicateAccountsRequest {
  // List of user IDs to merge - these should be users that have the same email or IDP user ID
  repeated string user_ids = 1;
  // Whether to run in dry-run mode (true) or actually make changes (false)
  bool dry_run = 2;
}

message MergeDuplicateAccountsResponse {
  // The merge operation that was performed or would be performed
  UserMerge user_merge = 1;
  // List of user IDs that failed to be processed
  repeated string failed_users = 2;
}

message DeleteTenantSubscriptionMappingRequest {
  // The tenant ID for which to delete the subscription mapping
  string tenant_id = 1;
}

message DeleteTenantSubscriptionMappingResponse {}

message UpdateSubscriptionOwnerToTeamRequest {
  // The subscription IDs to update
  repeated string subscription_ids = 1;
}

message UpdateSubscriptionOwnerToTeamResponse {
  message SubscriptionUpdateResult {
    string subscription_id = 1;
    bool success = 2;
    string error_message = 3;
  }
  repeated SubscriptionUpdateResult results = 1;
}

message DeduplicateUserTenantListRequest {
  bool make_changes = 1;
  string user_id = 2;
}

message DeduplicateUserTenantListResponse {
  repeated string previous_tenants = 1;
  string new_tenant = 2;
}

message UserCreditGrant {
  // The user ID to grant credits to
  string user_id = 1;
  // The number of user messages to grant as free credits
  int64 num_user_messages = 2;
}

message GrantFreeCreditsToUsersRequest {
  // List of users and credit amounts to grant
  repeated UserCreditGrant user_credits = 1;
  // Idempotency key for the request
  string idempotency_key = 2;
  // The reason for credit grant. Must be set for IAP users. This is audit logged.
  string reason = 3;
}

message CreditGrantResult {
  enum Status {
    STATUS_UNKNOWN = 0;
    STATUS_SUCCESS = 1;
    STATUS_ERROR = 2;
  }

  // The user ID
  string user_id = 1;
  // Status of the credit grant
  Status status = 2;
  // Error message if the grant failed
  string error_message = 3;
}

message GrantFreeCreditsToUsersResponse {
  // Results for each user credit grant attempt
  repeated CreditGrantResult results = 1;
}

message AllowNewTrialForUsersRequest {
  // List of user IDs to allow new trial for
  repeated string user_ids = 1;
}

message TrialAllowResult {
  enum Status {
    STATUS_UNKNOWN = 0;
    STATUS_SUCCESS = 1;
    STATUS_ERROR = 2;
  }

  // The user ID
  string user_id = 1;
  // Status of the trial allow operation
  Status status = 2;
  // Error message if the operation failed
  string error_message = 3;
}

message AllowNewTrialForUsersResponse {
  // Results for each user trial allow attempt
  repeated TrialAllowResult results = 1;
}

message UpdateAdminUserForTeamRequest {
  // The tenant ID of the self-serve team
  string tenant_id = 1;
  // The user ID of the new admin user
  string admin_user_id = 2;
}

message UpdateAdminUserForTeamResponse {}

message MigratePopulateIDPUserMappingsRequest {}

message MigratePopulateIDPUserMappingsResponse {}

message BackfillCancellationsRequest {
  bool make_changes = 1; // whether we make changes or not
  int32 max_invoices_to_process = 2; // max number of invoices to process
  string minimum_invoice_amount = 3; // the minimum amount of the invoices we search for
}

message BackfillCancellationsResponse {
  repeated string successful_invoice_ids = 1;
  repeated string failed_invoice_ids = 2;
  repeated string not_attempted_invoice_ids = 3;
  repeated string voided_invoice_ids = 4;
}

message RemovePendingStatusRequest {
  reserved 1, 2, 3;
  repeated string user_ids = 4;
  repeated string tenant_ids = 5;
  repeated string subscription_ids = 6;
}

message RemovePendingStatusResponse {
  repeated RemovePendingStatusResult results = 1;
}

message RemovePendingStatusResult {
  string user_id = 1;
  string tenant_id = 2;
  string subscription_id = 3;
  bool success = 4;
  string error_message = 5;
}

message GetUserSubscriptionInfoRequest {
  string user_id = 1;
  string tenant_id = 2;
}

message GetUserSubscriptionInfoResponse {
  oneof subscription {
    EnterpriseSubscription enterprise = 1;
    ActiveSubscription active_subscription = 2;
    Trial trial = 3;
    InactiveSubscription inactive_subscription = 4;
  }
  // These are suspensions that apply to the user in the context of the tenant and payment status
  // For example, if the user is suspended for API abuse, but the tenant is a self-serve team, the
  // API abuse suspension does not apply.
  // Only includes active suspensions
  repeated auth_entities.UserSuspension suspensions = 5;

  // Feature gating information for backend-driven feature control
  auth_entities.FeatureGatingInfo feature_gating_info = 6;
}

message BackfillNetsuiteInfoRequest {
  bool dry_run = 1;
  int32 max_users = 2;
}

message BackfillNetsuiteInfoResponse {
  repeated string failed_customer_ids = 1;
  repeated string successful_customer_ids = 2;
  repeated string unchanged_customer_ids = 3;
}

message BackfillAddressesRequest {
  bool dry_run = 1;
  int32 max_users = 2;
  // Optional. If set, filter search to customers created before this time.
  optional google.protobuf.Timestamp customer_created_before_time = 3;

  // Optional. If set, backfill only the following Orb customer IDs rather than all orb customers
  repeated string orb_customer_ids = 4;
}

message BackfillAddressesResponse {
  repeated string successful_customer_ids = 1;
  repeated string failed_customer_ids = 2;
  repeated string no_stripe_address_customer_ids = 3; // customers with no address in Stripe
  repeated string unchanged_customer_ids = 4; // customers whose address was already set in Orb
}

message RemoveMessagesFromAsyncOpsDeadLetterQueueRequest {
  repeated string message_ids = 1;
  int32 timeout_seconds = 2;
}

message RemoveMessagesFromAsyncOpsDeadLetterQueueResponse {}

message SetEarlyTokenExpirationRequest {
  string user_id = 1;
  int32 seconds_from_creation = 2;
}

message SetEarlyTokenExpirationResponse {
  int32 tokens_updated = 1;
}

// Separate team management service to help distinguish true auth operations from user-driven team
// management. This is purely organizational; AuthService and TeamManagementService share a database
// and are intended to run within the same server.
service TeamManagementService {
  //////////////// TEAM MANAGEMENT RPCs ////////////////////

  // Create a new tenant for a team, with the caller as the tenant's sole member and admin. This is
  // an async operation. Callers are expected to poll GetCreateTenantForTeamStatus to learn when the
  // operation has completed.
  // This is intended to be a temporary endpoint, until we're ready to create a tenant for every new
  // user.
  rpc CreateTenantForTeam(CreateTenantForTeamRequest) returns (CreateTenantForTeamResponse);
  rpc GetCreateTenantForTeamStatus(GetCreateTenantForTeamStatusRequest) returns (GetCreateTenantForTeamStatusResponse);

  // Invite users to a tenant. This will give users the option of joining the tenant (called a
  // "team" in the UI) when they log in. This will fail if any of the following are true:
  // - The caller is not an admin for the tenant.
  // - The tenant does not have sufficient seats available.
  rpc InviteUsersToTenant(InviteUsersToTenantRequest) returns (InviteUsersToTenantResponse);

  // Get a list of all the pending invitations for a tenant. This could be extended someday to allow
  // filtering by invitation status (so that we can show declined invitations as well).
  rpc GetTenantInvitations(GetTenantInvitationsRequest) returns (GetTenantInvitationsResponse);

  // Get a list of all the pending invitations for a user, by email. This is intended to be called
  // as part of the login flow.
  // Note(jacqueline): I suspect that this will eventually be deprecated in favor of emailed
  // invitation tokens.
  rpc GetUserInvitations(GetUserInvitationsRequest) returns (GetUserInvitationsResponse);

  // Accept or decline invitations. Note that this endpoint relies on the frontend's view of the
  // open invitations, and does not check for additional invitations on the backend. This is
  // intentional, to avoid a race where a user never sees an invitation sent between when the
  // frontend loads the open invitations and when they accept/decline them. Having additional
  // pending invitations is harmless; the user will see them the next time they log in.
  // This endpoint is async. Callers should use GetResolveInvitationsStatus to learn the outcome
  // of the operation.
  rpc ResolveInvitations(ResolveInvitationsRequest) returns (ResolveInvitationsResponse);
  rpc GetResolveInvitationsStatus(GetResolveInvitationsStatusRequest) returns (GetResolveInvitationsStatusResponse);

  // Delete an invitation. The intended usage is to delete a pending invitation (which will free
  // up a seat in the team's subscription).
  rpc DeleteInvitation(DeleteInvitationRequest) returns (DeleteInvitationResponse);

  // Delete account based on user role and team status.
  // - If individual user: cancel subscription, remove payment method, and remove from tenant
  // - If team user (non-admin): remove from tenant
  // - If team admin with >1 users in tenant: return error (cannot delete with active team members)
  // - If team admin with =1 user in tenant: cancel subscription, remove payment method, delete pending invitations, and remove from tenant
  rpc DeleteAccount(DeleteAccountRequest) returns (DeleteAccountResponse);

  //////////////// END TEAM MANAGEMENT RPCs ////////////////////

  //////////////// SUBSCRIPTION & BILLING RPCs ////////////////////

  // Get Orb subscription information (e.g., seat count).
  rpc GetSubscription(GetSubscriptionRequest) returns (GetSubscriptionResponse);

  // Update a subscription (currently just the number of seats). This will fail if any of the
  // following are true:
  // - The caller is not an admin for the tenant.
  // - The update would cause the subscription to have fewer seats than the current number of users + invitations in the tenant.
  // - The given subscription is associated with a user rather than a tenant. Only team subscriptions have seats.
  //
  // This operation is async. Callers should poll GetSubscription to learn when their change has
  // been persisted to Orb.
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (UpdateSubscriptionResponse);

  // Cancel Orb subscription at end of current billing period
  rpc CancelSubscription(CancelSubscriptionRequest) returns (CancelSubscriptionResponse);

  // UnschedulePendingSubscriptionCancellation reverts a pending cancellation for a subscription.
  // This is only allowed if the subscription has been scheduled to cancel at
  // the end of the billing period and is still active.
  // If the subscription is for a team, the caller must be an admin.
  //
  // If the subscription is already canceled or is not scheduled to be cancelled, will return an error.
  rpc UnschedulePendingSubscriptionCancellation(UnschedulePendingSubscriptionCancellationRequest) returns (UnschedulePendingSubscriptionCancellationResponse);

  ////////////// END SUBSCRIPTION & BILLING RPCs ////////////////////

  //////////////// PLAN MANAGEMENT RPCs ////////////////////

  // Get all Orb plans
  rpc GetAllOrbPlans(GetAllOrbPlansRequest) returns (GetAllOrbPlansResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }

  // Get the plan information of a user in Orb
  rpc GetUserOrbPlanInfo(GetUserOrbPlanInfoRequest) returns (GetUserOrbPlanInfoResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }

  // Get the status of a tenant's plan, specifically if a plan change is pending.
  rpc GetTenantPlanStatus(GetTenantPlanStatusRequest) returns (GetTenantPlanStatusResponse);

  // This is an operation that will coalesce the user to a given plan
  // The user is not required to be on a plan or have a subscription or be in the correct type of tenant
  //
  // This operation is conditionally asynchronous, if a tier change is required then it will be async, and
  // callers should poll on GetUser which will include the relevant pending state
  rpc PutUserOnPlan(PutUserOnPlanRequest) returns (PutUserOnPlanResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }

  // UnschedulePlanChanges reverts a pending plan change for a subscription.
  // This is only allowed if the subscription has a scheduled plan change.
  // If the subscription is for a team, the caller must be an admin.
  //
  // If the subscription has no scheduled plan change, will return an error.
  rpc UnschedulePlanChanges(UnschedulePlanChangesRequest) returns (UnschedulePlanChangesResponse);

  //////////////// END PLAN MANAGEMENT RPCs ////////////////////

  //////////////// CHECKOUT FLOW RPCs ///////////////////

  rpc GetAllPlanChangeDetails(GetAllPlanChangeDetailsRequest) returns (GetAllPlanChangeDetailsResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }
  rpc HandlePlanChange(HandlePlanChangeRequest) returns (HandlePlanChangeResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }
  rpc PutUserInPendingState(PutUserInPendingStateRequest) returns (PutUserInPendingStateResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }
  rpc GetPendingChange(GetPendingChangeRequest) returns (GetPendingChangeResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }

  // Called after a trial user enters their payment method
  rpc UpdatePaymentMethodForTrialUser(UpdatePaymentMethodForTrialUserRequest) returns (UpdatePaymentMethodForTrialUserResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }

  ////////// END CHECKOUT FLOW RPCs ////////////////////

  /////////// USER ORB INFO RPCs ////////////////////////

  // Get information about the user's credit status in Orb
  rpc GetUserOrbCreditsInfo(GetUserOrbCreditsInfoRequest) returns (GetUserOrbCreditsInfoResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }

  // Get information about the user's current payment methods and status
  rpc GetUserOrbPaymentInfo(GetUserOrbPaymentInfoRequest) returns (GetUserOrbPaymentInfoResponse);

  // Get information about the user's subscription from Orb
  rpc GetUserOrbSubscriptionInfo(GetUserOrbSubscriptionInfoRequest) returns (GetUserOrbSubscriptionInfoResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }

  ////////////// END USER ORB INFO RPCs //////////////////

  //////////////// CREDITS & PAYMENTS RPCs ////////////////////

  // Purchase additional Orb credits
  rpc PurchaseCredits(PurchaseCreditsRequest) returns (PurchaseCreditsResponse);

  // Evaluate if a user is eligible for a promotion and mark them as such
  rpc EvaluatePromotionEligibility(EvaluatePromotionEligibilityRequest) returns (EvaluatePromotionEligibilityResponse);

  // Process a promotion for a user, awarding any applicable credits and updating the user
  rpc ProcessPromotion(ProcessPromotionRequest) returns (ProcessPromotionResponse);

  // Grant free credits to a list of users
  // Note: This is only accessible to IAP users. Not a user-facing API.
  rpc GrantFreeCreditsToUsers(GrantFreeCreditsToUsersRequest) returns (GrantFreeCreditsToUsersResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }

  ////////////////// END CREDITS & PAYMENTS RPCs ///////////////////////

  /////////////////// TOP-UP RPCs ///////////////////////

  // Get current automatic credit top-up configuration for a user/team
  rpc GetTopUp(GetTopUpRequest) returns (GetTopUpResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }

  // Enable/disable automatic credit top-up for a user/team
  rpc SetTopUpEnabled(SetTopUpEnabledRequest) returns (SetTopUpEnabledResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }

  // Create or update automatic credit top-up configuration for a user/team
  rpc ConfigureTopUp(ConfigureTopUpRequest) returns (ConfigureTopUpResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }

  ////////////////// END TOP-UP RPCs ///////////////////////

  //////////////// ADMIN & INTERNAL RPCs ////////////////////

  // Update the admin user of a self-serve team to a new admin user.
  // Note: This is only accessible to IAP users. Not a user-facing API.
  rpc UpdateAdminUserForTeam(UpdateAdminUserForTeamRequest) returns (UpdateAdminUserForTeamResponse);

  // Backfill payment history information for subscriptions
  rpc BackfillPaymentHistory(BackfillPaymentHistoryRequest) returns (BackfillPaymentHistoryResponse) {
    option (auth_options.required_token_scopes) = AUTH_ADMIN;
  }

  ////////////// END ADMIN & INTERNAL RPCs ////////////////////

  ////////////// PAGINATED DATA EXPORT RPCs ////////////////////

  // List subscriptions in the system.
  rpc ListSubscriptions(ListSubscriptionsRequest) returns (ListSubscriptionsResponse);

  // List tenant subscription mappings in the system.
  rpc ListTenantSubscriptionMappings(ListTenantSubscriptionMappingsRequest) returns (ListTenantSubscriptionMappingsResponse);

  // List user tenant mappings in the system.
  rpc ListUserTenantMappings(ListUserTenantMappingsRequest) returns (ListUserTenantMappingsResponse);

  ////////////// END PAGINATED DATA EXPORT RPCs ////////////////////
}

message CreateTenantForTeamRequest {
  // The user ID of the user creating the team. It is an error if this does not match the user ID
  // in the auth token used to call this endpoint.
  string admin_user_id = 1;
}

message CreateTenantForTeamResponse {
  // Id that can be passed to GetCreateTenantForTeamStatus to poll for the status of the tenant
  // creation.
  string tenant_creation_id = 1;
}

message GetCreateTenantForTeamStatusRequest {
  // Should come from a previous call to CreateTenantForTeam.
  string tenant_creation_id = 1;
}

message GetCreateTenantForTeamStatusResponse {
  auth_entities.TenantCreation tenant_creation = 1;
}

message InviteUsersToTenantRequest {
  // The tenant to invite users to.
  string tenant_id = 1;

  // The emails of the users being invited.
  repeated string invitee_emails = 2 [debug_redact = true];
}

message InviteUsersToTenantResponse {
  // Wrapper around status information for a single invitation creation. Having this wrapper gives
  // us the option of displaying detailed information for each failure, if that's something we
  // decide we want to do in the future.
  message InvitationCreationStatus {
    enum Status {
      // Unused default.
      UNKNOWN = 0;

      // Invitation was successfully created.
      SUCCESS = 1;

      // Generic error status. Some reasons this could happen include:
      // - The invitee has an email domain associated with an enterprise tenant.
      ERROR = 2;
    }

    string email = 1 [debug_redact = true];
    Status status = 2;

    // Only populated if status is SUCCESS.
    string invitation_id = 3;
  }

  // For every email in the request, return the status of the creation.
  repeated InvitationCreationStatus invitation_statuses = 1;
}

message GetTenantInvitationsRequest {
  string tenant_id = 1;
}

message GetTenantInvitationsResponse {
  // Note that this has all invitations, including accepted and declined ones. We'll probably want
  // filtering at some point.
  repeated auth_entities.TenantInvitation invitations = 1;
}

message GetUserInvitationsRequest {
  // The email of the user to find invitations for. This email will be trimmed and lowercased for
  // lookup, but we do NOT normalize "+" or "." out of emails, to allow users to create multiple
  // accounts that way if they wish.
  string email = 1 [debug_redact = true];
}

message GetUserInvitationsResponse {
  // All the pending invitations for the user.
  repeated auth_entities.TenantInvitation invitations = 1;
}

message ResolveInvitationsRequest {
  // The invitation id to accept. Can be left unset if the user doesn't want to accept anything.
  optional string accept_invitation_id = 1;

  // The invitation ids to decline.
  repeated string decline_invitation_ids = 2;

  // The IDP user ID of the user resolving the invitations. This is used when creating
  // new users through invitation acceptance to properly link them to their identity provider.
  string idp_user_id = 3;
}

// Empty response on success.
message ResolveInvitationsResponse {
  // Id that can be passed to GetResolveInvitationsStatus to poll for the status of the invitation
  // resolution.
  string invitation_resolution_id = 1;
}

message GetResolveInvitationsStatusRequest {
  // Should come from a previous call to ResolveInvitations.
  string invitation_resolution_id = 1;
}

message GetResolveInvitationsStatusResponse {
  auth_entities.InvitationResolution invitation_resolution = 1;
}

message DeleteInvitationRequest {
  string tenant_id = 1;
  string invitation_id = 2;
}

message DeleteInvitationResponse {}

message GetSubscriptionRequest {
  oneof lookup_id {
    // Look up the subscription by subscription ID. An error will be returned if the caller doesn't
    // have access to this subscription.
    string subscription_id = 1;

    // Look up the subscription by tenant ID. An error will be returned if this doesn't match the
    // tenant ID in the caller's auth token.
    string tenant_id = 2;
  }
}

message GetSubscriptionResponse {
  auth_entities.Subscription subscription = 1;
}

message UpdateSubscriptionRequest {
  // The id of the subscription to update. The caller's auth token must match the tenant ID for this
  // subscription.
  string subscription_id = 1;

  // The number of seats to update the subscription to.
  int32 seats = 2;

  // Reason for the subscription update. Must be set for IAP users. This is audit logged.
  string reason = 3;
}

// Empty response on success.
message UpdateSubscriptionResponse {}

message CreateUserSuspensionRequest {
  string user_id = 1;
  string tenant_id = 2;
  auth_entities.UserSuspensionType suspension_type = 3;
  // Detailed message regarding why this uses needs to be suspended, for audit log.
  string evidence = 4 [debug_redact = true];
}

message CreateUserSuspensionResponse {
  string suspension_id = 1;
  int32 tokens_deleted = 2;
  repeated string cancelled_subscription_ids = 3;
}

message DeleteUserSuspensionsRequest {
  string user_id = 1;
  string tenant_id = 2;
  repeated string suspension_ids = 3;
}

message DeleteUserSuspensionsResponse {
  int32 suspensions_deleted = 1;
}

message UpdateSuspensionExemptionRequest {
  string user_id = 1;
  string tenant_id = 2;
  // When true, prevents new suspensions from being applied.
  bool exempt = 3;
  // The reason for the exemption request. This is audit logged.
  string reason = 4;
}

message UpdateSuspensionExemptionResponse {
  string user_id = 1;
  string tenant_id = 2;
  bool exempt = 3;
}

message PurchaseCreditsRequest {
  // The requester's Augment user ID and tenant ID
  string user_id = 1;
  string tenant_id = 2;

  // Number of usage units to purchase. Must be between the minimum and maximum of plan's add_credits_options, inclusive
  float credits = 3;

  // Optional idempotency key. Can represent a checkout session or be used to make sure
  // that the same purchase is not made twice.
  optional string idempotency_key = 4;
}

message PurchaseCreditsResponse {}

enum CancellationReason {
  CANCELLATION_REASON_UNSPECIFIED = 0;
  CANCELLATION_REASON_TOO_EXPENSIVE = 1;
  CANCELLATION_REASON_NOT_USING_ENOUGH = 2;
  CANCELLATION_REASON_MISSING_FEATURES = 3;
  CANCELLATION_REASON_TECHNICAL_ISSUES = 4;
  CANCELLATION_REASON_SWITCHING_TO_COMPETITOR = 5;
  // CLI/API solutions grouped together
  CANCELLATION_REASON_SWITCHING_TO_CLAUDE_CODE = 6;
  CANCELLATION_REASON_SWITCHING_TO_CODEX = 7;
  CANCELLATION_REASON_SWITCHING_TO_GEMINI_CLI = 8;
  // IDE solutions grouped together
  CANCELLATION_REASON_SWITCHING_TO_CURSOR = 9;
  CANCELLATION_REASON_SWITCHING_TO_WINDSURF = 10;
  CANCELLATION_REASON_SWITCHING_TO_COPILOT = 11;
  CANCELLATION_REASON_SWITCHING_TO_CLINE = 12;
  // Other solutions
  CANCELLATION_REASON_SWITCHING_TO_DEVIN = 13;
  CANCELLATION_REASON_SWITCHING_TO_ROO = 14;
  CANCELLATION_REASON_SWITCHING_TO_LOVABLE = 15;
  CANCELLATION_REASON_SWITCHING_TO_FACTORY = 16;
  CANCELLATION_REASON_SWITCHING_TO_OTHER_TOOL = 17;
  CANCELLATION_REASON_NO_LONGER_NEEDED = 18;
  CANCELLATION_REASON_OTHER = 19;
}

message CancelSubscriptionRequest {
  message Feedback {
    // Optional cancellation feedback
    // explicitly not restricted information. This information is explicitly entered by the user as feedback
    CancellationReason cancellation_reason = 1;

    // free-form feedback.
    // explicitly not restricted information. This information is explicitly entered by the user as feedback
    optional string additional_feedback = 2;
  }

  string user_id = 1;
  string tenant_id = 2;
  bool cancel_immediately = 3;

  // Optional cancellation feedback
  Feedback feedback = 4;

  // Reason for cancelling -- set by IAP users (non-customers) only. We expect customers to use Feedback from the churn survey.
  string reason = 5;
}

message CancelSubscriptionResponse {}

message DeleteAccountRequest {
  string user_id = 1;
  string tenant_id = 2;
}
message DeleteAccountResponse {
  // Type of account deletion that was performed
  AccountDeletionType deletion_type = 1;
}

enum AccountDeletionType {
  ACCOUNT_DELETION_TYPE_UNKNOWN = 0;
  ACCOUNT_DELETION_TYPE_ENTERPRISE = 1;
  ACCOUNT_DELETION_TYPE_INDIVIDUAL = 2;
  ACCOUNT_DELETION_TYPE_TEAM_ADMIN = 3;
  ACCOUNT_DELETION_TYPE_TEAM_MEMBER = 4;
}

message ListSubscriptionsRequest {
  // Maximum number of subscriptions to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 1;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 2;
}

message ListSubscriptionsResponse {
  // The list of subscriptions for the current page
  repeated auth_entities.Subscription subscriptions = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message ListTenantSubscriptionMappingsRequest {
  // Maximum number of tenant subscription mappings to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 1;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 2;
}

message ListTenantSubscriptionMappingsResponse {
  // The list of tenant subscription mappings for the current page
  repeated auth_entities.TenantSubscriptionMapping tenant_subscription_mappings = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message ListUserTenantMappingsRequest {
  // Maximum number of user tenant mappings to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 1;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 2;
}

message ListUserTenantMappingsResponse {
  // The list of user tenant mappings for the current page
  repeated auth_entities.UserTenantMapping user_tenant_mappings = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message FailedPayment {
  string amount = 1;
  string date = 2;
  string invoice_url = 3;
}

message GetUserOrbPlanInfoRequest {
  string user_id = 1; //the user's Augment user ID
}

// UI-specific display information for Orb plans
// This message contains fields that are used by the frontend for display purposes
message OrbPlanDisplayInfo {
  string color = 1;
  string icon = 2;
  int32 sort_order = 3;
  string usage_unit_display_name = 4;
  repeated string plan_facts = 5;
}

message OrbPlanInfo {
  string external_plan_id = 1;
  string formatted_plan_name = 2;
  string price_per_seat = 3;
  string additional_usage_unit_cost = 4;
  bool add_usage_available = 5;
  float usage_units_per_seat = 6;
  bool training_allowed = 7;
  bool teams_allowed = 8;
  int32 max_num_seats = 9;
  string usage_unit_name = 10 [deprecated = true]; // Deprecated: Use display_info.usage_unit_display_name instead
  enum PlanType {
    PLAN_TYPE_UNKNOWN = 0;
    PLAN_TYPE_COMMUNITY = 1;
    PLAN_TYPE_TRIAL = 2;
    PLAN_TYPE_PAID = 3;
    PLAN_TYPE_FREE = 4;
  }
  PlanType plan_type = 11;
  // UI-specific display information
  OrbPlanDisplayInfo display_info = 12;
  // Allowed purchase options for additional usage units (e.g., [100, 250, 500, 1000])
  repeated int32 usage_unit_purchase_options = 13;
  bool auto_top_up_available = 14;
}

// Subscription is Pending Creation for the user, so the plan is pending
message PendingOrbPlan {}

message GetUserOrbPlanInfoResponse {
  oneof plan_info {
    OrbPlanInfo orb_plan_info = 1;
    PendingOrbPlan pending_plan = 2;
  }
}

message GetAllOrbPlansRequest {
  string user_id = 1;
}

message GetAllOrbPlansResponse {
  repeated OrbPlanInfo orb_plans = 1;
}

message UnschedulePendingSubscriptionCancellationRequest {
  string user_id = 1; //augment user ID
}

message UnschedulePendingSubscriptionCancellationResponse {}

message UnschedulePlanChangesRequest {
  string user_id = 1; //augment user ID
}

message UnschedulePlanChangesResponse {}

message PutUserOnPlanRequest {
  // The user ID to put on a plan
  string user_id = 1;

  // TODO: deprecate the plan enum when plan_id is used
  enum Plan {
    UNKNOWN_PLAN = 0;
    COMMUNITY = 1;
    DEVELOPER = 2;
  }
  // The plan to put the user on
  Plan plan = 2;

  // The plan ID in Orb to associate with the user
  string plan_id = 3;

  // The reason to put user on plan -- must be set when IAP user
  string reason = 4;
}

// Empty response on success as this is an async operation
message PutUserOnPlanResponse {}

message GetUsersRequest {
  // String to search for in the user's record. If blank, matches all users
  string search_string = 1 [debug_redact = true];

  // Maximum number of users to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 2;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 3;

  // If true, the request's claims must have PII_ADMIN scope.
  bool include_gdpr_ccpa_deleted = 4;

  // If true, filter out unenforced suspensions
  bool filter_suspensions = 5;
}

message GetUsersResponse {
  // The users that match the search criteria
  repeated auth_entities.User users = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message MigrateUserBlocksToSuspensionsRequest {}

message MigrateUserBlocksToSuspensionsResponse {
  int32 users_migrated = 1;
  int32 users_failed = 2;
}

message GetUserOrbCreditsInfoRequest {
  string user_id = 1;
}

message GetUserOrbCreditsInfoResponse {
  int32 usage_units_available = 1;
  int32 usage_units_used_this_billing_cycle = 2;
  int32 usage_units_pending = 3;

  // float fields to replace old int32 fields while maintaining push safety - eventually deprecate the above
  float usage_units_remaining = 4;
  float usage_units_consumed_this_billing_cycle = 5;
}

message GetUserOrbPaymentInfoRequest {
  string user_id = 1;
}

message GetUserOrbPaymentInfoResponse {
  bool has_payment_method = 1; //whether the user has a payment method saved
  optional FailedPayment failed_payment = 2; //can be null. change to list??
  bool needs_trial_payment_method = 3; // whether we are waiting on trial payment method for user
}

message GetUserOrbSubscriptionInfoRequest {
  string user_id = 1;
}

message OrbSubscriptionInfo {
  string orb_customer_id = 1;
  string orb_subscription_id = 2;
  string portal_url = 3; // orb portal URL for showing additional invoices, etc
  string external_plan_id = 4;
  enum SubscriptionStatus {
    UNKNOWN = 0;
    UPCOMING = 1;
    ACTIVE = 2;
    ENDED = 3;
  }
  SubscriptionStatus subscription_status = 5;

  int32 usage_units_renewing_each_billing_cycle = 6; // usage units renewing each billing cycle. 0 if scheduled to end at billing cycle end
  int32 usage_units_included_this_billing_cycle = 7; // number of usage units on the current plan
  int32 seats = 8;

  string monthly_total_cost = 9; // how much they are paying currently
  string next_billing_cycle_amount = 10; // how much they will be charged at the next billing cycle

  string billing_period_end_date_iso = 11;
  optional string trial_period_end_date_iso = 12; // null if not on trial
  optional string subscription_end_date_iso = 13; // null if no end date
  bool subscription_end_at_billing_cycle_end = 14; //true if subscription will end at current billing cycle end

  optional string scheduled_target_plan_id = 15; // null if no scheduled plan change
  bool cancelled_due_to_payment_failure = 16; // true if subscription was cancelled due to payment failure
}

message PendingOrbSubscription {}

message NoSubscription {}

message GetUserOrbSubscriptionInfoResponse {
  oneof orb_subscription_info {
    OrbSubscriptionInfo subscription = 1;
    PendingOrbSubscription pending_subscription = 2;
    NoSubscription nonexistent_subscription = 3;
  }
}

message SuspensionCleanupRequest {
  // Remove all suspensions of the indicated types
  repeated auth_entities.UserSuspensionType remove_suspension_types = 1;
  // Dedup suspensions of the indicated types, keeping only the oldest suspension
  repeated auth_entities.UserSuspensionType dedup_suspension_types = 2;
  // Regex to match against evidence. If provided, only suspensions with matching evidence will be removed/deduped.
  string evidence_regex = 3;
}

message SuspensionCleanupResponse {
  // Check logs for results. Job may run longer than RPC timeout.
  int32 suspensions_removed = 1;
  int32 suspensions_deduped = 2;
  int32 updates_failed = 3;
}

message GetTenantPlanStatusRequest {
  string tenant_id = 1;
}

message GetTenantPlanStatusResponse {
  bool is_pending = 1;
  // The target Orb plan ID if a change is pending
  optional string pending_target_plan_id = 2;
  // Timestamp of when the plan change was initiated, if pending
  optional google.protobuf.Timestamp plan_change_created_at = 3;
}

message ForgetUserRequest {
  // Augment user id (uuid), NOT an email.
  string user_id = 1;
}

message ForgetUserResponse {}

message EvaluatePromotionEligibilityRequest {
  string user_id = 1;
  string promotion_name = 2;
}

message EvaluatePromotionEligibilityResponse {
  auth_entities.PromotionStatus promotion_status = 1;
}

message ProcessPromotionRequest {
  string user_id = 1;
  string promotion_name = 2;
  // Whether the file validation passed. All promotions currently require file validation.
  // Currently, this validation happens in the customer-ui, but eventually, we will move it to auth-central.
  bool file_validation_passed = 3;
  // reCAPTCHA token for security verification
  string recaptcha_token = 4;
}

message ProcessPromotionResponse {
  bool success = 1;
}

message AddUserTagsRequest {
  string user_id = 1;
  repeated string tags = 2;
}

message AddUserTagsResponse {
  // Empty response indicates success
}

message RemoveUserTagsRequest {
  string user_id = 1;
  repeated string tags = 2;
}

message RemoveUserTagsResponse {
  // Empty response indicates success
}

message SubscriptionEventPair {
  // The Orb subscription ID to process
  string subscription_id = 1;
  // The Orb customer ID associated with this subscription
  string orb_customer_id = 2;
}

message TriggerOrbSubscriptionEventRequest {
  reserved 1, 2;

  // List of subscription and customer ID pairs to process
  repeated SubscriptionEventPair subscription_pairs = 3;
}

message TriggerOrbSubscriptionEventResult {
  enum Status {
    STATUS_UNKNOWN = 0;
    STATUS_SUCCESS = 1;
    STATUS_ERROR = 2;
  }

  // The subscription ID that was processed
  string subscription_id = 1;
  // The customer ID that was processed
  string orb_customer_id = 2;
  // Status of the subscription event processing
  Status status = 3;
  // Error message if processing failed
  string error_message = 4;
  // The updated subscription object after processing (only populated on success)
  auth_entities.Subscription subscription = 5;
}

message TriggerOrbSubscriptionEventResponse {
  // Reserved field for backward compatibility
  reserved 1;

  // Results for each subscription processed
  repeated TriggerOrbSubscriptionEventResult results = 2;
  // Total number of subscriptions processed
  int32 total_processed = 3;
  // Number of subscriptions processed successfully
  int32 successful_count = 4;
  // Number of subscriptions that had errors
  int32 error_count = 5;
}

message BackfillPaymentHistoryRequest {
  bool dry_run = 1;
  int32 limit = 2;
  // Optional list of specific subscription IDs to process
  // If not provided, will process all active subscriptions up to the limit
  repeated string subscription_ids = 3;
}

message BackfillPaymentHistoryResponse {
  // Subscription IDs that were successfully updated
  repeated string successful_subscription_ids = 1;

  // Subscription IDs that failed to update
  repeated string failed_subscription_ids = 2;

  // Subscription IDs that were skipped (e.g., already had payment history)
  repeated string skipped_subscription_ids = 3;

  // Total number of subscriptions processed
  int32 total_processed = 4;
}

message GetAllPlanChangeDetailsRequest {
  string user_id = 1;
  string success_url = 2;
  string cancel_url = 3;
}

enum PlanChangeType {
  UNKNOWN = 0;
  IMMEDIATE = 1;
  END_OF_BILLING_PERIOD = 2;
}

// 1. NEW SUBSCRIPTION (Professional Plan, 1 seat):
//    proration: null (no proration for new subscriptions)
//    next_billing_period: { start_date: "2024-02-01T00:00:00Z", amount_due: "20.00", credits: "1000.00" }
//    confirmation_message: "By switching to Professional, I fully surrender to Augment for the duration
//                          of my professional career until one day when I give my job over to Augment, happily & willingly."
//    checkout_session_url: "https://buy.stripe.com/..."
//
// 2. TRIAL → PROFESSIONAL (1 seat):
//    proration: null (no proration, billing cycle resets)
//    next_billing_period: { start_date: "2024-02-01T00:00:00Z", amount_due: "20.00", credits: "1000.00" }
//    confirmation_message: "...change will take effect immediately, and I will lose any unused messages..."
//    checkout_session_url: "https://buy.stripe.com/..."
//
// 3. PROFESSIONAL → MAX UPGRADE (1 seat, mid-month):
//    proration: {
//      prorated_amount_due: "200.00",        // Cost of Max plan for remaining 15 days
//      credited_amount: "50.00",             // Refund for unused Pro plan (15 days)
//      total_amount_due: "150.00",           // Net charge today (200 - 50)
//      prorated_credits: "2750.00",          // Max plan credits for remaining 15 days
//      credits_left_in_month_for_current_plan: "500.00", // Pro credits for remaining 15 days (lost)
//      total_new_credits: "2250.00"          // Net credits gained (2750 - 500)
//    }
//    next_billing_period: { start_date: "2024-02-01T00:00:00Z", amount_due: "300.00", credits: "4500.00" }
//    confirmation_message: "...change will take effect immediately, and I will be charged for the prorated difference..."
//    checkout_session_url: "https://buy.stripe.com/..."
//
// 4. MAX → PROFESSIONAL DOWNGRADE (1 seat):
//    proration: null (no immediate charge)
//    next_billing_period: { start_date: "2024-02-01T00:00:00Z", amount_due: "20.00", credits: "1000.00" }
//    confirmation_message: "...downgrade will take effect at the end of my billing period..."
//    checkout_session_url: null (no payment required)
//
// 5. PROFESSIONAL → COMMUNITY (1 seat):
//    proration: null (billing cycle resets)
//    next_billing_period: { start_date: "2024-02-01T00:00:00Z", amount_due: "0.00", credits: "50.00" }
//    confirmation_message: "...change will take effect immediately, and I will lose any unused messages..."
//    checkout_session_url: null (no payment required)
//
// 6. SAME PLAN (no change needed):
//    response: null (empty response, no fields populated)
message GetAllPlanChangeDetailsResponse {
  message BillingPeriod {
    // Start date of the next billing period in ISO 8601 format (e.g., "2024-02-01T00:00:00Z")
    // Always provided to show when the next billing cycle begins
    string start_date_iso = 1;
    // Cost for this billing period (e.g., "20.00", "300.00", "0.00")
    string amount_due = 2;
    // Credits/messages for this billing period (e.g., "1000.00", "4500.00", "50.00")
    string credits = 3;
  }

  message Proration {
    // Amount due immediately for pro-rated upgrade (cost of new plan for remaining billing period)
    string prorated_amount_due = 1;
    // Credited amount back for the current subscription (refund for unused portion of current plan)
    string credited_amount = 2;
    // Total net amount due today (prorated_amount_due - credited_amount)
    string total_amount_due = 3;
    // Pro-rated credits for the new plan for the remainder of the month
    string prorated_credits = 4;
    // Credits that would have been available for the current plan for the remainder of the month (subtracted/lost)
    string credits_left_in_month_for_current_plan = 5;
    // Net credits gained this month (prorated_credits - credits_left_in_month_for_current_plan)
    string total_new_credits = 6;
  }

  message PlanChangeDetails {
    string plan_id = 1;

    // Present only for paid-to-paid upgrades with immediate billing
    // Contains pro-rated amounts for the current billing period
    optional Proration proration = 2;

    // Billing information for the next billing cycle
    // Shows full plan pricing and credits
    BillingPeriod next_billing_period = 3;

    // User-facing confirmation message including AI training consent and timing details
    // Varies based on plan features and billing behavior
    string confirmation_message = 4;

    // Checkout session URL if payment is required for this plan change
    optional string checkout_session_url = 5;

    // Type of plan change (immediate or end of billing period)
    PlanChangeType plan_change_type = 6;
  }

  // List of plan change details for all available plans
  repeated PlanChangeDetails plan_details = 1;
}

message HandlePlanChangeRequest {
  string user_id = 1;
  string target_plan_id = 2;
}

message HandlePlanChangeResponse {
  PlanChangeType plan_change_type = 1;
}

message PutUserInPendingStateRequest {
  string user_id = 1;
}

message PutUserInPendingStateResponse {
  // Empty response indicates success
}

message GetPendingChangeRequest {
  string user_id = 1;
}

message GetPendingChangeResponse {
  // The pending change for the user or team (if any such change exists)
  optional auth_entities.PendingChange pending_change = 1;
}

message UpdatePaymentMethodForTrialUserRequest {
  string user_id = 1;
  string setup_intent_id = 2;
}

message UpdatePaymentMethodForTrialUserResponse {
  // Empty response indicates success
}

message GetTopUpRequest {
  string user_id = 1;
}

message GetTopUpResponse {
  bool enabled = 1;
  int64 threshold_default = 2;
  int64 amount_default = 3;
  int64 threshold = 4;
  int64 amount = 5;
  double cost_per_credit = 6;
}

message SetTopUpEnabledRequest {
  string user_id = 1;
  bool enabled = 2;
}

message SetTopUpEnabledResponse {}

message ConfigureTopUpRequest {
  string user_id = 1;
  int64 threshold = 2;
  int64 amount = 3;
}

message ConfigureTopUpResponse {}
