package ri_central_restricted

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"text/template"
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/augmentcode/augment/base/go/clock"
	"github.com/augmentcode/augment/base/logging/audit"
	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	gcsproxyproto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/central/proto"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"golang.org/x/sync/errgroup"
	"google.golang.org/api/iterator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	defaultMaxResults    = 100
	requestMetadataTable = "request_metadata"
	resolutionTable      = "resolution"
	modelTable           = "model"
	httpStatusTable      = "http_status"
	remoteAgentLogsTable = "remote_agent_log"
)

type RandomStringGenerator func() string

var DefaultRandomStringGenerator = func() string {
	return strings.ReplaceAll(uuid.NewString(), "-", "")
}

type RestrictedServer struct {
	pb.UnimplementedRequestInsightCentralRestrictedServer

	bqClient                 *bigquery.Client
	datasetName              string
	nonenterpriseDatasetName string
	gcsProxyClient           gcsproxyclient.GcsProxyClient
	auditLogger              *audit.AuditLogger
	clock                    clock.Clock

	// Allow overriding random string generation for easier testing.
	randomStringGen RandomStringGenerator
}

func New(
	ctx context.Context,
	projectId string,
	datasetName string,
	nonenterpriseDatasetName string,
	gcsProxyClient gcsproxyclient.GcsProxyClient,
) (*RestrictedServer, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !checkDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}
	if !checkDatasetName(nonenterpriseDatasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", nonenterpriseDatasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, err
	}

	s := RestrictedServer{
		bqClient:                 bqClient,
		datasetName:              datasetName,
		nonenterpriseDatasetName: nonenterpriseDatasetName,
		gcsProxyClient:           gcsProxyClient,
		auditLogger:              audit.NewDefaultAuditLogger(),
		clock:                    clock.NewRealClock(),
		randomStringGen:          DefaultRandomStringGenerator,
	}
	return &s, nil
}

// Check that the provided dataset name contains only letters and underscores. Returns true iff the
// provided name is valid.
func checkDatasetName(name string) bool {
	return regexp.MustCompile(`^[a-zA-Z_]+$`).MatchString(name)
}

func (s RestrictedServer) Close() error {
	var errs []error
	if err := s.bqClient.Close(); err != nil {
		errs = append(errs, fmt.Errorf("bigquery client close: %w", err))
	}
	if err := s.gcsProxyClient.Close(); err != nil {
		errs = append(errs, fmt.Errorf("gcs proxy client close: %w", err))
	}
	if len(errs) == 0 {
		return nil
	}
	// Combine all errors into a single error message
	return fmt.Errorf("failed to close clients: %v", errs)
}

// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to request data for the provided tenant id, or the valid
// claims from the context.
func (s RestrictedServer) checkAuthClaims(
	ctx context.Context, tenantID string, requiredScope tokenscopesproto.Scope,
) (*auth.AugmentClaims, error) {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		// This shouldn't happen, since we have an auth interceptor that checks these claims before
		// calling our application logic.
		log.Error().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	if !authClaims.IsTenantAllowed(tenantID) {
		log.Error().Msgf(
			"Auth claims doesn't allow access to tenant %s",
			tenantID,
		)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Error().Msgf("Auth claims do not give have scope %s", requiredScope)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	return authClaims, nil
}

func (s RestrictedServer) RestrictedSearch(
	ctx context.Context, req *pb.RestrictedSearchRequest,
) (*pb.RestrictedSearchResponse, error) {
	// Check that the caller provided exactly one tenant_id filter and that their auth claims give
	// read access to request data for that tenant.
	tenantID := req.GetTenantId()
	if tenantID == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	authClaims, err := s.checkAuthClaims(ctx, tenantID, tokenscopesproto.Scope_REQUEST_RESTRICTED_R)
	if err != nil {
		return nil, err
	}

	// Write audit log before doing anything.
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("Restricted search for requests from tenant %s", tenantID),
		requestContext,
		audit.NewTenantID(tenantID),
		audit.NewProtoRequest(req),
	)

	// Check for required arguments.
	if req.TimeFilter == nil || req.TimeFilter.StartTime == nil {
		return nil, status.Error(codes.InvalidArgument, "time_filter with start_time is required")
	}

	// Construct the query dynamically. `whereConditions` is used to construct the WHERE clause and
	// `joinTemplateConditions` is used to construct the JOIN clause for each table being joined, with
	// templating syntax.
	whereConditions := []string{
		fmt.Sprintf("%s.tenant_id = @TENANT_ID", requestMetadataTable),
	}
	joinTemplateConditions := []string{
		"{{.}}.tenant_id = @TENANT_ID",
	}
	parameters := []bigquery.QueryParameter{
		{Name: "TENANT_ID", Value: tenantID},
	}

	// We checked above that the caller provided a start time.
	whereConditions = append(whereConditions, fmt.Sprintf("%s.time >= @START_TIME", requestMetadataTable))
	joinTemplateConditions = append(joinTemplateConditions, "{{.}}.time >= @START_TIME")
	parameters = append(parameters, bigquery.QueryParameter{Name: "START_TIME", Value: req.TimeFilter.StartTime.AsTime()})

	// End time is optional. If not specified we'll fetch everything up to the current time.
	if req.TimeFilter.EndTime != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("%s.time <= @END_TIME", requestMetadataTable))
		joinTemplateConditions = append(joinTemplateConditions, "{{.}}.time <= @END_TIME")
		parameters = append(parameters, bigquery.QueryParameter{Name: "END_TIME", Value: req.TimeFilter.EndTime.AsTime()})
	}

	maxResults := req.GetMaxResults()
	if maxResults == 0 {
		maxResults = defaultMaxResults
	}
	parameters = append(parameters, bigquery.QueryParameter{Name: "MAX_RESULTS", Value: maxResults})

	filterWhereCondition, filterParams, err := s.resolveFilterGroup(req.GetFilters())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "Failed to resolve filter group: %w", err)
	}
	if filterWhereCondition != "" {
		whereConditions = append(whereConditions, filterWhereCondition)
		parameters = append(parameters, filterParams...)
	}

	// The main table being queried is request_metadata. Everything else is LEFT JOINed, so that we
	// return results iff there's a request_metadata row.
	queryTemplate := fmt.Sprintf(`
		SELECT
			{{.requestMetadataTable}}.request_id AS requestId,
			{{.requestMetadataTable}}.session_id AS sessionId,
			{{.requestMetadataTable}}.user_id AS userId,
			{{.requestMetadataTable}}.tenant_id AS tenantID,
			{{.requestMetadataTable}}.tenant AS tenantName,
			{{.requestMetadataTable}}.time AS requestTime,
			{{.requestMetadataTable}}.request_type AS requestType,
			{{.modelTable}}.model_name AS modelName,
			{{.resolutionTable}}.accepted AS accepted,
			{{.httpStatusTable}}.code AS httpStatusCode
		FROM {{.datasetName}}.{{.requestMetadataTable}}
		{{range .joinTables}}
		LEFT JOIN {{$.datasetName}}.{{.}}
			ON {{$.requestMetadataTable}}.request_id = {{.}}.request_id
			AND %s
		{{end}}
		WHERE {{.whereClause}}
		ORDER BY requestTime DESC
		LIMIT @MAX_RESULTS
	`, strings.Join(joinTemplateConditions, " AND "))
	t := template.New("query")
	t, err = t.Parse(queryTemplate)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to parse query template")
		return nil, status.Errorf(codes.Internal, "Failed to parse query template")
	}
	var queryBuf bytes.Buffer
	err = t.Execute(
		&queryBuf,
		map[string]interface{}{
			"datasetName":          s.datasetName,
			"requestMetadataTable": requestMetadataTable,
			"modelTable":           modelTable,
			"resolutionTable":      resolutionTable,
			"httpStatusTable":      httpStatusTable,
			"whereClause":          strings.Join(whereConditions, " AND "),
			"joinTables":           []string{modelTable, resolutionTable, httpStatusTable},
		},
	)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to execute query template")
		return nil, status.Error(codes.Internal, "Failed to execute query template")
	}

	// Run the query.
	query := s.bqClient.Query(queryBuf.String())
	query.Parameters = parameters
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Err(err).Msgf("Query error")
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	type bqResult struct {
		RequestId      string
		SessionId      string
		UserId         string
		TenantID       string
		TenantName     string
		RequestTime    time.Time
		RequestType    string
		ModelName      bigquery.NullString
		Accepted       bigquery.NullBool
		HttpStatusCode bigquery.NullInt64
	}
	var results []*pb.RestrictedSearchResponse_Result
	for {
		var bqRes bqResult
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Err(err).Msgf("Query results error")
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			result := &pb.RestrictedSearchResponse_Result{
				RequestId: bqRes.RequestId,
				SessionId: bqRes.SessionId,
				UserId:    bqRes.UserId,
				TenantInfo: &pb.TenantInfo{
					TenantId:   bqRes.TenantID,
					TenantName: bqRes.TenantName,
				},
				RequestTime: timestamppb.New(bqRes.RequestTime),
				RequestType: bqRes.RequestType,
			}
			if bqRes.ModelName.Valid {
				result.ModelName = &bqRes.ModelName.StringVal
			}
			if bqRes.Accepted.Valid {
				result.Accepted = &bqRes.Accepted.Bool
			}
			if bqRes.HttpStatusCode.Valid {
				castVal := uint32(bqRes.HttpStatusCode.Int64)
				result.HttpStatusCode = &castVal
			}

			results = append(results, result)
		}
	}

	log.Info().Msgf("Returning %d search results for tenant %s", len(results), tenantID)
	return &pb.RestrictedSearchResponse{
		Results: results,
	}, nil
}

// Recursively resolve the given filter group into a WHERE clause that can be used directly in a
// query, a list of query parameters, and a list of tables needed by the filters. The condition
// string is always either empty or wrapped in parentheses.
func (s RestrictedServer) resolveFilterGroup(
	filterGroup *pb.RestrictedSearchRequest_FilterGroup,
) (condition string, parameters []bigquery.QueryParameter, err error) {
	if filterGroup == nil {
		return
	} else if len(filterGroup.GetFilters()) == 0 {
		return "", nil, fmt.Errorf("Filter group has no filters")
	}

	conjunction := filterGroup.GetConjunction()
	conditions := make([]string, 0, len(filterGroup.GetFilters()))
	for _, filter := range filterGroup.GetFilters() {
		switch filter.Filter.(type) {
		case *pb.RestrictedSearchRequest_Filter_BaseFilter:
			cond, param, err := s.resolveBaseFilter(filter.GetBaseFilter())
			if err != nil {
				return "", nil, err
			}
			conditions = append(conditions, cond)
			parameters = append(parameters, param)
		case *pb.RestrictedSearchRequest_Filter_FilterGroup:
			cond, params, err := s.resolveFilterGroup(filter.GetFilterGroup())
			if err != nil {
				return "", nil, err
			}
			conditions = append(conditions, cond)
			parameters = append(parameters, params...)
		}
	}
	return "(" + strings.Join(conditions, getConjunctionString(conjunction)) + ")", parameters, nil
}

func (s RestrictedServer) resolveBaseFilter(
	filter *pb.RestrictedSearchRequest_BaseFilter,
) (condition string, parameter bigquery.QueryParameter, err error) {
	// Parameters have a UUID in the name to prevent collisions.
	switch filter.Filter.(type) {
	case *pb.RestrictedSearchRequest_BaseFilter_RequestId:
		paramName := fmt.Sprintf("REQUEST_ID_%s", s.randomStringGen())
		condition = fmt.Sprintf("%s.request_id = @%s", requestMetadataTable, paramName)
		parameter = bigquery.QueryParameter{Name: paramName, Value: filter.GetRequestId()}
	case *pb.RestrictedSearchRequest_BaseFilter_SessionId:
		paramName := fmt.Sprintf("SESSION_ID_%s", s.randomStringGen())
		condition = fmt.Sprintf("%s.session_id = @%s", requestMetadataTable, paramName)
		parameter = bigquery.QueryParameter{Name: paramName, Value: filter.GetSessionId()}
	case *pb.RestrictedSearchRequest_BaseFilter_UserId:
		paramName := fmt.Sprintf("USER_ID_%s", s.randomStringGen())
		condition = fmt.Sprintf("%s.user_id = @%s", requestMetadataTable, paramName)
		parameter = bigquery.QueryParameter{Name: paramName, Value: filter.GetUserId()}
	case *pb.RestrictedSearchRequest_BaseFilter_RequestType:
		paramName := fmt.Sprintf("REQUEST_TYPE_%s", s.randomStringGen())
		condition = fmt.Sprintf("%s.request_type = @%s", requestMetadataTable, paramName)
		parameter = bigquery.QueryParameter{Name: paramName, Value: filter.GetRequestType()}
	case *pb.RestrictedSearchRequest_BaseFilter_Accepted:
		paramName := fmt.Sprintf("ACCEPTED_%s", s.randomStringGen())
		condition = fmt.Sprintf("%s.accepted = @%s", resolutionTable, paramName)
		parameter = bigquery.QueryParameter{Name: paramName, Value: filter.GetAccepted()}
	case *pb.RestrictedSearchRequest_BaseFilter_ModelName:
		paramName := fmt.Sprintf("MODEL_NAME_%s", s.randomStringGen())
		condition = fmt.Sprintf("%s.model_name = @%s", modelTable, paramName)
		parameter = bigquery.QueryParameter{Name: paramName, Value: filter.GetModelName()}
	case *pb.RestrictedSearchRequest_BaseFilter_HttpStatusCode:
		paramName := fmt.Sprintf("HTTP_STATUS_CODE_%s", s.randomStringGen())
		condition = fmt.Sprintf("%s.code = @%s", httpStatusTable, paramName)
		parameter = bigquery.QueryParameter{Name: paramName, Value: filter.GetHttpStatusCode()}
	default:
		return condition, parameter, fmt.Errorf("Unknown filter type")
	}
	return condition, parameter, nil
}

func getConjunctionString(conjunction pb.RestrictedSearchRequest_FilterConjunction) string {
	if conjunction == pb.RestrictedSearchRequest_OR {
		return " OR "
	}
	return " AND "
}

func (s RestrictedServer) GetRequestEvents(
	req *pb.GetRequestEventsRequest,
	stream pb.RequestInsightCentralRestricted_GetRequestEventsServer,
) error {
	ctx := stream.Context()

	tenantID := req.GetTenantId()
	if tenantID == "" {
		return status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	_, err := s.checkAuthClaims(ctx, tenantID, tokenscopesproto.Scope_REQUEST_RESTRICTED_R)
	if err != nil {
		return err
	}

	requestId := req.GetRequestId()
	if requestId == "" {
		return status.Error(codes.InvalidArgument, "request_id is required")
	}

	filters := req.GetFilters()
	eventTypes := make([]string, 0, len(filters))
	for _, filter := range filters {
		switch filter.Filter.(type) {
		case *pb.GetRequestEventsRequest_Filter_EventType:
			eventTypes = append(eventTypes, filter.GetEventType())
		default:
			return status.Error(codes.InvalidArgument, "Unknown filter type")
		}
	}
	hasEventTypeFilter := len(eventTypes) > 0
	if hasEventTypeFilter {
		log.Info().Msgf("Event type filter: %v", eventTypes)
	}

	rc, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request context")
		return status.Error(codes.Internal, "Failed to create request context")
	}

	log.Info().Msgf("Listing events for tenant %s, request %s", tenantID, requestId)
	prefix := fmt.Sprintf("request/%s/", requestId)
	objectPaths, err := s.gcsProxyClient.ListObjects(ctx, rc, tenantID, prefix, gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to list objects")
		return status.Error(codes.Internal, "Failed to list objects")
	}
	log.Info().Msgf("Found %d events", len(objectPaths))

	// TODO(zhewei): fetch events data in parallel to improve performance.
	for _, objectPath := range objectPaths {
		// Skip events that don't match the event type filter.
		// We use object path to identify the event type.
		if hasEventTypeFilter {
			skip := true
			for _, eventType := range eventTypes {
				if strings.Contains(objectPath, eventType) {
					skip = false
					break
				}
			}
			if skip {
				log.Info().Msgf("Skipping event %s", objectPath)
				continue
			}
		}

		log.Info().Msgf("Reading event %s", objectPath)
		objectBytes, err := s.gcsProxyClient.ReadObject(ctx, rc, tenantID, objectPath, gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to read object %s", objectPath)
			return status.Error(codes.Internal, "Failed to read object")
		}

		event := &requestinsightproto.RequestEvent{}
		if err := proto.Unmarshal(objectBytes, event); err != nil {
			log.Error().Err(err).Msgf("Failed to unmarshal object %s", objectPath)
			return status.Error(codes.Internal, "Failed to unmarshal object")
		}

		log.Info().Msgf("Sending event %s", event.EventId)
		if err := stream.Send(&pb.GetRequestEventResponse{RequestEvent: event}); err != nil {
			log.Error().Err(err).Msgf("Failed to send response")
			return status.Error(codes.Internal, "Failed to send response")
		}
	}

	return nil
}

func (s RestrictedServer) GetRemoteAgentLogs(
	req *pb.GetRemoteAgentLogsRequest,
	stream pb.RequestInsightCentralRestricted_GetRemoteAgentLogsServer,
) error {
	ctx := stream.Context()

	tenantID := req.GetTenantId()
	_, err := s.checkAuthClaims(ctx, tenantID, tokenscopesproto.Scope_REQUEST_RESTRICTED_R)
	if err != nil {
		return err
	}

	agentID := req.GetAgentId()
	if agentID == "" {
		return status.Error(codes.InvalidArgument, "agent_id is required")
	}
	if req.TimeFilter == nil || req.TimeFilter.StartTime == nil {
		return status.Error(codes.InvalidArgument, "time_filter with start_time is required")
	}
	maxResults := req.GetMaxResults()
	if maxResults == 0 {
		maxResults = defaultMaxResults
	}

	log.Info().Msgf("Listing remote agent logs for tenant %s, agent %s", tenantID, agentID)

	// Search the logs table for the given agent ID
	parameters := []bigquery.QueryParameter{
		{Name: "TENANT_ID", Value: tenantID},
		{Name: "AGENT_ID", Value: agentID},
		{Name: "MAX_RESULTS", Value: maxResults},
	}

	timeFilterCondition := "time >= @START_TIME"
	parameters = append(parameters, bigquery.QueryParameter{Name: "START_TIME", Value: req.TimeFilter.StartTime.AsTime()})
	if req.TimeFilter.EndTime != nil {
		timeFilterCondition += " AND time <= @END_TIME"
		parameters = append(parameters, bigquery.QueryParameter{Name: "END_TIME", Value: req.TimeFilter.EndTime.AsTime()})
	}

	// Construct the query
	queryTemplate := `
		SELECT
			event_id as eventId,
			time as eventTime,
		FROM {{.datasetName}}.{{.remoteAgentLogsTable}}
		WHERE
			tenant_id = @TENANT_ID
			AND session_id = @AGENT_ID
			AND {{.timeFilter}}
		ORDER BY time DESC
		LIMIT @MAX_RESULTS
	`
	t := template.New("query")
	t, err = t.Parse(queryTemplate)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to parse query template")
		return status.Errorf(codes.Internal, "Failed to parse query template")
	}
	var queryBuf bytes.Buffer
	err = t.Execute(
		&queryBuf,
		map[string]interface{}{
			"datasetName":          s.datasetName,
			"remoteAgentLogsTable": remoteAgentLogsTable,
			"timeFilter":           timeFilterCondition,
		},
	)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to execute query template")
		return status.Error(codes.Internal, "Failed to execute query template")
	}

	// Run the query and parse the results
	query := s.bqClient.Query(queryBuf.String())
	query.Parameters = parameters
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Err(err).Msgf("Query error")
		return status.Error(codes.Internal, "Query error")
	}

	type bqResult struct {
		EventId   string
		EventTime time.Time
	}
	var results []bqResult
	for {
		var bqRes bqResult
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Err(err).Msgf("Query results error")
			return status.Error(codes.Internal, "Error parsing query results")
		} else {
			results = append(results, bqRes)
		}
	}

	// Retrieve and return the log events
	for _, result := range results {
		// Retrieve the log event from GCS
		rc, err := requestcontext.FromGrpcContext(ctx)
		if err != nil {
			log.Error().Err(err).Msg("Failed to create request context")
			return status.Error(codes.Internal, "Failed to create request context")
		}

		objectPath := fmt.Sprintf("session/%s/remote_agent_log/%s", agentID, result.EventId)
		log.Info().Msgf("Reading event %s", objectPath)
		objectBytes, err := s.gcsProxyClient.ReadObject(ctx, rc, tenantID, objectPath, gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to read object %s", objectPath)
			return status.Error(codes.Internal, "Failed to read object")
		}

		event := &requestinsightproto.SessionEvent{}
		if err := proto.Unmarshal(objectBytes, event); err != nil {
			log.Error().Err(err).Msgf("Failed to unmarshal object %s", objectPath)
			return status.Error(codes.Internal, "Failed to unmarshal object")
		}

		if err := stream.Send(&pb.GetRemoteAgentLogsResponse{RemoteAgentLog: event.GetRemoteAgentLog()}); err != nil {
			log.Error().Err(err).Msgf("Failed to send response")
			return status.Error(codes.Internal, "Failed to send response")
		}
	}

	return nil
}

func (s RestrictedServer) DeleteGcsExportedBlobs(
	stream pb.RequestInsightCentralRestricted_DeleteGcsExportedBlobsServer,
) error {
	ctx := stream.Context()

	// Set up concurrency control
	const maxConcurrency = 10
	semaphore := make(chan struct{}, maxConcurrency)

	// Channels to collect requests from stream, and stream results back to client
	const requestsAndResultsChanSize = 1000
	requestsChan := make(chan *pb.DeleteGcsExportedBlobsRequest, requestsAndResultsChanSize)
	resultsChan := make(chan *pb.DeleteGcsExportedBlobsResponse, requestsAndResultsChanSize)

	// Error channel to capture any errors from goroutines
	errorChan := make(chan error, 1)

	var wg sync.WaitGroup

	// Start goroutine to read requests from stream
	go func() {
		defer close(requestsChan)

		for {
			req, err := stream.Recv()
			if err == io.EOF {
				break
			}
			if err != nil {
				select {
				case errorChan <- status.Errorf(codes.Internal, "failed to receive request: %v", err):
				default:
				}
				return
			}

			requestsChan <- req
		}
	}()

	// Start goroutine to send results back to client
	resultsDone := make(chan struct{})
	go func() {
		defer close(resultsDone)
		for result := range resultsChan {
			if err := stream.Send(result); err != nil {
				select {
				case errorChan <- status.Errorf(codes.Internal, "failed to send response: %v", err):
				default:
				}
				return
			}
		}
	}()

	// Process requests concurrently
	for req := range requestsChan {
		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore

		go func(req *pb.DeleteGcsExportedBlobsRequest) {
			defer wg.Done()
			defer func() { <-semaphore }() // Release semaphore

			tenantID := req.GetTenantId()
			blobName := req.GetBlobName()

			var validationError string
			var requestContext *requestcontext.RequestContext
			if tenantID == "" || blobName == "" {
				validationError = "tenant_id and blob_name are required"
			} else if _, err := s.checkAuthClaims(ctx, tenantID, tokenscopesproto.Scope_REQUEST_ADMIN); err != nil {
				validationError = "unauthorized access to tenant"
			} else {
				requestContext, err = requestcontext.FromGrpcContext(ctx)
				if err != nil {
					log.Error().Err(err).Msg("Failed to create request context")
					validationError = "failed to create request context"
				}
			}

			if validationError != "" {
				result := &pb.DeleteGcsExportedBlobsResponse{
					BlobName:     blobName,
					Found:        false,
					Deleted:      false,
					ErrorMessage: validationError,
				}
				select {
				case resultsChan <- result:
				case <-ctx.Done():
				}
				return
			}

			result := s.processBlobDeletion(ctx, requestContext, tenantID, blobName)

			select {
			case resultsChan <- result:
			case <-ctx.Done():
				return
			}
		}(req)
	}

	// Wait for all goroutines to complete
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// Wait for results to be sent or error to occur
	select {
	case <-resultsDone:
		log.Info().Msgf("Completed GCS blob deletion")
		return nil
	case err := <-errorChan:
		return err
	case <-ctx.Done():
		return status.Error(codes.Canceled, "request canceled")
	}
}

func (s RestrictedServer) processBlobDeletion(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	tenantID string,
	blobName string,
) *pb.DeleteGcsExportedBlobsResponse {
	result := &pb.DeleteGcsExportedBlobsResponse{
		BlobName: blobName,
	}

	// Currently exported blobs are stored under {tenant_id}/blobs/{blob_name}.
	// Note: gcsProxyClient automatically handles the tenant_id prefix
	blobPath := fmt.Sprintf("blobs/%s", blobName)

	// Delete the blob using gcsProxyClient
	existed, err := s.gcsProxyClient.DeleteObject(ctx, requestContext, tenantID, blobPath, gcsproxyproto.BucketType_NON_ENTERPRISE_BLOBS)
	if err != nil {
		result.Found = false
		result.Deleted = false
		result.ErrorMessage = fmt.Sprintf("Error deleting blob: %v", err)
		log.Error().Err(err).Msgf("Error deleting blob %s for tenant %s", blobPath, tenantID)
	} else {
		result.Found = existed
		result.Deleted = existed
		if existed {
			log.Info().Msgf("Successfully deleted blob: %s for tenant %s", blobPath, tenantID)
		} else {
			log.Info().Msgf("Blob not found: %s for tenant %s", blobPath, tenantID)
		}
	}

	return result
}

// Wrapper around GCS event information, for use in deletion.
type gcsEvent struct {
	TenantID   string
	PathPrefix string
}

func (s *RestrictedServer) DeleteEventsForUser(
	ctx context.Context, req *pb.DeleteEventsForUserRequest,
) (*pb.DeleteEventsForUserResponse, error) {
	authClaims, err := s.checkAuthClaims(ctx, "", tokenscopesproto.Scope_REQUEST_ADMIN)
	if err != nil {
		return nil, err
	}

	requestCtx, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to create request context")
	}

	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("DeleteEventsForUser request received for user %s", req.GetUserId()),
		requestCtx,
		audit.NewUser(req.GetUserId()),
		audit.NewProtoRequest(req),
	)

	// end_time is required to be at least 90 minutes in the past, or else we can get errors when we
	// try to delete from BigQuery about there being events in the streaming buffer.
	if req.GetEndTime() == nil {
		return nil, status.Error(codes.InvalidArgument, "end_time is required")
	}
	endTime := req.GetEndTime().AsTime()
	if endTime.Add(90 * time.Minute).After(s.clock.Now()) {
		return nil, status.Error(codes.InvalidArgument, "end_time must be at least 90 minutes in the past")
	}
	// start_time isn't required. If not set, use something in the distant past so we return everything.
	startTime := time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC)
	if req.GetStartTime() != nil {
		startTime = req.GetStartTime().AsTime()
	}

	eventsToDeleteChan := make(chan *gcsEvent, 1000)
	throttleChan := make(chan struct{}, 100)

	var deletedEvents atomic.Uint32
	var failedEventDeletions atomic.Uint32

	// Start a goroutine to gather request/session events. Failing to run either of these queries
	// (which should be unlikely) fails the entire request.
	var requestQueryErr error
	var sessionQueryErr error
	queryDoneChan := make(chan struct{})
	go func() {
		defer close(queryDoneChan)
		defer close(eventsToDeleteChan)
		if err := s.getRequestsForDeletion(ctx, req.GetUserId(), startTime, endTime, eventsToDeleteChan); err != nil {
			requestQueryErr = err
			return
		}
		if err := s.getSessionsForDeletion(ctx, req.GetUserId(), startTime, endTime, eventsToDeleteChan); err != nil {
			sessionQueryErr = err
			return
		}
	}()

	// Start goroutine to delete all GCS events for the gathered request/session events.
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()

		for gcsPrefix := range eventsToDeleteChan {
			throttleChan <- struct{}{}

			wg.Add(1)
			go func(gcsPrefix *gcsEvent) {
				defer func() { <-throttleChan }()
				defer wg.Done()

				// List all objects under the prefix
				objects, err := s.gcsProxyClient.ListObjects(
					ctx, requestCtx, gcsPrefix.TenantID, gcsPrefix.PathPrefix,
					gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Msgf("Failed to list objects for prefix %s", gcsPrefix)
					failedEventDeletions.Add(1)
					return
				}

				// Delete all objects, in parallel
				log.Ctx(ctx).Info().Msgf("Deleting %d events under %s", len(objects), gcsPrefix.PathPrefix)
				for _, object := range objects {
					wg.Add(1)
					go func(objectPath string) {
						defer wg.Done()

						existed, err := s.gcsProxyClient.DeleteObject(
							ctx, requestCtx, gcsPrefix.TenantID, objectPath,
							gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
						if err != nil {
							log.Ctx(ctx).Error().Err(err).Msgf("Failed to delete object %s", objectPath)
							failedEventDeletions.Add(1)
						} else if existed {
							log.Ctx(ctx).Info().Msgf("Deleted object %s", objectPath)
							deletedEvents.Add(1)
						} else if !existed {
							log.Ctx(ctx).Info().Msgf("Object %s did not exist", objectPath)
						}
					}(object)
				}
			}(gcsPrefix)
		}
	}()

	// Wait for query goroutine to complete.
	<-queryDoneChan
	if requestQueryErr != nil {
		return nil, fmt.Errorf("Failed to get request IDs for user %s: %w", req.GetUserId(), requestQueryErr)
	}
	if sessionQueryErr != nil {
		return nil, fmt.Errorf("Failed to get session IDs for user %s: %w", req.GetUserId(), sessionQueryErr)
	}

	// Wait for GCS deletions to complete.
	wg.Wait()

	// Delete request_metadata search rows. Ideally we would delete from all the tables but currently
	// request_metadata is the only table with PII and the primary one used for searching.
	// TODO(jacqueline): Add opaque_user_id to all tables and fix this.
	var deletedRows uint32
	deletionStatus := pb.DeleteEventsForUserResponse_NOT_ATTEMPTED
	if failedEventDeletions.Load() == 0 {
		deletedRows, err = s.deleteRequestMetadataForUser(ctx, req.GetUserId(), startTime, endTime)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to delete request_metadata for user %s", req.GetUserId())
			deletionStatus = pb.DeleteEventsForUserResponse_FAILURE
		} else {
			deletionStatus = pb.DeleteEventsForUserResponse_SUCCESS
		}
	}

	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf(
			"DeleteEventsForUser deleted %d events and %d search rows for user %s",
			deletedEvents.Load(), deletedRows, req.GetUserId(),
		),
		requestCtx,
		audit.NewUser(req.GetUserId()),
		audit.NewProtoRequest(req),
	)

	return &pb.DeleteEventsForUserResponse{
		NumSuccessfulEventDeletions: deletedEvents.Load(),
		NumFailedEventDeletions:     failedEventDeletions.Load(),
		SearchDeletionStatus:        deletionStatus,
		NumDeletedSearchRows:        deletedRows,
	}, nil
}

func (s *RestrictedServer) getRequestsForDeletion(
	ctx context.Context, userID string, startTime, endTime time.Time, resultChan chan<- *gcsEvent,
) error {
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT request_id, ARRAY_AGG(DISTINCT tenant_id) tenant_ids
		FROM %s.request_metadata
		WHERE opaque_user_id = @USER_ID
			AND time >= @START_TIME
			AND time <= @END_TIME
		GROUP BY request_id
	`, s.nonenterpriseDatasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "USER_ID", Value: userID},
		{Name: "START_TIME", Value: startTime},
		{Name: "END_TIME", Value: endTime},
	}

	it, err := query.Read(ctx)
	if err != nil {
		return err
	}

	for {
		var row struct {
			RequestID string   `bigquery:"request_id"`
			TenantIDs []string `bigquery:"tenant_ids"`
		}
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			return err
		}

		for _, tenantID := range row.TenantIDs {
			resultChan <- &gcsEvent{
				TenantID:   tenantID,
				PathPrefix: fmt.Sprintf("request/%s/", row.RequestID),
			}
		}
	}

	return nil
}

func (s *RestrictedServer) getSessionsForDeletion(
	ctx context.Context, userID string, startTime, endTime time.Time, resultChan chan<- *gcsEvent,
) error {
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT session_id, ARRAY_AGG(DISTINCT tenant_id) tenant_ids
		FROM %s.request_metadata
		WHERE opaque_user_id = @USER_ID
			AND time >= @START_TIME
			AND time <= @END_TIME
		GROUP BY session_id
	`, s.nonenterpriseDatasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "USER_ID", Value: userID},
		{Name: "START_TIME", Value: startTime},
		{Name: "END_TIME", Value: endTime},
	}

	it, err := query.Read(ctx)
	if err != nil {
		return err
	}

	for {
		var row struct {
			SessionID string   `bigquery:"session_id"`
			TenantIDs []string `bigquery:"tenant_ids"`
		}
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			return err
		}

		for _, tenantID := range row.TenantIDs {
			resultChan <- &gcsEvent{
				TenantID:   tenantID,
				PathPrefix: fmt.Sprintf("session/%s/", row.SessionID),
			}
		}
	}

	return nil
}

// Returns the number of rows deleted.
func (s *RestrictedServer) deleteRequestMetadataForUser(
	ctx context.Context, userID string, startTime, endTime time.Time,
) (uint32, error) {
	query := s.bqClient.Query(fmt.Sprintf(`
		DELETE FROM %s.request_metadata
		WHERE opaque_user_id = @USER_ID
			AND time >= @START_TIME
			AND time <= @END_TIME
	`, s.nonenterpriseDatasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "USER_ID", Value: userID},
		{Name: "START_TIME", Value: startTime},
		{Name: "END_TIME", Value: endTime},
	}

	job, err := query.Run(ctx)
	if err != nil {
		return 0, fmt.Errorf("Failed to run query: %w", err)
	}
	jobStatus, err := job.Wait(ctx)
	if err != nil {
		return 0, fmt.Errorf("Failed to wait for query job: %w", err)
	}
	if jobStatus.Err() != nil {
		return 0, fmt.Errorf("Query job failed: %w", jobStatus.Err())
	}

	jobStatus = job.LastStatus()
	if jobStatus == nil {
		return 0, fmt.Errorf("Failed to get job status")
	}
	stats := jobStatus.Statistics.Details.(*bigquery.QueryStatistics)
	return uint32(stats.NumDMLAffectedRows), nil
}

func (s *RestrictedServer) DeleteEventsForTenant(
	ctx context.Context, req *pb.DeleteEventsForTenantRequest,
) (*pb.DeleteEventsForTenantResponse, error) {
	if req.GetTenantId() == "" {
		return nil, status.Error(codes.InvalidArgument, "Missing tenant id")
	}

	authClaims, err := s.checkAuthClaims(ctx, req.GetTenantId(), tokenscopesproto.Scope_REQUEST_ADMIN)
	if err != nil {
		return nil, err
	}

	requestCtx, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to create request context")
	}

	s.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("DeleteEventsForTenant request received for tenant %s", req.GetTenantId()),
		requestCtx,
		audit.NewTenantID(req.GetTenantId()),
		audit.NewProtoRequest(req),
	)

	// Run deletion in the background because for large tenants this can take a long time.
	go func() {
		ctx := context.Background()

		objectsChan, err := s.gcsProxyClient.ListObjectsStream(
			ctx, requestCtx, req.GetTenantId(), "", gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to list objects")
			return
		}

		// Delete all objects, in parallel
		var errGroup errgroup.Group
		errGroup.SetLimit(200)
		var successfulDeletions atomic.Uint32
		var notFoundObjects atomic.Uint32
		var failedDeletions atomic.Uint32
		for objectPath := range objectsChan {
			errGroup.Go(func() error {
				existed, err := s.gcsProxyClient.DeleteObject(
					ctx, requestCtx, req.GetTenantId(), objectPath, gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
				)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Msgf("Failed to delete object %s", objectPath)
					failedDeletions.Add(1)
					return err
				}

				if existed {
					log.Ctx(ctx).Debug().Msgf("Deleted %s", objectPath)
					successfulDeletions.Add(1)
				} else {
					log.Ctx(ctx).Info().Msgf("%s did not exist", objectPath)
					notFoundObjects.Add(1)
				}
				return nil
			})
		}

		if err := errGroup.Wait(); err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to delete objects: %v", err)
		}

		s.auditLogger.WriteAuditLog(
			authClaims,
			fmt.Sprintf(
				"DeleteEventsForTenant deleted %d objects for tenant %s. %d objects did not exist. %d deletions failed.",
				successfulDeletions.Load(), req.GetTenantId(), notFoundObjects.Load(), failedDeletions.Load(),
			),
			requestCtx,
			audit.NewTenantID(req.GetTenantId()),
			audit.NewProtoRequest(req),
		)
	}()

	return &pb.DeleteEventsForTenantResponse{}, nil
}
