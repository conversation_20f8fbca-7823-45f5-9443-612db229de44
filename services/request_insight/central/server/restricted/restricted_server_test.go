package ri_central_restricted

import (
	"context"
	"fmt"
	"io"
	"os"
	"reflect"
	"strings"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/augmentcode/augment/base/go/clock"
	"github.com/augmentcode/augment/base/logging/audit"
	testutils "github.com/augmentcode/augment/base/test_utils"
	bqemulator "github.com/augmentcode/augment/base/test_utils/bigquery/emulator"
	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	gcsproxyproto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	pb "github.com/augmentcode/augment/services/request_insight/central/proto"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/iterator"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	projectId   = "test-project"
	datasetName = "test_dataset"

	// For shared use by tests that don't care about data.
	noDataTenantID   = "123"
	noDataTenantName = "nodata"
)

var (
	bqEmulator     *bqemulator.BigQueryEmulator  = nil
	gcsProxyClient gcsproxyclient.GcsProxyClient = nil
	mockTime       time.Time                     = time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
)

// TestMain is the entry point for the test suite. This is where shared test fixtures should be
// set up.
func TestMain(m *testing.M) {
	// Set up a shared emulator for all the tests.
	var err error
	bqEmulator, err = bqemulator.New(context.Background(), projectId, datasetName)
	if err != nil {
		panic(err)
	}
	defer bqEmulator.Close()

	err = bqEmulator.LoadJsonSchemaFile(context.Background(), "../../../support_database/search_dataset/schema.json")
	if err != nil {
		panic(err)
	}

	// Set up a mock GCS proxy client for all the tests.
	gcsProxyClient = gcsproxyclient.NewMockGcsProxyClient()
	defer gcsProxyClient.Close()

	// Run the tests.
	os.Exit(m.Run())
}

func testServer(t *testing.T) *RestrictedServer {
	if bqEmulator == nil {
		t.Fatal("BigQuery emulator is not set up")
	}

	return &RestrictedServer{
		bqClient:                 bqEmulator.Client,
		datasetName:              datasetName,
		nonenterpriseDatasetName: datasetName,
		gcsProxyClient:           gcsProxyClient,
		auditLogger:              audit.NewDefaultAuditLogger(),
		clock:                    clock.NewMockClock(mockTime),
		randomStringGen:          DefaultRandomStringGenerator,
	}
}

var mockRandomStringGenerator = func() string {
	return "uuid"
}

// Test server where random strings are always "uuid". Useful for testing query conditions.
func mockRandomTestServer(t *testing.T) *RestrictedServer {
	if bqEmulator == nil {
		t.Fatal("BigQuery emulator is not set up")
	}

	return &RestrictedServer{
		bqClient:        bqEmulator.Client,
		datasetName:     datasetName,
		randomStringGen: mockRandomStringGenerator,
	}
}

func userContext(tenantID string, scope string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:   tenantID,
		TenantName: "test-tenant",
		UserID:     "test-user",
		Scope:      []string{scope},
	}
	md := metadata.New(map[string]string{
		"x-tenant-id":   tenantID,
		"x-tenant-name": "test-tenant",
		"x-user-id":     "test-user",
	})
	return metadata.NewIncomingContext(claims.NewContext(context.Background()), md)
}

func iapContext(scope string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:         "",
		TenantName:       "",
		UserID:           "<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: "INTERNAL_IAP",
		Scope:            []string{scope},
	}
	md := metadata.New(map[string]string{
		"x-tenant-id":   "",
		"x-tenant-name": "",
		"x-user-id":     "<EMAIL>",
	})
	return metadata.NewIncomingContext(claims.NewContext(context.Background()), md)
}

func writeMetadataRow(
	t *testing.T, requestId string, tenantID string, tenantName string, time time.Time,
	requestType string, sessionId string, userAgent string, userID string, opaqueUserID string,
) {
	query := bqEmulator.Client.Query(fmt.Sprintf(`
		INSERT INTO %s.request_metadata (request_id, tenant, tenant_id, time, request_type, session_id, user_agent, user_id, opaque_user_id, user_id_type)
		VALUES (@REQUEST_ID, @TENANT, @TENANT_ID, @TIME, @REQUEST_TYPE, @SESSION_ID, @USER_AGENT, @USER_ID, @OPAQUE_USER_ID, "AUGMENT")
	`, datasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "REQUEST_ID", Value: requestId},
		{Name: "TENANT", Value: tenantName},
		{Name: "TENANT_ID", Value: tenantID},
		{Name: "TIME", Value: time},
		{Name: "REQUEST_TYPE", Value: requestType},
		{Name: "SESSION_ID", Value: sessionId},
		{Name: "USER_AGENT", Value: userAgent},
		{Name: "USER_ID", Value: userID},
		{Name: "OPAQUE_USER_ID", Value: opaqueUserID},
	}
	_, err := query.Read(context.Background())
	if err != nil {
		t.Fatalf("Failed to write metadata row: %v", err)
	}
}

func writeModelRow(
	t *testing.T, requestId string, tenantID string, tenantName string, time time.Time,
	modelName string,
) {
	query := bqEmulator.Client.Query(fmt.Sprintf(`
		INSERT INTO %s.model (request_id, tenant_id, tenant, time, model_name)
		VALUES (@REQUEST_ID, @TENANT_ID, @TENANT, @TIME, @MODEL_NAME)
	`, datasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "REQUEST_ID", Value: requestId},
		{Name: "TENANT_ID", Value: tenantID},
		{Name: "TENANT", Value: tenantName},
		{Name: "TIME", Value: time},
		{Name: "MODEL_NAME", Value: modelName},
	}
	_, err := query.Read(context.Background())
	if err != nil {
		t.Fatalf("Failed to write model row: %v", err)
	}
}

func writeResolutionRow(
	t *testing.T, requestId string, tenantID string, tenantName string, time time.Time,
	accepted bool,
) {
	query := bqEmulator.Client.Query(fmt.Sprintf(`
		INSERT INTO %s.resolution (request_id, tenant_id, tenant, time, accepted)
		VALUES (@REQUEST_ID, @TENANT_ID, @TENANT, @TIME, @ACCEPTED)
	`, datasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "REQUEST_ID", Value: requestId},
		{Name: "TENANT_ID", Value: tenantID},
		{Name: "TENANT", Value: tenantName},
		{Name: "TIME", Value: time},
		{Name: "ACCEPTED", Value: accepted},
	}
	_, err := query.Read(context.Background())
	if err != nil {
		t.Fatalf("Failed to write resolution row: %v", err)
	}
}

func writeHttpStatusRow(
	t *testing.T, requestId string, tenantID string, tenantName string, time time.Time,
	code int32,
) {
	query := bqEmulator.Client.Query(fmt.Sprintf(`
		INSERT INTO %s.http_status (request_id, tenant_id, tenant, time, code)
		VALUES (@REQUEST_ID, @TENANT_ID, @TENANT, @TIME, @CODE)
	`, datasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "REQUEST_ID", Value: requestId},
		{Name: "TENANT_ID", Value: tenantID},
		{Name: "TENANT", Value: tenantName},
		{Name: "TIME", Value: time},
		{Name: "CODE", Value: code},
	}
	_, err := query.Read(context.Background())
	if err != nil {
		t.Fatalf("Failed to write http status row: %v", err)
	}
}

// Helper function to write a remote agent log row to BigQuery
func writeRemoteAgentLogRow(
	t *testing.T, sessionId string, tenantID string, tenantName string, time time.Time, eventID string,
) {
	query := bqEmulator.Client.Query(fmt.Sprintf(`
		INSERT INTO %s.remote_agent_log (session_id, tenant_id, tenant, time, event_id)
		VALUES (@SESSION_ID, @TENANT_ID, @TENANT, @TIME, @EVENT_ID)
	`, datasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "SESSION_ID", Value: sessionId},
		{Name: "TENANT_ID", Value: tenantID},
		{Name: "TENANT", Value: tenantName},
		{Name: "TIME", Value: time},
		{Name: "EVENT_ID", Value: eventID},
	}
	_, err := query.Read(context.Background())
	if err != nil {
		t.Fatalf("Failed to write remote agent log row: %v", err)
	}
}

// Run basic auth tests that apply to all endpoints.
func runAuthTests[ReqT proto.Message, RespT proto.Message](
	t *testing.T, goodRequest ReqT, requestFunc func(context.Context, ReqT) (RespT, error),
) {
	t.Run("auth validation", func(t *testing.T) {
		// Test missing auth claims.
		_, err := requestFunc(context.Background(), goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for missing auth claims")
		}

		// Test mismatch between auth claims and request.
		wrongTenantClaims := &auth.AugmentClaims{
			TenantID:   "456",
			TenantName: "another-tenant",
			UserID:     "another-user",
			Scope:      []string{"REQUEST_RESTRICTED_R"},
		}
		badCtx := wrongTenantClaims.NewContext(context.Background())
		_, err = requestFunc(badCtx, goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for wrong tenant ID in claims")
		}

		// Test incorrect scope.
		badClaims := &auth.AugmentClaims{
			TenantID:   noDataTenantID,
			TenantName: noDataTenantName,
			UserID:     "test-user",
			Scope:      []string{"CONTENT_RW"},
		}
		badCtx = badClaims.NewContext(context.Background())
		_, err = requestFunc(badCtx, goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for missing scope")
		}
	})
}

func TestRestrictedSearch(t *testing.T) {
	server := testServer(t)

	t.Run("auth validation", func(t *testing.T) {
		runAuthTests(
			t,
			&pb.RestrictedSearchRequest{
				TimeFilter: &pb.TimeFilter{
					StartTime: timestamppb.New(mockTime),
				},
				TenantId: noDataTenantID,
			},
			server.RestrictedSearch,
		)
	})

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.RestrictedSearchRequest{
			// Missing time filter.
			{
				TenantId: noDataTenantID,
			},
			// Missing start time.
			{
				TimeFilter: &pb.TimeFilter{
					EndTime: timestamppb.New(mockTime),
				},
				TenantId: noDataTenantID,
			},
			// Missing tenant id.
			{
				TimeFilter: &pb.TimeFilter{
					StartTime: timestamppb.New(mockTime),
				},
			},
		}

		for _, req := range badRequests {
			_, err := server.RestrictedSearch(userContext("123", "REQUEST_RESTRICTED_R"), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected InvalidArgument error for request: %v", req)
			}
		}
	})

	t.Run("tenant id filter", func(t *testing.T) {
		// Make sure we only return results for the authorized tenant id.
		tenantID := uuid.New().String()
		writeMetadataRow(
			t, "test-request", tenantID, "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)
		writeMetadataRow(
			t, "test-request", "unauthorized", "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)

		resultRow := &pb.RestrictedSearchResponse_Result{
			RequestId:   "test-request",
			SessionId:   "test-session",
			UserId:      "test-user-id",
			TenantInfo:  &pb.TenantInfo{TenantId: tenantID, TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			userContext(tenantID, "REQUEST_RESTRICTED_R"),
			[]testutils.RequestResponse{
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
							EndTime:   timestamppb.New(mockTime.Add(time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow},
					},
				},
			},
			server.RestrictedSearch,
		)
	})

	t.Run("max results", func(t *testing.T) {
		// Test that we limit results correctly.
		tenantID := uuid.New().String()
		writeMetadataRow(
			t, "test-request-1", tenantID, "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)
		writeMetadataRow(
			t, "test-request-2", tenantID, "test-tenant", mockTime.Add(time.Hour), "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)

		resultRow1 := &pb.RestrictedSearchResponse_Result{
			RequestId:   "test-request-1",
			SessionId:   "test-session",
			UserId:      "test-user-id",
			TenantInfo:  &pb.TenantInfo{TenantId: tenantID, TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}
		resultRow2 := &pb.RestrictedSearchResponse_Result{
			RequestId:   "test-request-2",
			SessionId:   "test-session",
			UserId:      "test-user-id",
			TenantInfo:  &pb.TenantInfo{TenantId: tenantID, TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime.Add(time.Hour)),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			userContext(tenantID, "REQUEST_RESTRICTED_R"),
			[]testutils.RequestResponse{
				// When max results is 1, only the most recent result should be returned.
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId:   tenantID,
						MaxResults: proto.Uint32(1),
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow2},
					},
				},
				// When max results is 2, both results should be returned, in reverse chronological order.
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId:   tenantID,
						MaxResults: proto.Uint32(2),
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow2, resultRow1},
					},
				},
			},
			server.RestrictedSearch,
		)
	})

	t.Run("time filter", func(t *testing.T) {
		// Make sure we filter by time appropriately.
		tenantID := uuid.New().String()
		writeMetadataRow(
			t, "test-request", tenantID, "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)

		resultRow := &pb.RestrictedSearchResponse_Result{
			RequestId:   "test-request",
			SessionId:   "test-session",
			UserId:      "test-user-id",
			TenantInfo:  &pb.TenantInfo{TenantId: tenantID, TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			userContext(tenantID, "REQUEST_RESTRICTED_R"),
			[]testutils.RequestResponse{
				// Start and end time that include the request.
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
							EndTime:   timestamppb.New(mockTime.Add(time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow},
					},
				},
				// Just a start time.
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow},
					},
				},
				// Start time that excludes the request.
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{},
				},
				// End time that excludes the request.
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour * 2)),
							EndTime:   timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{},
				},
			},
			server.RestrictedSearch,
		)
	})

	t.Run("model filter", func(t *testing.T) {
		// Test filtering by model name, as a stand-in for filtering by all the various filters.
		tenantID := uuid.New().String()
		writeMetadataRow(
			t, "test-request1", tenantID, "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)
		writeModelRow(t, "test-request1", tenantID, "test-tenant", mockTime, "test-model1")
		writeMetadataRow(
			t, "test-request2", tenantID, "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)
		writeModelRow(t, "test-request2", tenantID, "test-tenant", mockTime, "test-model2")

		resultRow1 := &pb.RestrictedSearchResponse_Result{
			RequestId:   "test-request1",
			SessionId:   "test-session",
			UserId:      "test-user-id",
			TenantInfo:  &pb.TenantInfo{TenantId: tenantID, TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
			ModelName:   proto.String("test-model1"),
		}

		testutils.CheckResponses(
			t,
			userContext(tenantID, "REQUEST_RESTRICTED_R"),
			[]testutils.RequestResponse{
				// Model filter that filters to one request.
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId: tenantID,
						Filters: &pb.RestrictedSearchRequest_FilterGroup{
							Filters: []*pb.RestrictedSearchRequest_Filter{
								{
									Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
										BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
											Filter: &pb.RestrictedSearchRequest_BaseFilter_ModelName{
												ModelName: "test-model1",
											},
										},
									},
								},
							},
						},
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow1},
					},
				},
			},
			server.RestrictedSearch,
		)
	})

	t.Run("all tables", func(t *testing.T) {
		// Make sure we're joining all the tables we should be.
		tenantID := uuid.New().String()
		writeMetadataRow(
			t, "test-request", tenantID, "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)
		writeModelRow(t, "test-request", tenantID, "test-tenant", mockTime, "test-model")
		writeResolutionRow(t, "test-request", tenantID, "test-tenant", mockTime, true)
		writeHttpStatusRow(t, "test-request", tenantID, "test-tenant", mockTime, 200)

		resultRow := &pb.RestrictedSearchResponse_Result{
			RequestId:      "test-request",
			SessionId:      "test-session",
			UserId:         "test-user-id",
			TenantInfo:     &pb.TenantInfo{TenantId: tenantID, TenantName: "test-tenant"},
			RequestTime:    timestamppb.New(mockTime),
			RequestType:    "COMPLETION",
			ModelName:      proto.String("test-model"),
			Accepted:       proto.Bool(true),
			HttpStatusCode: proto.Uint32(200),
		}

		testutils.CheckResponses(
			t,
			userContext(tenantID, "REQUEST_RESTRICTED_R"),
			[]testutils.RequestResponse{
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow},
					},
				},
			},
			server.RestrictedSearch,
		)
	})

	t.Run("no metadata", func(t *testing.T) {
		// Make sure we don't return results for requests that don't have metadata.
		tenantID := uuid.New().String()
		writeModelRow(t, "test-request", tenantID, "test-tenant", mockTime, "test-model")
		writeResolutionRow(t, "test-request", tenantID, "test-tenant", mockTime, true)
		writeHttpStatusRow(t, "test-request", tenantID, "test-tenant", mockTime, 200)

		testutils.CheckResponses(
			t,
			userContext(tenantID, "REQUEST_RESTRICTED_R"),
			[]testutils.RequestResponse{
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{},
				},
			},
			server.RestrictedSearch,
		)
	})

	t.Run("only metadata", func(t *testing.T) {
		// Make sure we return results for requests that only have metadata.
		tenantID := uuid.New().String()
		writeMetadataRow(
			t, "test-request", tenantID, "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id", "test-opaque-user-id",
		)

		resultRow := &pb.RestrictedSearchResponse_Result{
			RequestId:   "test-request",
			SessionId:   "test-session",
			UserId:      "test-user-id",
			TenantInfo:  &pb.TenantInfo{TenantId: tenantID, TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			userContext(tenantID, "REQUEST_RESTRICTED_R"),
			[]testutils.RequestResponse{
				{
					Request: &pb.RestrictedSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						TenantId: tenantID,
					},
					Response: &pb.RestrictedSearchResponse{
						Results: []*pb.RestrictedSearchResponse_Result{resultRow},
					},
				},
			},
			server.RestrictedSearch,
		)
	})
}

func TestResolveFilterGroup(t *testing.T) {
	server := mockRandomTestServer(t)
	testFilterGroup := func(
		filterGroup *pb.RestrictedSearchRequest_FilterGroup,
		expectedWhereClause string,
		expectedParams []bigquery.QueryParameter,
		errExpected bool,
	) {
		whereClause, params, err := server.resolveFilterGroup(filterGroup)
		if (err != nil) != errExpected {
			t.Errorf("Unexpected error: %v", err)
		}
		if whereClause != expectedWhereClause {
			t.Errorf("Expected where clause %v, got %v", expectedWhereClause, whereClause)
		}
		if !reflect.DeepEqual(params, expectedParams) {
			t.Errorf("Expected params %v, got %v", expectedParams, params)
		}
	}

	t.Run("no filters", func(t *testing.T) {
		testFilterGroup(nil, "", nil, false)
	})

	t.Run("invalid filters", func(t *testing.T) {
		invalidFilters := []*pb.RestrictedSearchRequest_FilterGroup{
			// Unknown/empty base filter.
			{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{},
						},
					},
				},
			},
			// No base filters.
			{
				Conjunction: pb.RestrictedSearchRequest_AND,
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_FilterGroup{
							FilterGroup: &pb.RestrictedSearchRequest_FilterGroup{},
						},
					},
				},
			},
		}
		for _, filter := range invalidFilters {
			testFilterGroup(filter, "", nil, true)
		}
	})

	t.Run("single filter", func(t *testing.T) {
		// It's hard to test every possible filter and query, but at least this test should be updated
		// whenever we add a new filter.
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_RequestId{
									RequestId: "test-request-id",
								},
							},
						},
					},
				},
			},
			"(request_metadata.request_id = @REQUEST_ID_uuid)",
			[]bigquery.QueryParameter{
				{Name: "REQUEST_ID_uuid", Value: "test-request-id"},
			},
			false,
		)
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_SessionId{
									SessionId: "test-session-id",
								},
							},
						},
					},
				},
			},
			"(request_metadata.session_id = @SESSION_ID_uuid)",
			[]bigquery.QueryParameter{
				{Name: "SESSION_ID_uuid", Value: "test-session-id"},
			},
			false,
		)
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_UserId{
									UserId: "test-user-id",
								},
							},
						},
					},
				},
			},
			"(request_metadata.user_id = @USER_ID_uuid)",
			[]bigquery.QueryParameter{
				{Name: "USER_ID_uuid", Value: "test-user-id"},
			},
			false,
		)
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_RequestType{
									RequestType: "test-request-type",
								},
							},
						},
					},
				},
			},
			"(request_metadata.request_type = @REQUEST_TYPE_uuid)",
			[]bigquery.QueryParameter{
				{Name: "REQUEST_TYPE_uuid", Value: "test-request-type"},
			},
			false,
		)
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_Accepted{
									Accepted: true,
								},
							},
						},
					},
				},
			},
			"(resolution.accepted = @ACCEPTED_uuid)",
			[]bigquery.QueryParameter{
				{Name: "ACCEPTED_uuid", Value: true},
			},
			false,
		)
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_ModelName{
									ModelName: "test-model-name",
								},
							},
						},
					},
				},
			},
			"(model.model_name = @MODEL_NAME_uuid)",
			[]bigquery.QueryParameter{
				{Name: "MODEL_NAME_uuid", Value: "test-model-name"},
			},
			false,
		)
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_HttpStatusCode{
									HttpStatusCode: 200,
								},
							},
						},
					},
				},
			},
			"(http_status.code = @HTTP_STATUS_CODE_uuid)",
			[]bigquery.QueryParameter{
				{Name: "HTTP_STATUS_CODE_uuid", Value: uint32(200)},
			},
			false,
		)
	})

	t.Run("simple and", func(t *testing.T) {
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Conjunction: pb.RestrictedSearchRequest_AND,
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_RequestId{
									RequestId: "test-request-id",
								},
							},
						},
					},
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_SessionId{
									SessionId: "test-session-id",
								},
							},
						},
					},
				},
			},
			"(request_metadata.request_id = @REQUEST_ID_uuid AND request_metadata.session_id = @SESSION_ID_uuid)",
			[]bigquery.QueryParameter{
				{Name: "REQUEST_ID_uuid", Value: "test-request-id"},
				{Name: "SESSION_ID_uuid", Value: "test-session-id"},
			},
			false,
		)
	})

	t.Run("simple or", func(t *testing.T) {
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Conjunction: pb.RestrictedSearchRequest_OR,
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_RequestId{
									RequestId: "test-request-id",
								},
							},
						},
					},
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_SessionId{
									SessionId: "test-session-id",
								},
							},
						},
					},
				},
			},
			"(request_metadata.request_id = @REQUEST_ID_uuid OR request_metadata.session_id = @SESSION_ID_uuid)",
			[]bigquery.QueryParameter{
				{Name: "REQUEST_ID_uuid", Value: "test-request-id"},
				{Name: "SESSION_ID_uuid", Value: "test-session-id"},
			},
			false,
		)
	})

	t.Run("nested groups", func(t *testing.T) {
		testFilterGroup(
			&pb.RestrictedSearchRequest_FilterGroup{
				Conjunction: pb.RestrictedSearchRequest_AND,
				Filters: []*pb.RestrictedSearchRequest_Filter{
					{
						Filter: &pb.RestrictedSearchRequest_Filter_FilterGroup{
							FilterGroup: &pb.RestrictedSearchRequest_FilterGroup{
								Conjunction: pb.RestrictedSearchRequest_OR,
								Filters: []*pb.RestrictedSearchRequest_Filter{
									{
										Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
											BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
												Filter: &pb.RestrictedSearchRequest_BaseFilter_RequestId{
													RequestId: "test-request-id",
												},
											},
										},
									},
									{
										Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
											BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
												Filter: &pb.RestrictedSearchRequest_BaseFilter_SessionId{
													SessionId: "test-session-id",
												},
											},
										},
									},
								},
							},
						},
					},
					{
						Filter: &pb.RestrictedSearchRequest_Filter_BaseFilter{
							BaseFilter: &pb.RestrictedSearchRequest_BaseFilter{
								Filter: &pb.RestrictedSearchRequest_BaseFilter_UserId{
									UserId: "test-user-id",
								},
							},
						},
					},
				},
			},
			"((request_metadata.request_id = @REQUEST_ID_uuid OR request_metadata.session_id = @SESSION_ID_uuid) AND request_metadata.user_id = @USER_ID_uuid)",
			[]bigquery.QueryParameter{
				{Name: "REQUEST_ID_uuid", Value: "test-request-id"},
				{Name: "SESSION_ID_uuid", Value: "test-session-id"},
				{Name: "USER_ID_uuid", Value: "test-user-id"},
			},
			false,
		)
	})
}

func TestGetRequestEvents(t *testing.T) {
	server := testServer(t)

	wrapGetRequestEvents := func(
		server *RestrictedServer,
	) func(context.Context, *pb.GetRequestEventsRequest) (*pb.GetRequestEventResponse, error) {
		return func(ctx context.Context, req *pb.GetRequestEventsRequest) (*pb.GetRequestEventResponse, error) {
			stream := &mockGetRequestEventsServer{ctx: ctx}
			err := server.GetRequestEvents(req, stream)
			if err != nil {
				return nil, err
			}
			if len(stream.responses) == 0 {
				return nil, status.Error(codes.NotFound, "No events found")
			}
			return stream.responses[0], nil
		}
	}

	t.Run("auth validation", func(t *testing.T) {
		runAuthTests(
			t,
			&pb.GetRequestEventsRequest{
				TenantId:  noDataTenantID,
				RequestId: "test-request-id",
			},
			wrapGetRequestEvents(server),
		)
	})

	t.Run("successful request", func(t *testing.T) {
		// Setup test metadata
		tenantID := uuid.New().String()
		requestId := uuid.New().String()
		eventId := uuid.New().String()

		// Mock request event data
		event := &riproto.RequestEvent{
			EventId: &eventId,
			Time:    timestamppb.New(mockTime),
			Event: &riproto.RequestEvent_CompletionResolution{
				CompletionResolution: &riproto.CompletionResolution{
					AcceptedIdx: 1,
				},
			},
		}
		marshaledEvent, err := proto.Marshal(event)
		if err != nil {
			t.Fatalf("Failed to marshal event: %v", err)
		}

		// Write event to GCS proxy
		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("request/%s/completion_resolution/%s", requestId, eventId),
			marshaledEvent,
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)

		// Create request
		req := &pb.GetRequestEventsRequest{
			TenantId:  tenantID,
			RequestId: requestId,
		}

		// Setup stream with authorized context
		stream := &mockGetRequestEventsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}

		// Execute request
		err = server.GetRequestEvents(req, stream)
		if err != nil {
			t.Fatalf("GetRequestEvents failed: %v", err)
		}

		// Verify results
		if len(stream.responses) != 1 {
			t.Fatalf("Expected 1 responses, got %d", len(stream.responses))
		}
		resp := stream.responses[0]

		if resp.RequestEvent.GetEventId() != eventId {
			t.Errorf("Expected event ID %s, got %s", eventId, resp.RequestEvent.GetEventId())
		}
		if resp.RequestEvent.Time.AsTime() != mockTime {
			t.Errorf("Expected time %v, got %v", mockTime, resp.RequestEvent.Time.AsTime())
		}
	})

	t.Run("event type filter", func(t *testing.T) {
		// Setup test metadata
		tenantID := uuid.New().String()
		requestId := uuid.New().String()
		nonMatchingEventId := uuid.New().String()
		matchingEventId := uuid.New().String()

		// Mock non-matching request event data
		event := &riproto.RequestEvent{
			EventId: &nonMatchingEventId,
			Time:    timestamppb.New(mockTime),
			Event: &riproto.RequestEvent_CompletionResolution{
				CompletionResolution: &riproto.CompletionResolution{
					AcceptedIdx: 1,
				},
			},
		}
		marshaledEvent, err := proto.Marshal(event)
		if err != nil {
			t.Fatalf("Failed to marshal event: %v", err)
		}

		// Write non-matching event to GCS proxy
		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("request/%s/completion_resolution/%s", requestId, nonMatchingEventId),
			marshaledEvent,
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)

		// Create request
		req := &pb.GetRequestEventsRequest{
			TenantId:  tenantID,
			RequestId: requestId,
			Filters: []*pb.GetRequestEventsRequest_Filter{
				{
					Filter: &pb.GetRequestEventsRequest_Filter_EventType{
						EventType: "completion_host_request",
					},
				},
			},
		}

		// Setup stream with authorized context
		stream := &mockGetRequestEventsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}

		// Execute request
		err = server.GetRequestEvents(req, stream)
		if err != nil {
			t.Fatalf("GetRequestEvents failed: %v", err)
		}

		// Verify results
		if len(stream.responses) != 0 {
			t.Fatalf("Expected 0 responses, got %d", len(stream.responses))
		}

		// Create and write matching event to GCS proxy
		event.EventId = &matchingEventId
		marshaledEvent, err = proto.Marshal(event)
		if err != nil {
			t.Fatalf("Failed to marshal event: %v", err)
		}

		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("request/%s/completion_host_request/%s", requestId, matchingEventId),
			marshaledEvent,
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)

		// Execute request again
		err = server.GetRequestEvents(req, stream)
		if err != nil {
			t.Fatalf("GetRequestEvents failed: %v", err)
		}

		// Verify results
		if len(stream.responses) != 1 {
			t.Fatalf("Expected 1 responses, got %d", len(stream.responses))
		}
		resp := stream.responses[0]
		if resp.RequestEvent.GetEventId() != matchingEventId {
			t.Errorf("Expected event ID %s, got %s", matchingEventId, resp.RequestEvent.GetEventId())
		}
	})

	t.Run("missing request ID", func(t *testing.T) {
		tenantID := uuid.New().String()

		req := &pb.GetRequestEventsRequest{
			TenantId: tenantID,
			// RequestId intentionally omitted
		}

		stream := &mockGetRequestEventsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}

		err := server.GetRequestEvents(req, stream)
		if err == nil || status.Code(err) != codes.InvalidArgument {
			t.Error("Expected InvalidArgument error for missing request ID")
		}
	})

	t.Run("missing tenant ID", func(t *testing.T) {
		req := &pb.GetRequestEventsRequest{
			// TenantId intentionally omitted
			RequestId: uuid.New().String(),
		}

		stream := &mockGetRequestEventsServer{
			ctx: userContext("test-tenant", "REQUEST_RESTRICTED_R"),
		}

		err := server.GetRequestEvents(req, stream)
		if err == nil || status.Code(err) != codes.InvalidArgument {
			t.Error("Expected InvalidArgument error for missing tenant ID")
		}
	})

	t.Run("unauthorized tenant", func(t *testing.T) {
		tenantID := uuid.New().String()
		requestId := uuid.New().String()
		wrongTenantID := uuid.New().String()

		req := &pb.GetRequestEventsRequest{
			TenantId:  tenantID,
			RequestId: requestId,
		}

		stream := &mockGetRequestEventsServer{
			ctx: userContext(wrongTenantID, "REQUEST_RESTRICTED_R"),
		}

		err := server.GetRequestEvents(req, stream)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Error("Expected PermissionDenied error for unauthorized tenant")
		}
	})

	t.Run("non-existent request", func(t *testing.T) {
		tenantID := uuid.New().String()

		req := &pb.GetRequestEventsRequest{
			TenantId:  tenantID,
			RequestId: "non-existent-request",
		}

		stream := &mockGetRequestEventsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}

		err := server.GetRequestEvents(req, stream)
		if err != nil {
			t.Errorf("Expected success with empty response for non-existent request, got error: %v", err)
		}

		if len(stream.responses) != 0 {
			t.Error("Expected no responses for non-existent request")
		}
	})
}

type mockGetRequestEventsServer struct {
	grpc.ServerStream
	ctx       context.Context
	responses []*pb.GetRequestEventResponse
}

func (m *mockGetRequestEventsServer) Context() context.Context {
	return m.ctx
}

func (m *mockGetRequestEventsServer) Send(resp *pb.GetRequestEventResponse) error {
	m.responses = append(m.responses, resp)
	return nil
}

// Mock implementation of the GetRemoteAgentLogs server stream
type mockGetRemoteAgentLogsServer struct {
	grpc.ServerStream
	ctx       context.Context
	responses []*pb.GetRemoteAgentLogsResponse
}

func (m *mockGetRemoteAgentLogsServer) Context() context.Context {
	return m.ctx
}

func (m *mockGetRemoteAgentLogsServer) Send(resp *pb.GetRemoteAgentLogsResponse) error {
	m.responses = append(m.responses, resp)
	return nil
}

// Helper function to add a mock remote agent log entry to BigQuery and GCS
func addRemoteAgentLogEntry(
	t *testing.T, agentID string, tenantID string, tenantName string, time time.Time, eventID string,
) {
	// Write a row to BigQuery
	writeRemoteAgentLogRow(t, agentID, tenantID, tenantName, time, eventID)

	// Create a mock remote agent log event
	logEntry := &riproto.RemoteAgentLogEntry{
		Message:   fmt.Sprintf("Test log message for agentID %s, eventID %s", agentID, eventID),
		Transport: "stdout",
		Timestamp: timestamppb.New(time),
	}
	remoteAgentLog := &riproto.RemoteAgentLog{
		Entries:        []*riproto.RemoteAgentLogEntry{logEntry},
		RemoteAgentId:  agentID,
		Component:      "test-component",
		StartTimestamp: timestamppb.New(time),
		EndTimestamp:   timestamppb.New(time),
	}
	sessionEvent := &riproto.SessionEvent{
		EventId: &eventID,
		Time:    timestamppb.New(time),
		Event: &riproto.SessionEvent_RemoteAgentLog{
			RemoteAgentLog: remoteAgentLog,
		},
	}
	marshaledEvent, err := proto.Marshal(sessionEvent)
	if err != nil {
		t.Fatalf("Failed to marshal event: %v", err)
	}

	// Write event to GCS proxy
	gcsProxyClient.WriteObject(
		context.Background(),
		nil,
		tenantID,
		fmt.Sprintf("session/%s/remote_agent_log/%s", agentID, eventID),
		marshaledEvent,
		gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
	)
}

// Helper for validating GetRemoteAgentLogs responses
func validateRemoteAgentsLogResponse(t *testing.T, responses []*pb.GetRemoteAgentLogsResponse, expectedAgentID string, expectedEventIDs []string) {
	if len(responses) != len(expectedEventIDs) {
		t.Fatalf("Expected %d response(s), got %d", len(expectedEventIDs), len(responses))
	}
	for i, resp := range responses {
		if resp.RemoteAgentLog.RemoteAgentId != expectedAgentID {
			t.Errorf("Expected agent ID %s, got %s", expectedAgentID, resp.RemoteAgentLog.RemoteAgentId)
		}
		if len(resp.RemoteAgentLog.Entries) != 1 {
			t.Errorf("Expected 1 log entry, got %d", len(resp.RemoteAgentLog.Entries))
		}
		expectedMessage := fmt.Sprintf("Test log message for agentID %s, eventID %s", expectedAgentID, expectedEventIDs[i])
		if resp.RemoteAgentLog.Entries[0].Message != expectedMessage {
			t.Errorf("Expected message '%s', got '%s'", expectedMessage, resp.RemoteAgentLog.Entries[0].Message)
		}
	}
}

func TestGetRemoteAgentLogs(t *testing.T) {
	server := testServer(t)

	wrapGetRemoteAgentLogs := func(
		server *RestrictedServer,
	) func(context.Context, *pb.GetRemoteAgentLogsRequest) (*pb.GetRemoteAgentLogsResponse, error) {
		return func(ctx context.Context, req *pb.GetRemoteAgentLogsRequest) (*pb.GetRemoteAgentLogsResponse, error) {
			stream := &mockGetRemoteAgentLogsServer{ctx: ctx}
			err := server.GetRemoteAgentLogs(req, stream)
			if err != nil {
				return nil, err
			}
			if len(stream.responses) == 0 {
				return nil, status.Error(codes.NotFound, "No logs found")
			}
			return stream.responses[0], nil
		}
	}

	t.Run("auth validation", func(t *testing.T) {
		runAuthTests(
			t,
			&pb.GetRemoteAgentLogsRequest{
				TenantId: noDataTenantID,
				AgentId:  "test-agent-id",
				TimeFilter: &pb.TimeFilter{
					StartTime: timestamppb.New(mockTime),
				},
			},
			wrapGetRemoteAgentLogs(server),
		)
	})

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetRemoteAgentLogsRequest{
			// Missing agent ID
			{
				TenantId: noDataTenantID,
				TimeFilter: &pb.TimeFilter{
					StartTime: timestamppb.New(mockTime),
				},
			},
			// Missing time filter
			{
				TenantId: noDataTenantID,
				AgentId:  "test-agent-id",
			},
			// Missing start time
			{
				TenantId: noDataTenantID,
				AgentId:  "test-agent-id",
				TimeFilter: &pb.TimeFilter{
					EndTime: timestamppb.New(mockTime),
				},
			},
		}

		for _, req := range badRequests {
			stream := &mockGetRemoteAgentLogsServer{
				ctx: userContext(noDataTenantID, "REQUEST_RESTRICTED_R"),
			}
			err := server.GetRemoteAgentLogs(req, stream)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected InvalidArgument error for request: %v", req)
			}
		}
	})

	t.Run("missing tenant ID", func(t *testing.T) {
		// The missing tenant ID case is handled by the auth check, which happens before
		// argument validation, so we need to test it separately
		req := &pb.GetRemoteAgentLogsRequest{
			AgentId: "test-agent-id",
			TimeFilter: &pb.TimeFilter{
				StartTime: timestamppb.New(mockTime),
			},
		}

		// Create a context with no auth claims
		stream := &mockGetRemoteAgentLogsServer{
			ctx: context.Background(),
		}

		err := server.GetRemoteAgentLogs(req, stream)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected PermissionDenied error for missing tenant ID, got: %v", err)
		}
	})

	t.Run("successful request", func(t *testing.T) {
		// Setup test metadata
		agentID := uuid.New().String()
		tenantID := uuid.New().String()
		tenantName := "test-tenant"
		eventIDs := []string{uuid.New().String(), uuid.New().String()}
		addRemoteAgentLogEntry(t, agentID, tenantID, tenantName, mockTime, eventIDs[0])
		// Add a second entry with an earlier timestamp to ensure correct ordering
		addRemoteAgentLogEntry(t, agentID, tenantID, tenantName, mockTime.Add(-time.Minute), eventIDs[1])

		// Create request
		req := &pb.GetRemoteAgentLogsRequest{
			TenantId:   tenantID,
			AgentId:    agentID,
			TimeFilter: &pb.TimeFilter{StartTime: timestamppb.New(mockTime.Add(-time.Hour))},
		}

		// Execute request
		stream := &mockGetRemoteAgentLogsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}
		err := server.GetRemoteAgentLogs(req, stream)
		if err != nil {
			t.Fatalf("GetRemoteAgentLogs failed: %v", err)
		}

		// Verify results
		validateRemoteAgentsLogResponse(t, stream.responses, agentID, eventIDs)
	})

	t.Run("time filter", func(t *testing.T) {
		// Setup test metadata
		agentID := uuid.New().String()
		tenantID := uuid.New().String()
		tenantName := "test-tenant"
		eventIDs := []string{uuid.New().String(), uuid.New().String()}
		addRemoteAgentLogEntry(t, agentID, tenantID, tenantName, mockTime, eventIDs[0])
		addRemoteAgentLogEntry(t, agentID, tenantID, tenantName, mockTime.Add(2*time.Hour), eventIDs[1])

		// Create request
		req := &pb.GetRemoteAgentLogsRequest{
			TenantId: tenantID,
			AgentId:  agentID,
			// Only include the first event
			TimeFilter: &pb.TimeFilter{
				StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
				EndTime:   timestamppb.New(mockTime.Add(time.Hour)),
			},
		}

		// Execute request
		stream := &mockGetRemoteAgentLogsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}
		err := server.GetRemoteAgentLogs(req, stream)
		if err != nil {
			t.Fatalf("GetRemoteAgentLogs failed: %v", err)
		}

		// Verify results
		validateRemoteAgentsLogResponse(t, stream.responses, agentID, eventIDs[0:1])
	})

	t.Run("max results", func(t *testing.T) {
		// Setup test metadata
		agentID := uuid.New().String()
		tenantID := uuid.New().String()
		tenantName := "test-tenant"
		eventIDs := []string{uuid.New().String(), uuid.New().String()}
		addRemoteAgentLogEntry(t, agentID, tenantID, tenantName, mockTime, eventIDs[0])
		addRemoteAgentLogEntry(t, agentID, tenantID, tenantName, mockTime.Add(-time.Minute), eventIDs[1])

		// Create request
		req := &pb.GetRemoteAgentLogsRequest{
			TenantId:   tenantID,
			AgentId:    agentID,
			TimeFilter: &pb.TimeFilter{StartTime: timestamppb.New(mockTime.Add(-time.Hour))},
			MaxResults: proto.Uint32(1),
		}

		// Execute request
		stream := &mockGetRemoteAgentLogsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}
		err := server.GetRemoteAgentLogs(req, stream)
		if err != nil {
			t.Fatalf("GetRemoteAgentLogs failed: %v", err)
		}

		// Verify results
		validateRemoteAgentsLogResponse(t, stream.responses, agentID, eventIDs[0:1])
	})

	t.Run("correct agent id", func(t *testing.T) {
		// Setup test metadata
		agentIDs := []string{uuid.New().String(), uuid.New().String()}
		tenantID := uuid.New().String()
		tenantName := "test-tenant"
		eventIDs := []string{uuid.New().String(), uuid.New().String()}
		addRemoteAgentLogEntry(t, agentIDs[0], tenantID, tenantName, mockTime, eventIDs[0])
		addRemoteAgentLogEntry(t, agentIDs[1], tenantID, tenantName, mockTime, eventIDs[1])

		// Create request
		req := &pb.GetRemoteAgentLogsRequest{
			TenantId:   tenantID,
			AgentId:    agentIDs[0],
			TimeFilter: &pb.TimeFilter{StartTime: timestamppb.New(mockTime.Add(-time.Hour))},
		}

		// Execute request
		stream := &mockGetRemoteAgentLogsServer{
			ctx: userContext(tenantID, "REQUEST_RESTRICTED_R"),
		}
		err := server.GetRemoteAgentLogs(req, stream)
		if err != nil {
			t.Fatalf("GetRemoteAgentLogs failed: %v", err)
		}

		// Verify results
		validateRemoteAgentsLogResponse(t, stream.responses, agentIDs[0], eventIDs[0:1])
	})
}

// Mock stream for DeleteGcsExportedBlobs testing
type mockDeleteGcsExportedBlobsServer struct {
	grpc.ServerStream
	ctx       context.Context
	requests  []*pb.DeleteGcsExportedBlobsRequest
	responses []*pb.DeleteGcsExportedBlobsResponse
	recvIndex int
}

func (m *mockDeleteGcsExportedBlobsServer) Send(resp *pb.DeleteGcsExportedBlobsResponse) error {
	m.responses = append(m.responses, resp)
	return nil
}

func (m *mockDeleteGcsExportedBlobsServer) Recv() (*pb.DeleteGcsExportedBlobsRequest, error) {
	if m.recvIndex >= len(m.requests) {
		return nil, io.EOF
	}
	req := m.requests[m.recvIndex]
	m.recvIndex++
	return req, nil
}

func (m *mockDeleteGcsExportedBlobsServer) Context() context.Context {
	return m.ctx
}

func TestDeleteGcsExportedBlobs(t *testing.T) {
	server := testServer(t)

	t.Run("insufficient scope - REQUEST_RESTRICTED_R denied", func(t *testing.T) {
		// Create streaming request
		req := &pb.DeleteGcsExportedBlobsRequest{
			TenantId: noDataTenantID,
			BlobName: "test-blob",
		}

		// Use context with REQUEST_RESTRICTED_R scope (should be denied)
		stream := &mockDeleteGcsExportedBlobsServer{
			ctx:      userContext(noDataTenantID, "REQUEST_RESTRICTED_R"),
			requests: []*pb.DeleteGcsExportedBlobsRequest{req},
		}

		err := server.DeleteGcsExportedBlobs(stream)
		if err != nil {
			t.Errorf("Expected no error (errors should be in response), got: %v", err)
		}

		if len(stream.responses) != 1 {
			t.Errorf("Expected 1 response, got %d", len(stream.responses))
		}

		// Check that response contains error message due to insufficient scope
		if stream.responses[0].ErrorMessage == "" {
			t.Errorf("Expected error message in response for insufficient scope")
		}
		if !strings.Contains(stream.responses[0].ErrorMessage, "unauthorized access to tenant") {
			t.Errorf("Expected 'unauthorized access to tenant' error, got: %s", stream.responses[0].ErrorMessage)
		}
	})

	t.Run("basic functionality - deletion", func(t *testing.T) {
		blobName := "test-blob-delete"
		blobPath := fmt.Sprintf("blobs/%s", blobName)
		testData := []byte("test blob content for deletion")

		// Write a test blob first
		err := gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			noDataTenantID,
			blobPath,
			testData,
			gcsproxyproto.BucketType_NON_ENTERPRISE_BLOBS,
		)
		if err != nil {
			t.Fatalf("Failed to write test blob: %v", err)
		}

		// Create streaming request for deletion
		req := &pb.DeleteGcsExportedBlobsRequest{
			TenantId: noDataTenantID,
			BlobName: blobName,
		}

		// Use context with REQUEST_ADMIN scope (should be allowed)
		stream := &mockDeleteGcsExportedBlobsServer{
			ctx:      userContext(noDataTenantID, "REQUEST_ADMIN"),
			requests: []*pb.DeleteGcsExportedBlobsRequest{req},
		}

		err = server.DeleteGcsExportedBlobs(stream)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}

		if len(stream.responses) != 1 {
			t.Errorf("Expected 1 response, got %d", len(stream.responses))
		}

		// Check that response does not contain error message
		if stream.responses[0].ErrorMessage != "" {
			t.Errorf("Expected no error message in response, got: %s", stream.responses[0].ErrorMessage)
		}

		// Blob should be found and deleted
		if !stream.responses[0].Found {
			t.Errorf("Expected blob to be found")
		}
		if !stream.responses[0].Deleted {
			t.Errorf("Expected blob to be deleted")
		}

		// Verify blob no longer exists using ObjectExist
		exists, err := gcsProxyClient.ObjectExist(
			context.Background(),
			nil,
			noDataTenantID,
			blobPath,
			gcsproxyproto.BucketType_NON_ENTERPRISE_BLOBS,
		)
		if err != nil {
			t.Errorf("Failed to check blob existence after deletion: %v", err)
		}
		if exists {
			t.Errorf("Expected blob %s to not exist after deletion", blobName)
		}
	})

	t.Run("arg validation", func(t *testing.T) {
		// Test missing tenant ID
		badReq1 := &pb.DeleteGcsExportedBlobsRequest{
			TenantId: "", // Missing tenant ID
			BlobName: "test-blob",
		}

		stream := &mockDeleteGcsExportedBlobsServer{
			ctx:      userContext(noDataTenantID, "REQUEST_ADMIN"),
			requests: []*pb.DeleteGcsExportedBlobsRequest{badReq1},
		}

		err := server.DeleteGcsExportedBlobs(stream)
		if err != nil {
			t.Errorf("Expected no error (errors should be in response), got: %v", err)
		}

		// Check that response contains error
		if len(stream.responses) == 0 || stream.responses[0].ErrorMessage == "" {
			t.Errorf("Expected error message in response for missing tenant ID")
		}

		// Test missing blob name
		badReq2 := &pb.DeleteGcsExportedBlobsRequest{
			TenantId: noDataTenantID,
			BlobName: "", // Missing blob name
		}

		stream2 := &mockDeleteGcsExportedBlobsServer{
			ctx:      userContext(noDataTenantID, "REQUEST_ADMIN"),
			requests: []*pb.DeleteGcsExportedBlobsRequest{badReq2},
		}

		err = server.DeleteGcsExportedBlobs(stream2)
		if err != nil {
			t.Errorf("Expected no error (errors should be in response), got: %v", err)
		}

		// Check that response contains error
		if len(stream2.responses) == 0 || stream2.responses[0].ErrorMessage == "" {
			t.Errorf("Expected error message in response for missing blob name")
		}
	})

	t.Run("multiple blobs - deletion", func(t *testing.T) {
		tenantID := uuid.New().String()
		blobNames := []string{"test-blob-delete-1", "test-blob-delete-2", "test-blob-delete-3"}

		// Write test blobs first
		for i, blobName := range blobNames {
			blobPath := fmt.Sprintf("blobs/%s", blobName)
			testData := []byte(fmt.Sprintf("test blob content for deletion %d", i+1))

			err := gcsProxyClient.WriteObject(
				context.Background(),
				nil,
				tenantID,
				blobPath,
				testData,
				gcsproxyproto.BucketType_NON_ENTERPRISE_BLOBS,
			)
			if err != nil {
				t.Fatalf("Failed to write test blob %s: %v", blobName, err)
			}
		}

		// Create streaming requests for deletion
		var requests []*pb.DeleteGcsExportedBlobsRequest
		for _, blobName := range blobNames {
			req := &pb.DeleteGcsExportedBlobsRequest{
				TenantId: tenantID,
				BlobName: blobName,
			}
			requests = append(requests, req)
		}

		stream := &mockDeleteGcsExportedBlobsServer{
			ctx:      userContext(tenantID, "REQUEST_ADMIN"),
			requests: requests,
		}

		err := server.DeleteGcsExportedBlobs(stream)
		if err != nil {
			t.Fatalf("DeleteGcsExportedBlobs failed: %v", err)
		}

		// Should have responses for each blob
		if len(stream.responses) != len(blobNames) {
			t.Errorf("Expected %d responses, got %d", len(blobNames), len(stream.responses))
		}

		// All responses should indicate found and deleted
		seenBlobs := make(map[string]bool)
		for _, resp := range stream.responses {
			seenBlobs[resp.BlobName] = true
			if resp.ErrorMessage != "" {
				t.Errorf("Expected no error message for blob %s, got: %s", resp.BlobName, resp.ErrorMessage)
			}
			if !resp.Found {
				t.Errorf("Expected blob %s to be found", resp.BlobName)
			}
			if !resp.Deleted {
				t.Errorf("Expected blob %s to be deleted", resp.BlobName)
			}
		}

		// Check that we got responses for all expected blob names
		for _, expectedBlob := range blobNames {
			if !seenBlobs[expectedBlob] {
				t.Errorf("Missing response for blob name %s", expectedBlob)
			}
		}

		// Verify all blobs no longer exist using ObjectExist
		for _, blobName := range blobNames {
			blobPath := fmt.Sprintf("blobs/%s", blobName)
			exists, err := gcsProxyClient.ObjectExist(
				context.Background(),
				nil,
				tenantID,
				blobPath,
				gcsproxyproto.BucketType_NON_ENTERPRISE_BLOBS,
			)
			if err != nil {
				t.Errorf("Failed to check blob existence after deletion for %s: %v", blobName, err)
			}
			if exists {
				t.Errorf("Expected blob %s to not exist after deletion", blobName)
			}
		}
	})
}

func TestDeleteEventsForUser(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server := testServer(t)

		// No claims
		_, err := server.DeleteEventsForUser(context.Background(), &pb.DeleteEventsForUserRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Wrong scope
		_, err = server.DeleteEventsForUser(iapContext("REQUEST_RESTRICTED_RW"), &pb.DeleteEventsForUserRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("end time validation", func(t *testing.T) {
		server := testServer(t)

		// Missing end_time
		_, err := server.DeleteEventsForUser(
			iapContext("REQUEST_ADMIN"),
			&pb.DeleteEventsForUserRequest{
				UserId: "test-user",
			},
		)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))

		// end_time too recent
		_, err = server.DeleteEventsForUser(
			iapContext("REQUEST_ADMIN"),
			&pb.DeleteEventsForUserRequest{
				UserId:  "test-user",
				EndTime: timestamppb.New(mockTime.Add(-60 * time.Minute)),
			},
		)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("basic", func(t *testing.T) {
		server := testServer(t)
		tenantID := uuid.New().String()
		userID := uuid.New().String()
		opaqueUserID := uuid.New().String()
		requestID := uuid.New().String()
		sessionID := uuid.New().String()

		// Write test data for a single request and session event.
		writeMetadataRow(t, requestID, tenantID, "test-tenant", mockTime.Add(-100*time.Minute), "test-type", sessionID, "test-user-agent", userID, opaqueUserID)
		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("request/%s/request_metadata/%s", requestID, uuid.New().String()),
			[]byte("test data"),
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)
		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("session/%s/next_edit_session_event/%s", sessionID, uuid.New().String()),
			[]byte("test data"),
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)

		// Should do nothing with the wrong kind of ID
		req := &pb.DeleteEventsForUserRequest{
			UserId:  userID,
			EndTime: timestamppb.New(mockTime.Add(-90 * time.Minute)),
		}
		resp, err := server.DeleteEventsForUser(iapContext("REQUEST_ADMIN"), req)
		require.NoError(t, err)

		require.Equal(t, uint32(0), resp.NumSuccessfulEventDeletions)
		require.Equal(t, uint32(0), resp.NumFailedEventDeletions)
		require.Equal(t, pb.DeleteEventsForUserResponse_SUCCESS, resp.SearchDeletionStatus)

		// The real deletion request
		req = &pb.DeleteEventsForUserRequest{
			UserId:  opaqueUserID,
			EndTime: timestamppb.New(mockTime.Add(-90 * time.Minute)),
		}
		resp, err = server.DeleteEventsForUser(iapContext("REQUEST_ADMIN"), req)
		require.NoError(t, err)

		require.Equal(t, uint32(2), resp.NumSuccessfulEventDeletions)
		require.Equal(t, uint32(0), resp.NumFailedEventDeletions)
		require.Equal(t, pb.DeleteEventsForUserResponse_SUCCESS, resp.SearchDeletionStatus)
		// BigQuery emulator doesn't record statistics correctly, so we can't check the number of
		// deleted search rows here.

		// Verify that the GCS objects are deleted.
		exists, err := gcsProxyClient.ObjectExist(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("request/%s/request_metadata/%s", requestID, uuid.New().String()),
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)
		require.NoError(t, err)
		require.False(t, exists)
		exists, err = gcsProxyClient.ObjectExist(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("session/%s/next_edit_session_event/%s", sessionID, uuid.New().String()),
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)
		require.NoError(t, err)
		require.False(t, exists)

		// Verify the metadata row is deleted.
		query := bqEmulator.Client.Query(fmt.Sprintf(`
			SELECT * FROM %s.request_metadata WHERE request_id = @REQUEST_ID
		`, datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "REQUEST_ID", Value: requestID},
		}
		it, err := query.Read(context.Background())
		require.NoError(t, err)
		var results []bigquery.Value
		err = it.Next(&results)
		require.Equal(t, iterator.Done, err, "Expected no results, got %v", results)

		// Running the same deletion again should be a no-op.
		resp, err = server.DeleteEventsForUser(iapContext("REQUEST_ADMIN"), req)
		require.NoError(t, err)
		require.Equal(t, uint32(0), resp.NumSuccessfulEventDeletions)
		require.Equal(t, uint32(0), resp.NumFailedEventDeletions)
		require.Equal(t, pb.DeleteEventsForUserResponse_SUCCESS, resp.SearchDeletionStatus)
	})

	t.Run("time filters", func(t *testing.T) {
		server := testServer(t)
		tenantID := uuid.New().String()
		userID := uuid.New().String()
		opaqueUserID := uuid.New().String()
		requestID := uuid.New().String()
		sessionID := uuid.New().String()

		// Write test data for a single request event.
		writeMetadataRow(t, requestID, tenantID, "test-tenant", mockTime.Add(-100*time.Minute), "test-type", sessionID, "test-user-agent", userID, opaqueUserID)
		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("request/%s/request_metadata/%s", requestID, uuid.New().String()),
			[]byte("test data"),
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)

		// Request time range before the event. Nothing should be deleted.
		req := &pb.DeleteEventsForUserRequest{
			UserId:    opaqueUserID,
			StartTime: timestamppb.New(mockTime.Add(-110 * time.Minute)),
			EndTime:   timestamppb.New(mockTime.Add(-105 * time.Minute)),
		}
		resp, err := server.DeleteEventsForUser(iapContext("REQUEST_ADMIN"), req)
		require.NoError(t, err)
		require.Equal(t, uint32(0), resp.NumSuccessfulEventDeletions)
		require.Equal(t, uint32(0), resp.NumFailedEventDeletions)
		require.Equal(t, pb.DeleteEventsForUserResponse_SUCCESS, resp.SearchDeletionStatus)

		// Request time range after the event. Nothing should be deleted.
		req = &pb.DeleteEventsForUserRequest{
			UserId:    opaqueUserID,
			StartTime: timestamppb.New(mockTime.Add(-95 * time.Minute)),
			EndTime:   timestamppb.New(mockTime.Add(-90 * time.Minute)),
		}
		resp, err = server.DeleteEventsForUser(iapContext("REQUEST_ADMIN"), req)
		require.NoError(t, err)
		require.Equal(t, uint32(0), resp.NumSuccessfulEventDeletions)
		require.Equal(t, uint32(0), resp.NumFailedEventDeletions)
		require.Equal(t, pb.DeleteEventsForUserResponse_SUCCESS, resp.SearchDeletionStatus)
	})
}

func TestDeleteEventsForTenant(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server := testServer(t)

		// No claims
		_, err := server.DeleteEventsForTenant(context.Background(), &pb.DeleteEventsForTenantRequest{
			TenantId: "test-tenant",
		})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Wrong scope
		_, err = server.DeleteEventsForTenant(
			iapContext("REQUEST_RESTRICTED_RW"), &pb.DeleteEventsForTenantRequest{
				TenantId: "test-tenant",
			})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("basic", func(t *testing.T) {
		server := testServer(t)
		tenantID := uuid.New().String()
		requestID := uuid.New().String()
		sessionID := uuid.New().String()

		// Write test data for a single request and session event.
		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("request/%s/request_metadata/%s", requestID, uuid.New().String()),
			[]byte("test data"),
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)
		gcsProxyClient.WriteObject(
			context.Background(),
			nil,
			tenantID,
			fmt.Sprintf("session/%s/next_edit_session_event/%s", sessionID, uuid.New().String()),
			[]byte("test data"),
			gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS,
		)

		_, err := server.DeleteEventsForTenant(iapContext("REQUEST_ADMIN"), &pb.DeleteEventsForTenantRequest{
			TenantId: uuid.New().String(),
		})
		require.NoError(t, err)

		_, err = server.DeleteEventsForTenant(iapContext("REQUEST_ADMIN"), &pb.DeleteEventsForTenantRequest{
			TenantId: tenantID,
		})
		require.NoError(t, err)
	})
}
