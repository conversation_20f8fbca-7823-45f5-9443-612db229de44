/*
 * Definition of flags and default values.
 *
 * These flags are not meant for configuration of stable
 * widely deployed features. They should
 * be removed when a feature is stable or when a feature
 * is no longer needed.
 *
 * Changing the default value of a flag should only be done
 * when the new value is ready to be used everywhere. Once
 * confidence is gained by running everywhere with the new
 * value, the flag should be removed.
 */
local flags_lib = import 'deploy/tenants/namespace_configs/flags-lib.jsonnet';

local flags = {
  withDeployFlag:: function(field, val) flags_lib.override(self, field, val),
  withFakeFeatureFlag:: function(field, val) self.withDeployFlag('extraFakeFeatureFlags', self.extraFakeFeatureFlags.withFakeFeatureFlag(field, val)),
  withDeployFlags:: function(overrides) std.foldl(function(acc, kv) acc.withDeployFlag(kv.key, kv.value), std.objectKeysValues(overrides), self),
  withFakeFeatureFlags:: function(overrides) self.withDeployFlag('extraFakeFeatureFlags', self.extraFakeFeatureFlags.withFakeFeatureFlags(overrides)),
  override:: self.withDeployFlag,  // TODO(mattm): finish off rename

  forceMtls: true,
  forceMtls_: {
    owner: 'costa',
    date: '2024/02/14',
    description: 'If true, force mTLS on in DEV. mTLS is always on in STAGING and PROD.',
  },

  exportFullData: false,
  exportFullData_: {
    owner: 'jacqueline',
    date: '2024/01/11',
    sensitive: true,
    description: |||
      If true, sensitive customer data (including code and user actions) will be exported to GCS
      buckets and BigQuery datasets used for model training.
      DO NOT ENABLE THIS WITHOUT EXPLICIT CONSENT FROM THE CUSTOMER!
    |||,
  },

  enableClientDataCollection: false,
  enableClientDataCollection_: {
    owner: 'luke',
    date: '2024/06/28',
    sensitive: true,
    description: |||
      If true, return a flag from api proxy /get-models that tells the client to configure
      enableDataCollection = true. DO NOT ENABLE THIS WITHOUT EXPLICIT CONSENT FROM THE CUSTOMER!
    |||,
  },

  exportAnnotations: {
    enabled: false,
    webhook_url: '',
    // A sealed secret also needs to be created to provide auth credentials for
    // this webhook URL and added to
    // services/request_insight/annotation_exporter/deploy.jsonnet.
    // Create one by following these steps:
    // * echo -n <secret> | kubectl -n <namespace> create secret generic annotation-exporter-webhook-auth --dry-run=client --from-file=credentials.json=/dev/stdin -o json > /tmp/secret.json
    // * bazel run //tools:kubeseal -- -f /tmp/secret.json -w <output.json> -n <namespace> --context=<dev or prod context>
    // For more background, see
    // https://www.notion.so/Runbook-How-to-create-a-sealed-secret-2bc2040cb1ca46b19f0a7b2a77610abb
  },
  exportAnnotations_: {
    owner: 'aswin',
    date: '2024/02/28',
    sensitive: true,
    description: |||
      If enabled, sensitive customer data (annotations) will be exported to
      the specified webhook link. This is meant for use by vendors.
      DO NOT ENABLE THIS FOR ANYONE EXCEPT VENDORS!
    |||,
  },

  sharedVendorBucket: {
    enabled: false,
    // These emails will have storage.objectAdmin privileges. Typically reserved for the
    // vendor POC to manage the bucket.
    // Providing vendors objectAdmin access is recommended by Google's docs:
    // https://cloud.google.com/storage/docs/collaboration#vendor
    adminEmails: [],
    // These emails will have storage.objectUser privileges. Typically meant for
    // engineers and the vendor's workers. This list is used to provide specific vendors
    // emails access to the bucket, but we expect the vendor POC to add their own email
    // addresses via the Google Cloud console.
    accessEmails: [],
  },

  sharedVendorBucket_: {
    owner: 'arun',
    date: '2024/01/12',
    sensitive: true,
    description: |||
      If enabled, create a shared GCS bucket for vendor data and allow read/write access
      to the specified email addresses.
      DO NOT ENABLE THIS WITHOUT EXPLICIT CONSENT FROM THE CUSTOMER!
    |||,
  },

  request_insight_event_retention_days: 14,
  request_insight_event_retention_days_: {
    owner: 'jacqueline',
    date: '2024/02/21',
    description: |||
      Number of days to retain event data in the request-insight-main table.
    |||,
  },

  testNamespace: '',
  testNamespace_: {
    owner: 'costa',
    date: '2024/10/29',
    description: |||
      If set, use this namespace as the prefix for the testing namespaces.
    |||,
  },

  useSharedDevRequestInsightBigquery: true,
  useSharedDevRequestInsightBigquery_: {
    owner: 'aswin',
    date: '2024/02/22',
    description: |||
      If true, use the shared request-insight-bigquery-dev dataset. Set to false
      to use personal bigquery tables in your dev deployment.
    |||,
  },

  iapJwtVerifierDisabled: false,
  iapJwtVerifierDisabled_: {
    owner: 'moogi',
    date: '2024/03/26',
    description: |||
      For DEV only, helps development and testing by disabling login requirements.
      NEVER switch on in production.
    |||,
  },

  passthroughBigtableProxy: true,
  passthroughBigtableProxy_: {
    owner: 'luke',
    date: '2024/05/29',
    description: |||
      If set, bigtable proxy will pass along requests to existing bigtable tables unmodified.
    |||,
  },

  loadBalancerType: 'ingress',
  loadBalancerType_: {
    owner: 'dirk',
    date: '2024/06/10',
    description: |||
      set to "ingress" to use ingresses for all external services
      set to null to not publish any external services
    |||,
  },

  useBigEmbeddingsSearch: false,
  useBigEmbeddingsSearch_: {
    owner: 'luke',
    date: '2024/08/01',
    description: |||
      Use a scaled-up version of embeddings search, to help us serve tenants with more load.
      Temporary band-aid only.
    |||,
  },

  skipTokenExchangePeerValidation: false,
  skipTokenExchangePeerValidation_: {
    owner: 'aswin',
    date: '2024/07/30',
    description: |||
      For the token_exchange integration test ONLY, skip peer validation.
    |||,
  },

  useFakeFeatureFlags: false,
  useFakeFeatureFlags_: {
    owner: 'dirk',
    date: '2024/09/12',
    description: |||
      Use fake feature flags instead of LaunchDarkly.
      This is intended for testing only.
    |||,
  },

  exposeInternalModelNames: false,
  exposeInternalModelNames_: {
    owner: 'ran',
    date: '2024/12/10',
    description: |||
      If true, the api proxy will send the internal model names to the client. If false, the model
      names will be hashed.
    |||,
  },

  slackSealedSecrets: {},
  slackSealedSecrets_: {
    owner: 'jacqueline',
    date: '2024/09/30',
    description: |||
      Override to use a different Slack app for the Slack bot. This is only valid in DEV. See
      https://www.notion.so/Setting-up-a-dev-Slack-app-111bba10175a804b81afcab1cc4e4a62.
    |||,
  },

  githubSealedSecrets: {},
  githubSealedSecrets_: {
    owner: 'surbhi',
    date: '2024/10/11',
    description: |||
      Override to use a different Github app for the Github processor the Slack bot uses. This is only valid in DEV. See
      https://www.notion.so/Setting-up-a-github-app-11cbba10175a8030af17de8f140724eb?pvs=4.
    |||,
  },

  githubPrivateKeyInfo: {},
  githubPrivateKeyInfo_: {
    owner: 'surbhi',
    date: '2024/10/11',
    description: |||
      Override to use a different Github private key for the Github app. This is only valid in DEV. See
      https://www.notion.so/Setting-up-a-github-app-11cbba10175a8030af17de8f140724eb?pvs=4.
    |||,
  },

  extraFakeFeatureFlags: {
    withFakeFeatureFlag:: function(field, val) flags_lib.override(self, field, val, allow_new_field=true),
    withFakeFeatureFlags:: function(overrides) std.foldl(function(acc, kv) acc.withFakeFeatureFlag(kv.key, kv.value), std.objectKeysValues(overrides), self),
  },
  extraFakeFeatureFlags_: {
    owner: 'dirk',
    date: '2024/09/12',
    description: |||
      Extra fake feature flags in dev environments. This is intended for testing only.
    |||,
  },

  migratedTenantIds: [],
  migratedTenantIds_: {
    owner: 'costa',
    date: '2024/11/25',
    description: |||
      List of tenant IDs that have been migrated away from this namespace.
    |||,
  },

  embeddingsSearchReplicasOverride: 0,
  embeddingsSearchReplicasOverride_: {
    owner: 'luke',
    date: '2024/10/02',
    description: |||
      If set, overrides the number of replicas for the embeddings-search-cpu service to increase
      the amount of simultaneous CPU work we can handle. Will not be respected if the value is
      less than the default (2 for legacy namespaces, 4 for shard namespaces).
    |||,
  },

  bigtableProxyMinReplicas: 1,
  bigtableProxyMinReplicas_: {
    owner: 'dirk',
    date: '2025/04/29',
    description: |||
      If set, overrides the minimum number of replicas for the bigtable-proxy service to increase
      the amount of simultaneous CPU work we can handle.
    |||,
  },

  bigtableProxyMaxReplicas: 4,
  bigtableProxyMaxReplicas_: {
    owner: 'dirk',
    date: '2025/04/29',
    description: |||
      If set, overrides the maximum number of replicas for the bigtable-proxy service to increase
      the amount of simultaneous CPU work we can handle.
    |||,
  },

  apiProxyReplicasOverride: 0,
  apiProxyReplicasOverride_: {
    owner: 'jhu',
    date: '2025/08/26',
    description: |||
      If set to non-zero value, overrides the number of replicas for the api-proxy. Otherwise,
      the default policy for replicas is used.
    |||,
  },

  useFakeSlack: false,
  useFakeSlack_: {
    owner: 'dirk',
    date: '2024/09/12',
    description: |||
      Use fake slack instead of the real slack.
      This is intended for testing only.
    |||,
  },

  enableApiProxyGrpcDebugEndpoint: false,
  enableApiProxyGrpcDebugEndpoint_: {
    owner: 'dirk',
    date: '2024/09/12',
    description: |||
      Enable the gRPC debug via api-proxy
    |||,
  },

  embeddingsSearchEndpoint: 'embeddings-search-cpu-svc:50051',
  embeddingsSearchEndpoint_: {
    owner: 'dirk',
    date: '2024/11/26',
    description: |||
      Override the embeddings search endpoint.
    |||,
  },

  workingSetEndpoint: '',  // disabled by default
  workingSetEndpoint_: {
    owner: 'dirk',
    date: '2025/01/24',
    description: |||
      Override the working set endpoint.
    |||,
  },

  workingSetPublishIndexMetrics: false,
  workingSetPublishIndexMetrics_: {
    owner: 'maxhahn',
    date: '2025/03/07',
    description: |||
      Enable the index/unindexed blob count metrics derived from workingset
      activity. This consumes extra memory and places additional load on the content
      manager service.
    |||,
  },

  stripeWebhookSecretOverride: {},
  stripeWebhookSecretOverride_: {
    owner: 'siyao',
    date: '2025/04/01',
    description: |||
      Override for the Stripe webhook signing secret version in DEV environments.
      This allows testing with different webhook secret versions.
    |||,
  },

  deployGlean: true,
  deployGlean_: {
    owner: 'surbhi',
    date: '2025/02/03',
    description: |||
      Deploy the glean integration to the namespace. Glean won't reply in slack, though, unless enable_glean feature flag is also enabled.
    |||,
  },

  deployCommitRetrieval: true,
  deployCommitRetrieval_: {
    owner: 'arun',
    date: '2025/03/31',
    description: |||
      Deploy commit retrieval functionality to the namespace. When enabled, the agents service will be configured with commit retrieval capabilities for git history search.
    |||,
  },

  userTier: 'unknown',
  userTier_: {
    owner: 'dirk',
    date: '2025/02/24',
    description: |||
      The user tier for the namespace. This will be returned in the GetModelsResponse.
    |||,
  },

  githubOauthSecretOverride: {},
  githubOauthSecretOverride_: {
    owner: 'aswin',
    date: '2025/02/26',
    description: |||
      For dev deploys, use a custom secret and version to get the client ID and
      client secret for the Github OAuth app, rather than the default secret.
      For example, {
        name: dev-aswin-github-processor-oauth-app-secret
        version: 'latest'
      }
    |||,
  },

  embeddingsSearchPartitions: 0,
  embeddingsSearchPartitions_: {
    owner: 'luke',
    date: '2025/02/27',
    description: |||
      The number of partitions to set up for the embeddings search service. If set to 0, no partitions will be added (only the normal un-partitioned service).
    |||,
  },

  enablePartitionedEmbeddingsSearch: false,
  enablePartitionedEmbeddingsSearch_: {
    owner: 'luke',
    date: '2025/02/27',
    description: |||
      If true, the embeddings search client will be configured to use the partitioned embeddings search service.
    |||,
  },

  remoteAgentImageTag: '',
  remoteAgentImageTag_: {
    owner: 'mattm',
    date: '2025/04/08',
    description: |||
      By default, the augment-remote-agent image is scoped to the environment with
      the tags :PROD, :STAGING, and :DEV. This flag allows changing the tag
      to an arbitrary string.

      There is also a special value `{namespace}`, which is used to change the scope
      of the image to the current namespace. To use this, you must push your own image.
      See services/remote_agents/img/README.md for details. This special value is the default
      for DEV namespaces.
    |||,
  },

  remoteAgentInnieImageDefault: '',
  remoteAgentInnieImageDefault_: {
    owner: 'mattm',
    date: '2025/08/21',
    description: |||
      The current defaults are calculated in deploy.jsonnet:
       - PROD:    reg.ws.augmentcode.com/augment/ubuntu:22.04-augment
       - STAGING: reg.ws.augmentcode.com/augment/ubuntu:22.04-STAGING (any env not PROD or DEV).
       - DEV:     reg.ws.augmentcode.com/augment/ubuntu:22.04-{namespace} (e.g., ubuntu:22.04-dev-mattm)
    |||,
  },

  remoteAgentWorkspacePoolGroups: ['general'],
  remoteAgentWorkspacePoolGroups_: {
    owner: 'mattm',
    date: '2025/05/05',
    description: |||
      Set 'raws.augmentcode.com/pool-group' toleration and selector labels. This can
      be used to isolate a namespace to its own nodepool.
    |||,
  },

  remoteAgentEnableInstructionFlags: false,
  remoteAgentEnableInstructionFlags_: {
    owner: 'mattm',
    date: '2025/04/18',
    description: |||
      Allow parsing '_flags:{key[=value],...}' from instructions. These enable experimental behavior
      on a per-workspace basis, and so are intended only for DEV and STAGING

      The flags can be read by the remote_agents service and beachhead binary. They are stripped by
      the beachhead before invoking the agent.
    |||,
  },

  tokenExchangeAclCheck: true,
  tokenExchangeAclCheck_: {
    owner: 'dirk',
    date: '2025/03/12',
    description: |||
      If true, the token exchange service will perform an ACL check when generating a service token for a tenant.
      Used to give namespace level access control in dev namespaces, e.g. grpc-debug

      NEVER SET THIS TO FALSE IN PRODUCTION!
    |||,
  },

  linearOauthClientIdOverride: {},
  linearOauthClientIdOverride_: {
    owner: 'sophie',
    date: '2025/03/11',
    description: |||
      For dev deploys, use a custom secret and version to get the
      client ID for the linear OAuth app, rather than the default client ID.
      For example, {
        name: dev-sophie-linear-client-id
        version: 'latest'
      }
    |||,
  },

  linearOauthClientSecretOverride: {},
  linearOauthClientSecretOverride_: {
    owner: 'sophie',
    date: '2025/03/11',
    description: |||
      For dev deploys, use a custom secret and version to get the
      client secret for the Linear OAuth app, rather than the default client secret.
      For example, {
        name: dev-sophie-linear-client-secret
        version: 'latest'
      }
    |||,
  },

  supabaseOauthSecretOverride: {},
  supabaseOauthSecretOverride_: {
    owner: 'xiaolei',
    date: '2025/03/12',
    description: |||
      For dev deploys, use a custom secret and version to get the client ID and
      client secret for the Supabase OAuth app, rather than the default secret.
      For example, {
        name: supabase-oauth-secret-dev-xiaolei
        version: 'latest'
      }
    |||,
  },

  orbWebhookSecretOverride: {},
  orbWebhookSecretOverride_: {
    owner: 'xiaolei',
    date: '2025/04/04',
    description: |||
      Override for the Orb webhook signing secret version in DEV environments.
      This allows testing with different webhook secret versions.
    |||,
  },


  authQueryTokenCacheTtlSecs: 15 * 60,
  authQueryTokenCacheTtlSecs_: {
    owner: 'surbhi',
    date: '2025/04/17',
    description: |||
      The TTL for the token info cache in auth query. This is in seconds. This flag is supposed to be a temporary solution while we figure out the right TTL as we are adding subscription validation and do not have a pub sub queue to notify the auth query service of any changes to the user's subscription.
    |||,
    name: 'auth_query_token_cache_ttl_secs',
  },

  chatHostReplicaScale: 1.0,
  chatHostReplicaScale_: {
    owner: 'brandon',
    date: '2025/04/29',
    description: |||
      The replica scale for the chat host deployment. This is a multiplier on the number of replicas.
    |||,
    name: 'chat_host_replica_scale',
  },

  completionHostReplicaScale: 1.0,
  completionHostReplicaScale_: {
    owner: 'brandon',
    date: '2025/04/29',
    description: |||
      The replica scale for the completions host deployment. This is a multiplier on the number of replicas.
    |||,
    name: 'completions_replica_scale',
  },

  embeddingsSearchRamGbOverride: 0,
  embeddingsSearchRamGbOverride_: {
    owner: 'luke',
    date: '2025/06/12',
    description: |||
      The amount of RAM to allocate to the embeddings search service, in GB.
    |||,
    name: 'embeddings_search_ram_gb_override',
  },

  checkpointIndexerRamGbOverride: 0,
  checkpointIndexerRamGbOverride_: {
    owner: 'luke',
    date: '2025/06/13',
    description: |||
      The amount of RAM to allocate to the checkpoint indexer service, in GB.
    |||,
    name: 'checkpoint_indexer_ram_gb_override',
  },

  enableChatStreamPayloadTimeout: true,
  enableChatStreamPayloadTimeout_: {
    owner: 'brandon',
    date: '2025/06/23',
    description: |||
      If true, the chat stream endpoint will enforce a timeout on the payload and return 408
    |||,
    name: 'enable_chat_stream_payload_timeout',
  },

  supportServiceTokenExpiration: 1,  // hours,
  supportServiceTokenExpiration_: {
    owner: 'lawrence',
    date: '2025/07/01',
    description: |||
      The expiration time for support service tokens.
    |||,
    name: 'support_service_token_expiration',
  },

  memstoreMemoryLimit: '',
  memstoreMemoryLimit_: {
    owner: 'markp',
    date: '2025/07/28',
    description: |||
      Override for the memstore service main container memory limit.
      If set, overrides the default 128Mi memory limit.
      Should be specified in Kubernetes memory format (e.g., '256Mi', '512Mi', '1Gi').
    |||,
    name: 'memstore_memory_limit',
  },

  disableHealthCheck: false,
  disableHealthCheck_: {
    owner: 'luke',
    date: '2025/07/01',
    description: |||
      If true, the API proxy health check will be disabled. Use only for namespaces where we're migrating out the last tenant.
    |||,
    name: 'disable_health_check',
  },

  enableAgentsWebApp: false,
  enableAgentsWebApp_: {
    owner: 'jared',
    date: '2025/07/14',
    description: |||
      If true, the agents web app will be enabled.
    |||,
    name: 'enable_agents_web_app',
  },

  enableBillingPlanValidationAndCreation: false,
  enableBillingPlanValidationAndCreation_: {
    owner: 'bin',
    date: '2025/07/22',
    description: |||
      If true, the billing central service will validate and create billing plans in Orb.
      When enabled in DEV environment, plan IDs, external_plan_ids, and names will be
      appended with the dev namespace to separate dev environments.
    |||,
  },

  useOutboundProxy: false,
  useOutboundProxy_: {
    owner: 'marcmac',
    date: '2025/08/12',
    description: |||
      If true, external HTTP requests (e.g., to GitHub API) will be routed through
      the outbound web proxy for security and compliance. Defaults to false for all
      environments but can be overridden per environment or namespace as needed.
    |||,
  },

  outboundProxyMode: 'fallback',
  outboundProxyMode_: {
    owner: 'marcmac',
    date: '2025/09/09',
    description: |||
      Outbound proxy behavior: 'require' to fail closed if proxy is unavailable,
      or 'fallback' to attempt proxy first and fall back to direct egress.
      Used by deploy/common/outbound-proxy-lib.jsonnet.
    |||,
    name: 'outbound_proxy_mode',
  },


  enforcePIIPolicyTags: true,
  enforcePIIPolicyTags_: {
    owner: 'jacqueline',
    date: '2025/07/15',
    description: |||
      If true, policy tags are applied to BigQuery tables. This should only be disabled in dev.
    |||,
    name: 'enforce_pii_policy_tags',
  },

  enableHeadlessTokenExchangeClient: false,
  enableHeadlessTokenExchangeClient_: {
    owner: 'aswin',
    date: '2025/07/25',
    description: |||
      If true, token exchange clients will be configured to use the headless
      service. For this to take effect on a service, it requires updating its
      jsonnet to use this when calling endpointsLib.getTokenExchangeGrpcUrl
    |||,
    name: 'enable_headless_token_exchange_client',
  },

  deployTenantSpannerDatabase: true,
  deployTenantSpannerDatabase_: {
    owner: 'jacqueline',
    date: '2025/08/01',
    description: |||
      An easy off switch for deploying the tenant spanner database if something goes wrong.
      This is temporary.
    |||,
    name: 'deploy_tenant_spanner_database',
  },

  useJemallocEmbeddingsSearch: false,
  useJemallocEmbeddingsSearch_: {
    owner: 'siyao',
    date: '2025/08/18',
    description: |||
      If true, use jemalloc build for embeddings search CPU server.
    |||,
  },

  useHeadlessBigTableProxy: false,
  useHeadlessBigTableProxy_: {
    owner: 'lawrence',
    date: '2025/08/15',
    description: |||
      If true, clients will be configured to use the headless bigtable proxy service.
    |||,
    name: 'use_headless_bigtable_proxy',
  },

  useHeadlessContentManager: false,
  useHeadlessContentManager_: {
    owner: 'lawrence',
    date: '2025/08/15',
    description: |||
      If true, clients will be configured to use the headless content manager service.
    |||,
    name: 'use_headless_content_manager',
  },

  contentManagerMinReplicas: 1,
  contentManagerMinReplicas_: {
    owner: 'lawrence',
    date: '2025/08/19',
    description: |||
      If set, overrides the minimum number of replicas for the content manager service.
    |||,
    name: 'content_manager_min_replicas',
  },
  contentManagerMaxReplicas: 4,
  contentManagerMaxReplicas_: {
    owner: 'lawrence',
    date: '2025/08/19',
    description: |||
      If set, overrides the maximum number of replicas for the content manager service.
    |||,
    name: 'content_manager_max_replicas',
  },

  billingWebhookNamespace: '',
  billingWebhookNamespace_: {
    owner: 'dirk',
    date: '2025/09/01',
    description: |||
      The namespace to use for the billing and stripe webhook. If null, use the current namespace.
      This is useful for testing in dev. Useful values are '' or 'central-dev'. If 'central-dev' is used, then
      the shared webhooks are used.
    |||,
    name: 'billing_webhook_namespace',
  },

  contentManagerGcInterval: 0,
  contentManagerGcInterval_: {
    owner: 'luke',
    date: '2025/08/22',
    description: |||
      The interval in seconds between content manager GC jobs. Set to 0 to disable.
    |||,
    name: 'content_manager_gc_interval',
  },

  useManagedRedisMemstore: false,
  useManagedRedisMemstore_: {
    owner: 'luke',
    date: '2025/08/22',
    description: |||
      If true, use a managed Redis instance for memstore instead of a local instance.
    |||,
    name: 'use_managed_redis_memstore',
  },

  abandonIngress: false,
  abandonIngress_: {
    owner: 'aswin',
    date: '2025/08/12',
    description: |||
      If true, set "eng.augmentcode.com/deletion-policy" = "abandon" on any
      ingresses.
    |||,
  },

  useMockDeploymentExecutor: false,
  useMockDeploymentExecutor_: {
    owner: 'lawrence',
    date: '2025/08/26',
    description: |||
      If true, use a mock deployment executor instead of the real one. This is intended for testing only.
    |||,
    name: 'use_mock_deployment_executor',
  },

  enableSettingsWebApp: false,
  enableSettingsWebApp_: {
    owner: 'jared',
    date: '2025/09/04',
    description: |||
      If true, the settings web app will be enabled in customer UI at /settings.
    |||,
    name: 'enable_settings_web_app',
  },
};

local errors = flags_lib.check_flags(flags);

assert std.length(errors) == 0 : 'Errors: ' + std.toString(errors);

flags
