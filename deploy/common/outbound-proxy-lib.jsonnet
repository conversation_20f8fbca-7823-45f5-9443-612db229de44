// Shared helper to configure outbound proxy and mounts for processors
// Usage:
//   local outboundProxy = outboundProxyLib.configure(env=env, cloud=cloud, namespace=namespace, appName=appName, namespace_config=namespace_config)
//   config.outbound_proxy = outboundProxy.config
//   append outboundProxy.volumeMountDef and outboundProxy.podVolumeDef when not null

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';

{
  // Returns { config, volumeMountDef, podVolumeDef }
  configure(env, cloud, namespace, appName, namespace_config)::
    local useProxy = std.get(namespace_config.flags, 'useOutboundProxy', false);
    local svc = if env == 'DEV' then 'outbound-proxy.devtools.svc.cluster.local:443'
    else if cloudInfo.isEuCluster(cloud) then 'outbound-proxy-eu.proxy.svc.cluster.local:443'
    else 'outbound-proxy.proxy.svc.cluster.local:443';

    if useProxy then {
      // App/service config
      config: {
        enabled: true,
        service_name: svc,
        skip_cert_verification: false,
        ca_cert_path: '/outbound-proxy-ca/ca.crt',
        mode: std.get(namespace_config.flags, 'outboundProxyMode', 'fallback'),
      },

      // K8s mounts
      volumeMountDef: {
        name: 'outbound-proxy-ca',
        mountPath: '/outbound-proxy-ca',
        readOnly: true,
      },
      podVolumeDef: {
        name: 'outbound-proxy-ca',
        secret: { secretName: 'outbound-proxy-ca', optional: false },  // pragma: allowlist secret
      },
    } else {
      config: { enabled: false },
      volumeMountDef: null,
      podVolumeDef: null,
    },
}
