#!/usr/bin/env bash
set -euo pipefail

# Bazel runfiles setup for locating data deps (kubectl, kubeconfig)
if [[ -z "${RUNFILES_DIR:-}" ]]; then
  # shellcheck disable=SC1090,SC1091
  if ! source "$0.runfiles/bazel_tools/tools/bash/runfiles/runfiles.bash" 2>/dev/null; then
    echo "ERROR: runfiles.bash not found; run via bazel or set RUNFILES_DIR" >&2
  fi
fi
rlocation_or_die() { local p="$1"; local f; f="$(rlocation "$p" 2>/dev/null || true)"; [[ -n "$f" && -e "$f" ]] || { echo "Missing runfile: $p" >&2; exit 1; }; printf "%s" "$f"; }
# Prefer hermetic kubectl and kubeconfig from runfiles if available
KUBECTL_BIN="${KUBECTL_BIN:-}"
KUBECONFIG_FILE="${KUBECONFIG_FILE:-}"
if [[ -z "$KUBECTL_BIN" ]] && type rlocation >/dev/null 2>&1; then
  KUBECTL_BIN="$(rlocation_or_die k8s_binary/file/kubectl)"
fi
if [[ -z "$KUBECONFIG_FILE" ]] && type rlocation >/dev/null 2>&1; then
  # rlocation requires repo-prefixed paths; use TEST_WORKSPACE for same-repo files
  WS_PREFIX="${TEST_WORKSPACE:-}"
  if [[ -n "$WS_PREFIX" ]]; then
    KUBECONFIG_FILE="$(rlocation_or_die "$WS_PREFIX/tools/deploy/auth_kube_config.yaml")"
  fi
fi
KUBECTL_CMD=("${KUBECTL_BIN:-kubectl}")
if [[ -n "$KUBECONFIG_FILE" ]]; then
  KUBECTL_CMD+=("--kubeconfig" "$KUBECONFIG_FILE")
fi
# Make GKE auth plugin discoverable to kubectl via explicit env var (no PATH changes)
if type rlocation >/dev/null 2>&1; then
  for p in \
    gke-gcloud-auth-plugin/gke-gcloud-auth-plugin \
    gke-gcloud-auth-plugin/file/gke-gcloud-auth-plugin; do
    f="$(rlocation "$p" 2>/dev/null || true)"
    if [[ -n "$f" && -x "$f" ]]; then
      export KUBECTL_GKE_GCLOUD_AUTH_PLUGIN_EXECUTABLE="$f"
      break
    fi
  done
fi


# Sync the outbound proxy issuer CA (ca.crt) into namespaces that deploy the GitHub Processor
# Creates/updates a Secret named "outbound-proxy-ca" with key ca.crt
#
# Usage:
#   sync-proxy-ca <region> [apps] [namespaces] [--dry-run]
#     region: dev | us | eu
#     apps: optional, space-separated list to restrict discovery (env APPS also supported)
#     namespaces: optional, space- or comma-separated explicit namespaces to target (env TARGET_NAMESPACES also supported)
#     --dry-run: validate and print changes without applying them (uses server-side dry-run)
#
# Source of truth (by region):
#   dev: namespace=devtools, secret=outbound-proxy-server-cert
#   us : namespace=proxy   , secret=outbound-proxy-server-cert-prod
#   eu : namespace=proxy   , secret=outbound-proxy-server-cert-prod-eu
#
# Target namespaces are discovered by finding Deployments matching a known app list
# in the cluster corresponding to the region. Default apps: github-processor, notion, linear, supabase, atlassian

region="${1:-}"
if [[ -z "${region}" ]]; then
  echo "Usage: $0 <region>   (region: dev|us|eu)" >&2
  exit 2
fi

case "${region}" in
  dev)
    CTX="gke_system-services-dev_us-central1_us-central1-dev"
    SRC_NS="devtools"
    SRC_SECRET="outbound-proxy-server-cert"  # pragma: allowlist secret
    ;;
  us)
    CTX="gke_system-services-prod_us-central1_us-central1-prod"
    SRC_NS="proxy"
    SRC_SECRET="outbound-proxy-server-cert-prod"  # pragma: allowlist secret
    ;;
  eu)
    CTX="gke_system-services-prod_europe-west4_eu-west4-prod"
    SRC_NS="proxy"
    SRC_SECRET="outbound-proxy-server-cert-prod-eu"  # pragma: allowlist secret
    ;;
  *)
    echo "Unknown region: ${region} (expected dev|us|eu)" >&2
    exit 2
    ;;
esac

info() { printf "[sync-proxy-ca][%s] %s\n" "${region}" "$*"; }
run() { info "+ $*"; "$@"; }

TMPDIR="$(mktemp -d)"
trap 'rm -rf "${TMPDIR}"' EXIT

# Fetch issuer CA from source secret
info "Fetching CA from ${SRC_NS}/${SRC_SECRET}"
CA_B64="${TMPDIR}/ca.crt.b64"
CA_PEM="${TMPDIR}/ca.crt"
GC_AUTH=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
info "Using gcloud auth: ${GC_AUTH}"
info "Fetching cert with ${KUBECTL_CMD[@]} --context='${CTX}' -n '${SRC_NS}' get secret '${SRC_SECRET}' -o jsonpath='{.data.ca\.crt}'"
"${KUBECTL_CMD[@]}" --context="${CTX}" -n "${SRC_NS}" get secret "${SRC_SECRET}" -o jsonpath='{.data.ca\.crt}' >"${CA_B64}" 2>${TMPDIR}/err.txt
rc=$?
if [[ -s ${TMPDIR}/err.txt ]]; then
  info "Cert fetch produced errors: $(<${TMPDIR}/err.txt)"
fi
if [[ ${rc} -ne 0 ]] || [[ ! -s "${CA_B64}" ]]; then
  echo "ERROR: Could not read ca.crt from ${SRC_NS}/${SRC_SECRET} in context ${CTX}" >&2
  exit 1
fi
base64 -d "${CA_B64}" >"${CA_PEM}"

# Parse flags after region (support --dry-run)
DRY_RUN_FLAG=""
for arg in "${@:2}"; do
  if [[ "$arg" == "--dry-run" ]]; then
    DRY_RUN_FLAG="--dry-run=server"
  fi
done

# Determine apps (space-separated) either from APPS env/arg or default set
APPS_INPUT=${APPS:-}
if [[ $# -ge 2 && ! "${2:-}" =~ ^- ]]; then
  APPS_INPUT="$2"
fi
if [[ -z "${APPS_INPUT}" ]]; then
  APPS_INPUT="github-processor notion linear supabase atlassian"
fi

# Optional explicit namespaces override discovery
RAW_NS_INPUT=${TARGET_NAMESPACES:-}
if [[ $# -ge 3 && ! "${3:-}" =~ ^- ]]; then
  RAW_NS_INPUT="$3"
fi
if [[ -n "${RAW_NS_INPUT}" ]]; then
  # support comma or space separated
  NS_LIST=$(echo "${RAW_NS_INPUT}" | tr ',' ' ')
  info "Using explicit target namespaces: ${NS_LIST}"
else
  info "Discovering namespaces with deployments for apps: ${APPS_INPUT}"
  NS_LIST=""
  for app in ${APPS_INPUT}; do
    found=$("${KUBECTL_CMD[@]}" --context="${CTX}" get deploy -A -o custom-columns=NS:.metadata.namespace,NAME:.metadata.name --no-headers | awk -v a="$app" '$2==a{print $1}' | sort -u)
    if [[ -n "${found}" ]]; then
      NS_LIST+="$found\n"
    fi
  done
  NS_LIST=$(echo -e "${NS_LIST}" | grep -v '^$' | sort -u)

  if [[ -z "${NS_LIST}" ]]; then
    info "No target deployments found in ${CTX}; nothing to do."
    exit 0
  fi
fi

# If dry-run, summarize planned namespaces
if [[ -n "${DRY_RUN_FLAG}" ]]; then
  info "Dry-run enabled. Namespaces to update:"
  # shellcheck disable=SC2001
  echo -e "${NS_LIST}" | sed '/^$/d' | sed 's/^/  - /'
fi

for ns in ${NS_LIST}; do
  if [[ -n "${DRY_RUN_FLAG}" ]]; then
    info "[dry-run] Validating outbound-proxy-ca in namespace: ${ns}"
  else
    info "Syncing outbound-proxy-ca into namespace: ${ns}"
  fi
  "${KUBECTL_CMD[@]}" --context="${CTX}" -n "${ns}" create secret generic outbound-proxy-ca \
    --from-file=ca.crt="${CA_PEM}" \
    --dry-run=client -o yaml | \
  "${KUBECTL_CMD[@]}" --context="${CTX}" -n "${ns}" apply -f - ${DRY_RUN_FLAG}

done

info "Done."
