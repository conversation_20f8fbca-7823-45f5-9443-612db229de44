// K8S deployment file for the deploy job
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local githubAppSealedLib = import 'tools/deploy/github_app_sealed.jsonnet';
function(cloud, env, namespace, namespace_config)
  local tolerations = nodeLib.tolerations(resource='prohibitGpu', env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource='prohibitGpu', env=env, cloud=cloud, appName=null);
  local appName = 'deploy-job';

  local clientCert = certLib.createClientCert(
    name='deploy-job-client-cert',
    namespace=namespace,
    appName=appName,
  );

  local githubSecret = githubAppSealedLib(cloud=cloud, namespace=namespace, appName=appName);
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local pdb = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
  // create a deployment job with the given name, track, schedule, and branch
  //
  // Remember to also update tools/deploy_runner/control/deploy.jsonnet for the volumes
  //
  // Args:
  //   name: the name of the job, e.g. 'default' or 'experimental'
  //   track: the deployment track to use, e.g. 'DEFAULT' or 'EXPERIMENTAL'
  //   schedule: the cron schedule to use, e.g. '0 8,10,12,14 * * 1,2,3,4,5' or '0,30 8-18 * * 1,2,3,4,5'
  //   branch: the branch to deploy, e.g. 'main' or 'bazel-last-known-good'. When in doubt, use 'bazel-last-known-good'
  local createDeploymentJob(name,
                            track,
                            schedule_name,
                            schedule,
                            branch,
                            pause_after_staging_minutes,
                            suspend=false) =
    local config = {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: 'deploy-%s-job-config' % name,
        namespace: namespace,
        annotations: {
          'reloader.stakater.com/match': 'true',
        },
        labels: {
          app: 'deploy-job',
        },
      },
      data: {
        'config.json': std.manifestJson({
          deploy_server: 'deploy-rpc-svc:50051',
          client_mtls: mtls,
          client_ca_path: '/client-certs/ca.crt',
          client_key_path: '/client-certs/tls.key',
          client_cert_path: '/client-certs/tls.crt',
          base_directory: '/cache',
          repo_owner: 'augmentcode',
          repo_name: 'augment',
          branch: branch,
          github_app_path: '/github-app',
          cloud_filter: if cloudInfo.isLeadProdCluster(cloud) then [] else [cloud],
          namespace_filter: [],
          interval_seconds: 30,
          pause_after_staging_minutes: pause_after_staging_minutes,
          deployment_track: track,
          deployment_schedule_name: schedule_name,
        }),
      },
    };
    local container =
      {
        name: 'deploy-job',
        target: {
          name: '//tools/deploy_runner/deploy_job:image',
          dst: 'deploy-job-image',
        },
        volumeMounts: [
          {
            name: 'config',
            mountPath: '/config',
            readOnly: true,
          },
          {
            name: 'cache-volume',
            mountPath: '/cache',
          },
          {
            mountPath: '/github-app',
            name: 'github-app-secret',
          },
          {
            mountPath: '/dev/shm',
            name: 'dshm',
          },
          clientCert.volumeMountDef,
        ],
        args: [
          '--config',
          '/config/config.json',
        ],
        env: [
          {
            name: 'POD_NAME',
            valueFrom: {
              fieldRef: {
                fieldPath: 'metadata.name',
              },
            },
          },
          {
            name: 'POD_NAMESPACE',
            valueFrom: {
              fieldRef: {
                fieldPath: 'metadata.namespace',
              },
            },
          },
        ],
        resources: {
          limits: {
            cpu: 1,
            memory: '1Gi',
          },
        },
      };
    local pod =
      {
        restartPolicy: 'Never',
        containers: [
          container,
        ],
        tolerations: tolerations,
        affinity: affinity,
        priorityClassName: cloudInfo.envToPriorityClass(env),
        securityContext: {
          runAsUser: 1000,
          fsGroup: 1000,
          fsGroupChangePolicy: 'OnRootMismatch',
        },
        volumes: [
          // add extra large shared memory
          {
            name: 'dshm',
            emptyDir: {
              medium: 'Memory',
              sizeLimit: '2Gi',
            },
          },
          {
            name: 'cache-volume',
            emptyDir: {
              sizeLimit: '10Gi',
            },
          },
          {
            name: 'config',
            configMap: {
              name: config.metadata.name,
            },
          },
          clientCert.podVolumeDef,
          {
            name: 'github-app-secret',
            secret: {
              secretName: githubSecret.metadata.name,  // pragma: allowlist secret
              optional: false,
            },
          },
        ],
      };

    local cronjob =
      {
        apiVersion: 'batch/v1',
        kind: 'CronJob',
        metadata: {
          name: if name == 'default' then 'deploy' else 'deploy-%s' % name,
          namespace: namespace,
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/search': 'true',
          },
        },
        spec: {
          suspend: env != 'PROD' || suspend,
          schedule: schedule,
          // Don't start deployments that were skipped when this job was suspended. Set to 2 minutes
          // to give the scheduler some slack.
          startingDeadlineSeconds: 120,
          successfulJobsHistoryLimit: 16,
          failedJobsHistoryLimit: 4,
          concurrencyPolicy: 'Forbid',
          timeZone: 'America/Los_Angeles',
          jobTemplate: {
            metadata: {
              labels: {
                app: 'deploy',
              },
            },
            spec: {
              // disable backoff
              backoffLimit: 0,
              template: {
                metadata: {
                  labels: {
                    app: appName,
                  },
                },
                spec: pod,
              },
            },
          },
        },
      };
    lib.flatten([
      config,
      cronjob,
    ]);
  lib.flatten([
    githubSecret,
    clientCert.objects,
    pdb,
    // default job track with a schedule of minute 0; hours 8,9,10,11,12,13,14; every day of the month; every weekday
    createDeploymentJob(name='default',
                        track='DEFAULT',
                        schedule_name='DEFAULT',
                        schedule='0 8,9,10,11,12,13,14 * * 1,2,3,4,5',
                        pause_after_staging_minutes=40,
                        branch='bazel-last-known-good'),
    // experimental job track with a schedule of minute 0,30; hours 8-18; every day of the month; every weekday
    createDeploymentJob(name='experimental',
                        track='EXPERIMENTAL',
                        schedule_name='EXPERIMENTAL',
                        schedule='0,30 8-18 * * 1,2,3,4,5',
                        branch='main',
                        pause_after_staging_minutes=0),
    // infer and embedder job track with a schedule of minute 0; hours 8,14; every day of the month; every weekday
    createDeploymentJob(name='infer-and-embedder',
                        track='INFER_AND_EMBEDDER',
                        schedule_name='INFER_AND_EMBEDDER',
                        schedule='0 8,14 * * 1,2,3,4,5',
                        branch='bazel-last-known-good',
                        pause_after_staging_minutes=40),
    // tombstone job track with a schedule of minute 0; hours 10; every day of the month; every weekday
    createDeploymentJob(name='tombstone',
                        track='TOMBSTONE',
                        schedule_name='TOMBSTONE',
                        schedule='0 10 * * 1,2,3,4,5',
                        branch='bazel-last-known-good',
                        pause_after_staging_minutes=0),
    // CICD job track with the same schedule as default
    createDeploymentJob(name='cicd',
                        track='CICD',
                        schedule_name='CICD',
                        schedule='0 8,9,10,11,12,13,14 * * 1,2,3,4,5',
                        branch='bazel-last-known-good',
                        pause_after_staging_minutes=0),
    // FOUNDATION job track with the same schedule as default
    createDeploymentJob(name='foundation',
                        track='FOUNDATION',
                        schedule_name='FOUNDATION',
                        schedule='0,30 8,9,10,11,12,13,14,15,16 * * 1,2,3,4,5',
                        branch='main',
                        pause_after_staging_minutes=0),
    // TEAM_BAM job track with the same schedule as default
    createDeploymentJob(name='team-bam',
                        track='TEAM_BAM',
                        schedule_name='TEAM_BAM',
                        schedule='0 8,9,10,11,12,13,14 * * 1,2,3,4,5',
                        pause_after_staging_minutes=40,
                        branch='bazel-last-known-good'),
  ])
